<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>nav2_behavior_tree</name>
  <version>1.3.1</version>
  <description>Nav2 behavior tree wrappers, nodes, and utilities</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>action_msgs</depend>
  <depend>behaviortree_cpp</depend>
  <depend>geometry_msgs</depend>
  <depend>nav2_common</depend>
  <depend>nav2_msgs</depend>
  <depend>nav2_util</depend>
  <depend>nav_msgs</depend>
  <depend>rclcpp</depend>
  <depend>rclcpp_action</depend>
  <depend>rclcpp_lifecycle</depend>
  <depend>sensor_msgs</depend>
  <depend>std_msgs</depend>
  <depend>std_srvs</depend>
  <depend>tf2</depend>
  <depend>tf2_ros</depend>

  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>lifecycle_msgs</test_depend>
  <test_depend>test_msgs</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
