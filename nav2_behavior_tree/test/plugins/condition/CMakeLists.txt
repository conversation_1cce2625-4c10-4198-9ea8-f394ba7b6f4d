plugin_add_test(test_condition_distance_traveled test_distance_traveled.cpp nav2_distance_traveled_condition_bt_node)

plugin_add_test(test_condition_time_expired test_time_expired.cpp nav2_time_expired_condition_bt_node)

plugin_add_test(test_condition_path_expiring_timer test_path_expiring_timer.cpp nav2_path_expiring_timer_condition)

plugin_add_test(test_condition_goal_reached test_goal_reached.cpp nav2_goal_reached_condition_bt_node)

plugin_add_test(test_condition_goal_updated test_goal_updated.cpp nav2_goal_updated_condition_bt_node)

plugin_add_test(test_condition_globally_updated_goal test_globally_updated_goal.cpp nav2_globally_updated_goal_condition_bt_node)

plugin_add_test(test_condition_initial_pose_received test_initial_pose_received.cpp nav2_initial_pose_received_condition_bt_node)

plugin_add_test(test_condition_transform_available test_transform_available.cpp nav2_transform_available_condition_bt_node)

plugin_add_test(test_condition_is_stuck test_is_stuck.cpp nav2_is_stuck_condition_bt_node)

plugin_add_test(test_condition_is_stopped test_is_stopped.cpp nav2_is_stopped_condition_bt_node)

plugin_add_test(test_condition_is_battery_charging test_is_battery_charging.cpp nav2_is_battery_charging_condition_bt_node)

plugin_add_test(test_condition_is_battery_low test_is_battery_low.cpp nav2_is_battery_low_condition_bt_node)

plugin_add_test(test_condition_is_path_valid test_is_path_valid.cpp nav2_is_path_valid_condition_bt_node)

plugin_add_test(test_are_error_codes_present test_are_error_codes_present.cpp nav2_would_a_controller_recovery_help_condition_bt_node)

plugin_add_test(test_would_a_controller_recovery_help
  test_would_a_controller_recovery_help.cpp
  nav2_would_a_controller_recovery_help_condition_bt_node)

plugin_add_test(test_would_a_planner_recovery_help
  test_would_a_planner_recovery_help.cpp
  nav2_would_a_planner_recovery_help_condition_bt_node)

plugin_add_test(test_would_a_smoother_recovery_help
  test_would_a_smoother_recovery_help.cpp
  nav2_would_a_smoother_recovery_help_condition_bt_node)
