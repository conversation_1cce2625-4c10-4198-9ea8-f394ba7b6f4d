find_package(test_msgs REQUIRED)

ament_add_gtest(test_bt_action_node test_bt_action_node.cpp)
target_link_libraries(test_bt_action_node rclcpp::rclcpp ${test_msgs_TARGETS} rclcpp_action::rclcpp_action ${library_name})

plugin_add_test(test_action_spin_action test_spin_action.cpp nav2_spin_action_bt_node)

plugin_add_test(test_action_back_up_action test_back_up_action.cpp nav2_back_up_action_bt_node)

plugin_add_test(test_action_drive_on_heading test_drive_on_heading_action.cpp nav2_drive_on_heading_bt_node)

plugin_add_test(test_action_wait_action test_wait_action.cpp nav2_wait_action_bt_node)

plugin_add_test(test_action_assisted_teleop_action test_assisted_teleop_action.cpp nav2_assisted_teleop_action_bt_node)

plugin_add_test(test_action_controller_cancel_action test_controller_cancel_node.cpp nav2_controller_cancel_bt_node)

plugin_add_test(test_action_wait_cancel_action test_wait_cancel_node.cpp nav2_wait_cancel_bt_node)

plugin_add_test(test_action_spin_cancel_action test_spin_cancel_node.cpp nav2_spin_cancel_bt_node)

plugin_add_test(test_action_back_up_cancel_action test_back_up_cancel_node.cpp nav2_back_up_cancel_bt_node)

plugin_add_test(test_action_assisted_teleop_cancel_action test_assisted_teleop_cancel_node.cpp nav2_assisted_teleop_cancel_bt_node)

plugin_add_test(test_action_drive_on_heading_cancel_action test_drive_on_heading_cancel_node.cpp nav2_drive_on_heading_cancel_bt_node)

plugin_add_test(test_action_clear_costmap_service test_clear_costmap_service.cpp nav2_clear_costmap_service_bt_node)

plugin_add_test(test_action_reinitialize_global_localization_service
  test_reinitialize_global_localization_service.cpp
  nav2_reinitialize_global_localization_service_bt_node)

plugin_add_test(test_action_compute_path_to_pose_action test_compute_path_to_pose_action.cpp nav2_compute_path_to_pose_action_bt_node)

plugin_add_test(test_action_compute_path_through_poses_action
  test_compute_path_through_poses_action.cpp
  nav2_compute_path_through_poses_action_bt_node)

plugin_add_test(test_action_smooth_path_action test_smooth_path_action.cpp nav2_smooth_path_action_bt_node)

plugin_add_test(test_action_follow_path_action test_follow_path_action.cpp nav2_follow_path_action_bt_node)

plugin_add_test(test_action_navigate_to_pose_action test_navigate_to_pose_action.cpp nav2_navigate_to_pose_action_bt_node)

plugin_add_test(test_action_navigate_through_poses_action test_navigate_through_poses_action.cpp nav2_navigate_through_poses_action_bt_node)

plugin_add_test(test_truncate_path_action test_truncate_path_action.cpp nav2_truncate_path_action_bt_node)

plugin_add_test(test_truncate_path_local_action test_truncate_path_local_action.cpp nav2_truncate_path_local_action_bt_node)

plugin_add_test(test_remove_passed_goals_action test_remove_passed_goals_action.cpp nav2_remove_passed_goals_action_bt_node)

plugin_add_test(test_remove_in_collision_goals_action
  test_remove_in_collision_goals_action.cpp
  nav2_remove_in_collision_goals_action_bt_node)

plugin_add_test(test_get_pose_from_path_action test_get_pose_from_path_action.cpp nav2_get_pose_from_path_action_bt_node)

plugin_add_test(test_planner_selector_node test_planner_selector_node.cpp nav2_planner_selector_bt_node)

plugin_add_test(test_controller_selector_node test_controller_selector_node.cpp nav2_controller_selector_bt_node)

plugin_add_test(test_smoother_selector_node test_smoother_selector_node.cpp nav2_smoother_selector_bt_node)

plugin_add_test(test_goal_checker_selector_node test_goal_checker_selector_node.cpp nav2_goal_checker_selector_bt_node)

plugin_add_test(test_progress_checker_selector_node test_progress_checker_selector_node.cpp nav2_progress_checker_selector_bt_node)
