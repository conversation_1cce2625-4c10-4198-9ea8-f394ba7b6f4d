#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from nav_msgs.msg import Odometry
from geometry_msgs.msg import TransformStamped
import tf2_ros
import time
import threading

class TransformMonitor(Node):
    def __init__(self):
        super().__init__('transform_monitor')
        
        # TF buffer and listener
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer, self)
        
        # Subscribers
        self.odom_sub = self.create_subscription(
            Odometry, '/odom', self.odom_callback, 10)
        self.indoor_odom_sub = self.create_subscription(
            Odometry, '/odometry/indoor', self.indoor_odom_callback, 10)
        
        # Data storage
        self.latest_odom = None
        self.latest_indoor_odom = None
        
        # Start monitoring thread
        self.monitor_thread = threading.Thread(target=self.monitor_transforms)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        self.get_logger().info("Transform monitor started")
    
    def odom_callback(self, msg):
        self.latest_odom = msg
    
    def indoor_odom_callback(self, msg):
        self.latest_indoor_odom = msg
    
    def get_transform(self, target_frame, source_frame):
        try:
            transform = self.tf_buffer.lookup_transform(
                target_frame, source_frame, rclpy.time.Time())
            return transform
        except Exception as e:
            return None
    
    def monitor_transforms(self):
        while rclpy.ok():
            print("\n" + "="*80)
            print(f"时间: {time.strftime('%H:%M:%S')}")
            print("="*80)
            
            # 获取TF变换
            map_to_odom = self.get_transform('map', 'odom')
            odom_to_baselink = self.get_transform('odom', 'base_link')
            
            if map_to_odom:
                print(f"map->odom: x={map_to_odom.transform.translation.x:.3f}, "
                      f"y={map_to_odom.transform.translation.y:.3f}, "
                      f"yaw={map_to_odom.transform.rotation.z:.3f}")
            else:
                print("map->odom: 变换不可用")
            
            if odom_to_baselink:
                print(f"odom->base_link: x={odom_to_baselink.transform.translation.x:.3f}, "
                      f"y={odom_to_baselink.transform.translation.y:.3f}, "
                      f"yaw={odom_to_baselink.transform.rotation.z:.3f}")
            else:
                print("odom->base_link: 变换不可用")
            
            # 显示odom话题数据
            if self.latest_odom:
                print(f"原始odom: x={self.latest_odom.pose.pose.position.x:.3f}, "
                      f"y={self.latest_odom.pose.pose.position.y:.3f}, "
                      f"yaw={self.latest_odom.pose.pose.orientation.z:.3f}")
                print(f"原始odom速度: vx={self.latest_odom.twist.twist.linear.x:.3f}, "
                      f"vy={self.latest_odom.twist.twist.linear.y:.3f}, "
                      f"vz={self.latest_odom.twist.twist.angular.z:.3f}")
            else:
                print("原始odom: 数据不可用")
            
            # 显示室内EKF输出
            if self.latest_indoor_odom:
                print(f"室内EKF: x={self.latest_indoor_odom.pose.pose.position.x:.3f}, "
                      f"y={self.latest_indoor_odom.pose.pose.position.y:.3f}, "
                      f"yaw={self.latest_indoor_odom.pose.pose.orientation.z:.3f}")
                print(f"室内EKF速度: vx={self.latest_indoor_odom.twist.twist.linear.x:.3f}, "
                      f"vy={self.latest_indoor_odom.twist.twist.linear.y:.3f}, "
                      f"vz={self.latest_indoor_odom.twist.twist.angular.z:.3f}")
            else:
                print("室内EKF: 数据不可用")
            
            time.sleep(2)  # 每2秒更新一次

def main():
    rclpy.init()
    monitor = TransformMonitor()
    
    try:
        rclpy.spin(monitor)
    except KeyboardInterrupt:
        print("\n监控停止")
    finally:
        monitor.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
