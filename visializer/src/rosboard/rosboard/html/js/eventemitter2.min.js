!function(e){function r(){this._events={};if(this._conf){i.call(this,this._conf)}}function i(e){if(e){this._conf=e;e.delimiter&&(this.delimiter=e.delimiter);e.maxListeners&&(this._events.maxListeners=e.maxListeners);e.wildcard&&(this.wildcard=e.wildcard);e.newListener&&(this.newListener=e.newListener);if(this.wildcard){this.listenerTree={}}}}function s(e){this._events={};this.newListener=false;i.call(this,e)}function o(e,t,n,r){if(!n){return[]}var i=[],s,u,a,f,l,c,h,p=t.length,d=t[r],v=t[r+1];if(r===p&&n._listeners){if(typeof n._listeners==="function"){e&&e.push(n._listeners);return[n]}else{for(s=0,u=n._listeners.length;s<u;s++){e&&e.push(n._listeners[s])}return[n]}}if(d==="*"||d==="**"||n[d]){if(d==="*"){for(a in n){if(a!=="_listeners"&&n.hasOwnProperty(a)){i=i.concat(o(e,t,n[a],r+1))}}return i}else if(d==="**"){h=r+1===p||r+2===p&&v==="*";if(h&&n._listeners){i=i.concat(o(e,t,n,p))}for(a in n){if(a!=="_listeners"&&n.hasOwnProperty(a)){if(a==="*"||a==="**"){if(n[a]._listeners&&!h){i=i.concat(o(e,t,n[a],p))}i=i.concat(o(e,t,n[a],r))}else if(a===v){i=i.concat(o(e,t,n[a],r+2))}else{i=i.concat(o(e,t,n[a],r))}}}return i}i=i.concat(o(e,t,n[d],r+1))}f=n["*"];if(f){o(e,t,f,r+1)}l=n["**"];if(l){if(r<p){if(l._listeners){o(e,t,l,p)}for(a in l){if(a!=="_listeners"&&l.hasOwnProperty(a)){if(a===v){o(e,t,l[a],r+2)}else if(a===d){o(e,t,l[a],r+1)}else{c={};c[a]=l[a];o(e,t,{"**":c},r+1)}}}}else if(l._listeners){o(e,t,l,p)}else if(l["*"]&&l["*"]._listeners){o(e,t,l["*"],p)}}return i}function u(e,r){e=typeof e==="string"?e.split(this.delimiter):e.slice();for(var i=0,s=e.length;i+1<s;i++){if(e[i]==="**"&&e[i+1]==="**"){return}}var o=this.listenerTree;var u=e.shift();while(u){if(!o[u]){o[u]={}}o=o[u];if(e.length===0){if(!o._listeners){o._listeners=r}else if(typeof o._listeners==="function"){o._listeners=[o._listeners,r]}else if(t(o._listeners)){o._listeners.push(r);if(!o._listeners.warned){var a=n;if(typeof this._events.maxListeners!=="undefined"){a=this._events.maxListeners}if(a>0&&o._listeners.length>a){o._listeners.warned=true;console.error("(node) warning: possible EventEmitter memory "+"leak detected. %d listeners added. "+"Use emitter.setMaxListeners() to increase limit.",o._listeners.length);console.trace()}}}return true}u=e.shift()}return true}var t=Array.isArray?Array.isArray:function(t){return Object.prototype.toString.call(t)==="[object Array]"};var n=10;s.prototype.delimiter=".";s.prototype.setMaxListeners=function(e){this._events||r.call(this);this._events.maxListeners=e;if(!this._conf)this._conf={};this._conf.maxListeners=e};s.prototype.event="";s.prototype.once=function(e,t){this.many(e,1,t);return this};s.prototype.many=function(e,t,n){function i(){if(--t===0){r.off(e,i)}n.apply(this,arguments)}var r=this;if(typeof n!=="function"){throw new Error("many only accepts instances of Function")}i._origin=n;this.on(e,i);return r};s.prototype.emit=function(){this._events||r.call(this);var e=arguments[0];if(e==="newListener"&&!this.newListener){if(!this._events.newListener){return false}}if(this._all){var t=arguments.length;var n=new Array(t-1);for(var i=1;i<t;i++)n[i-1]=arguments[i];for(i=0,t=this._all.length;i<t;i++){this.event=e;this._all[i].apply(this,n)}}if(e==="error"){if(!this._all&&!this._events.error&&!(this.wildcard&&this.listenerTree.error)){if(arguments[1]instanceof Error){throw arguments[1]}else{throw new Error("Uncaught, unspecified 'error' event.")}return false}}var s;if(this.wildcard){s=[];var u=typeof e==="string"?e.split(this.delimiter):e.slice();o.call(this,s,u,this.listenerTree,0)}else{s=this._events[e]}if(typeof s==="function"){this.event=e;if(arguments.length===1){s.call(this)}else if(arguments.length>1)switch(arguments.length){case 2:s.call(this,arguments[1]);break;case 3:s.call(this,arguments[1],arguments[2]);break;default:var t=arguments.length;var n=new Array(t-1);for(var i=1;i<t;i++)n[i-1]=arguments[i];s.apply(this,n)}return true}else if(s){var t=arguments.length;var n=new Array(t-1);for(var i=1;i<t;i++)n[i-1]=arguments[i];var a=s.slice();for(var i=0,t=a.length;i<t;i++){this.event=e;a[i].apply(this,n)}return a.length>0||!!this._all}else{return!!this._all}};s.prototype.on=function(e,i){if(typeof e==="function"){this.onAny(e);return this}if(typeof i!=="function"){throw new Error("on only accepts instances of Function")}this._events||r.call(this);this.emit("newListener",e,i);if(this.wildcard){u.call(this,e,i);return this}if(!this._events[e]){this._events[e]=i}else if(typeof this._events[e]==="function"){this._events[e]=[this._events[e],i]}else if(t(this._events[e])){this._events[e].push(i);if(!this._events[e].warned){var s=n;if(typeof this._events.maxListeners!=="undefined"){s=this._events.maxListeners}if(s>0&&this._events[e].length>s){this._events[e].warned=true;console.error("(node) warning: possible EventEmitter memory "+"leak detected. %d listeners added. "+"Use emitter.setMaxListeners() to increase limit.",this._events[e].length);console.trace()}}}return this};s.prototype.onAny=function(e){if(typeof e!=="function"){throw new Error("onAny only accepts instances of Function")}if(!this._all){this._all=[]}this._all.push(e);return this};s.prototype.addListener=s.prototype.on;s.prototype.off=function(e,n){if(typeof n!=="function"){throw new Error("removeListener only takes instances of Function")}var r,i=[];if(this.wildcard){var s=typeof e==="string"?e.split(this.delimiter):e.slice();i=o.call(this,null,s,this.listenerTree,0)}else{if(!this._events[e])return this;r=this._events[e];i.push({_listeners:r})}for(var u=0;u<i.length;u++){var a=i[u];r=a._listeners;if(t(r)){var f=-1;for(var l=0,c=r.length;l<c;l++){if(r[l]===n||r[l].listener&&r[l].listener===n||r[l]._origin&&r[l]._origin===n){f=l;break}}if(f<0){continue}if(this.wildcard){a._listeners.splice(f,1)}else{this._events[e].splice(f,1)}if(r.length===0){if(this.wildcard){delete a._listeners}else{delete this._events[e]}}return this}else if(r===n||r.listener&&r.listener===n||r._origin&&r._origin===n){if(this.wildcard){delete a._listeners}else{delete this._events[e]}}}return this};s.prototype.offAny=function(e){var t=0,n=0,r;if(e&&this._all&&this._all.length>0){r=this._all;for(t=0,n=r.length;t<n;t++){if(e===r[t]){r.splice(t,1);return this}}}else{this._all=[]}return this};s.prototype.removeListener=s.prototype.off;s.prototype.removeAllListeners=function(e){if(arguments.length===0){!this._events||r.call(this);return this}if(this.wildcard){var t=typeof e==="string"?e.split(this.delimiter):e.slice();var n=o.call(this,null,t,this.listenerTree,0);for(var i=0;i<n.length;i++){var s=n[i];s._listeners=null}}else{if(!this._events[e])return this;this._events[e]=null}return this};s.prototype.listeners=function(e){if(this.wildcard){var n=[];var i=typeof e==="string"?e.split(this.delimiter):e.slice();o.call(this,n,i,this.listenerTree,0);return n}this._events||r.call(this);if(!this._events[e])this._events[e]=[];if(!t(this._events[e])){this._events[e]=[this._events[e]]}return this._events[e]};s.prototype.listenersAny=function(){if(this._all){return this._all}else{return[]}};if(typeof define==="function"&&define.amd){define(function(){return s})}else if(typeof exports==="object"){exports.EventEmitter2=s}else{window.EventEmitter2=s}}()