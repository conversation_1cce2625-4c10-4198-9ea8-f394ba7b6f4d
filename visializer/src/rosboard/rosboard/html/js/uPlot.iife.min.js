/*! https://github.com/leeoniya/uPlot (v1.6.13) */
var uPlot=function(){"use strict";function n(n,e,r,t){var l;r=r||0;for(var a=2147483647>=(t=t||e.length-1);t-r>1;)n>e[l=a?r+t>>1:m((r+t)/2)]?r=l:t=l;return n-e[r]>e[t]-n?t:r}function e(n,e,r,t){for(var l=1==t?e:r;l>=e&&r>=l;l+=t)if(null!=n[l])return l;return-1}var r=[0,0];function t(n,e,t,l){return r[0]=0>t?H(n,-t):n,r[1]=0>l?H(e,-l):e,r}function l(n,e,r,l){var a,i,o,s=10==r?k:y;return n==e&&(n/=r,e*=r),l?(a=m(s(n)),i=x(s(e)),n=(o=t(_(r,a),_(r,i),a,i))[0],e=o[1]):(a=m(s(p(n))),i=m(s(p(e))),n=F(n,(o=t(_(r,a),_(r,i),a,i))[0]),e=C(e,o[1])),[n,e]}function a(n,e,r,t){var a=l(n,e,r,t);return 0==n&&(a[0]=0),0==e&&(a[1]=0),a}var i={mode:3,pad:.1},o={pad:0,soft:null,mode:0},s={min:o,max:o};function u(n,e,r,t){return j(r)?c(n,e,r):(o.pad=r,o.soft=t?0:null,o.mode=t?3:0,c(n,e,s))}function f(n,e){return null==n?e:n}function c(n,e,r){var t=r.min,l=r.max,a=f(t.pad,0),i=f(l.pad,0),o=f(t.hard,-S),s=f(l.hard,S),u=f(t.soft,S),c=f(l.soft,-S),v=f(t.mode,0),h=f(l.mode,0),d=e-n;1e-9>d&&(d=0,0!=n&&0!=e||(d=1e-9,2==v&&u!=S&&(a=0),2==h&&c!=-S&&(i=0)));var g=d||p(e)||1e3,x=k(g),y=_(10,m(x)),M=H(F(n-g*(0==d?0==n?.1:1:a),y/10),9),E=u>n||1!=v&&(3!=v||M>u)&&(2!=v||u>M)?S:u,z=b(o,E>M&&n>=E?E:w(E,M)),D=H(C(e+g*(0==d?0==e?.1:1:i),y/10),9),T=e>c||1!=h&&(3!=h||c>D)&&(2!=h||D>c)?-S:c,A=w(s,D>T&&T>=e?T:b(T,D));return z==A&&0==z&&(A=100),[z,A]}var v=new Intl.NumberFormat(navigator.language).format,h=Math,d=h.PI,p=h.abs,m=h.floor,g=h.round,x=h.ceil,w=h.min,b=h.max,_=h.pow,k=h.log10,y=h.log2,M=(n,e)=>(void 0===e&&(e=1),h.asinh(n/e)),S=1/0;function E(n,e){return g(n/e)*e}function z(n,e,r){return w(b(n,e),r)}function D(n){return"function"==typeof n?n:()=>n}var T=n=>n,A=(n,e)=>e,P=()=>null,W=()=>!0,Y=(n,e)=>n==e;function C(n,e){return x(n/e)*e}function F(n,e){return m(n/e)*e}function H(n,e){return g(n*(e=Math.pow(10,e)))/e}var R=new Map;function L(n){return((""+n).split(".")[1]||"").length}function I(n,e,r,t){for(var l=[],a=t.map(L),i=e;r>i;i++)for(var o=p(i),s=H(_(n,i),o),u=0;t.length>u;u++){var f=t[u]*s,c=(0>f||0>i?o:0)+(a[u]>i?a[u]:0),v=H(f,c);l.push(v),R.set(v,c)}return l}var G={},B=[null,null],N=Array.isArray;function O(n){return"string"==typeof n}function j(n){var e=!1;if(null!=n){var r=n.constructor;e=null==r||r==Object}return e}function U(n){return null!=n&&"object"==typeof n}function V(n,e){var r;if(e=e||j,N(n))r=n.map((n=>V(n,e)));else if(e(n))for(var t in r={},n)r[t]=V(n[t],e);else r=n;return r}function J(n){for(var e=arguments,r=1;e.length>r;r++){var t=e[r];for(var l in t)j(n[l])?J(n[l],V(t[l])):n[l]=V(t[l])}return n}function q(n,e,r){for(var t=0,l=void 0,a=-1;e.length>t;t++){var i=e[t];if(i>a){for(l=i-1;l>=0&&null==n[l];)n[l--]=null;for(l=i+1;r>l&&null==n[l];)n[a=l++]=null}}}var Z,K,X="undefined"==typeof queueMicrotask?n=>Promise.resolve().then(n):queueMicrotask,Q="width",$="height",nn="top",en="bottom",rn="left",tn="right",ln="#000",an="#0000",on="mousemove",sn="mousedown",un="mouseup",fn="mouseenter",cn="mouseleave",vn="dblclick",hn="change",dn="dppxchange",pn="u-off",mn="u-label",gn=document,xn=window;function wn(n,e){if(null!=e){var r=n.classList;!r.contains(e)&&r.add(e)}}function bn(n,e){var r=n.classList;r.contains(e)&&r.remove(e)}function _n(n,e,r){n.style[e]=r+"px"}function kn(n,e,r,t){var l=gn.createElement(n);return null!=e&&wn(l,e),null!=r&&r.insertBefore(l,t),l}function yn(n,e){return kn("div",n,e)}function Mn(n,e,r,t,l){n.style.transform="translate("+e+"px,"+r+"px)",0>e||0>r||e>t||r>l?wn(n,pn):bn(n,pn)}var Sn={passive:!0},En=J({capture:!0},Sn);function zn(n,e,r,t){e.addEventListener(n,r,t?En:Sn)}function Dn(n,e,r,t){e.removeEventListener(n,r,t?En:Sn)}!function n(){Z=devicePixelRatio,K&&Dn(hn,K,n),K=matchMedia("screen and (min-resolution: "+(Z-.001)+"dppx) and (max-resolution: "+(Z+.001)+"dppx)"),zn(hn,K,n),xn.dispatchEvent(new CustomEvent(dn))}();var Tn=["January","February","March","April","May","June","July","August","September","October","November","December"],An=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];function Pn(n){return n.slice(0,3)}var Wn=An.map(Pn),Yn=Tn.map(Pn),Cn={MMMM:Tn,MMM:Yn,WWWW:An,WWW:Wn};function Fn(n){return(10>n?"0":"")+n}var Hn={YYYY:n=>n.getFullYear(),YY:n=>(n.getFullYear()+"").slice(2),MMMM:(n,e)=>e.MMMM[n.getMonth()],MMM:(n,e)=>e.MMM[n.getMonth()],MM:n=>Fn(n.getMonth()+1),M:n=>n.getMonth()+1,DD:n=>Fn(n.getDate()),D:n=>n.getDate(),WWWW:(n,e)=>e.WWWW[n.getDay()],WWW:(n,e)=>e.WWW[n.getDay()],HH:n=>Fn(n.getHours()),H:n=>n.getHours(),h:n=>{var e=n.getHours();return 0==e?12:e>12?e-12:e},AA:n=>12>n.getHours()?"AM":"PM",aa:n=>12>n.getHours()?"am":"pm",a:n=>12>n.getHours()?"a":"p",mm:n=>Fn(n.getMinutes()),m:n=>n.getMinutes(),ss:n=>Fn(n.getSeconds()),s:n=>n.getSeconds(),fff:n=>function(n){return(10>n?"00":100>n?"0":"")+n}(n.getMilliseconds())};function Rn(n,e){e=e||Cn;for(var r,t=[],l=/\{([a-z]+)\}|[^{]+/gi;r=l.exec(n);)t.push("{"==r[0][0]?Hn[r[1]]:r[0]);return n=>{for(var r="",l=0;t.length>l;l++)r+="string"==typeof t[l]?t[l]:t[l](n,e);return r}}var Ln=(new Intl.DateTimeFormat).resolvedOptions().timeZone,In=n=>n%1==0,Gn=[1,2,2.5,5],Bn=I(10,-16,0,Gn),Nn=I(10,0,16,Gn),On=Nn.filter(In),jn=Bn.concat(Nn),Un="{YYYY}",Vn="\n"+Un,Jn="{M}/{D}",qn="\n"+Jn,Zn=qn+"/{YY}",Kn="{aa}",Xn="{h}:{mm}"+Kn,Qn="\n"+Xn,$n=":{ss}",ne=null;function ee(n){var e=1e3*n,r=60*e,t=60*r,l=24*t,a=30*l,i=365*l;return[(1==n?I(10,0,3,Gn).filter(In):I(10,-3,0,Gn)).concat([e,5*e,10*e,15*e,30*e,r,5*r,10*r,15*r,30*r,t,2*t,3*t,4*t,6*t,8*t,12*t,l,2*l,3*l,4*l,5*l,6*l,7*l,8*l,9*l,10*l,15*l,a,2*a,3*a,4*a,6*a,i,2*i,5*i,10*i,25*i,50*i,100*i]),[[i,Un,ne,ne,ne,ne,ne,ne,1],[28*l,"{MMM}",Vn,ne,ne,ne,ne,ne,1],[l,Jn,Vn,ne,ne,ne,ne,ne,1],[t,"{h}"+Kn,Zn,ne,qn,ne,ne,ne,1],[r,Xn,Zn,ne,qn,ne,ne,ne,1],[e,$n,Zn+" "+Xn,ne,qn+" "+Xn,ne,Qn,ne,1],[n,$n+".{fff}",Zn+" "+Xn,ne,qn+" "+Xn,ne,Qn,ne,1]],function(e){return(o,s,u,f,c,v)=>{var h=[],d=c>=i,p=c>=a&&i>c,x=e(u),w=H(x*n,3),b=ve(x.getFullYear(),d?0:x.getMonth(),p||d?1:x.getDate()),_=H(b*n,3);if(p||d)for(var k=p?c/a:0,y=d?c/i:0,M=w==_?w:H(ve(b.getFullYear()+y,b.getMonth()+k,1)*n,3),S=new Date(g(M/n)),E=S.getFullYear(),z=S.getMonth(),D=0;f>=M;D++){var T=ve(E+y*D,z+k*D,1),A=T-e(H(T*n,3));(M=H((+T+A)*n,3))>f||h.push(M)}else{var P=l>c?c:l,W=_+(m(u)-m(w))+C(w-_,P);h.push(W);for(var Y=e(W),F=Y.getHours()+Y.getMinutes()/r+Y.getSeconds()/t,R=c/t,L=v/o.axes[s]._space;(W=H(W+c,1==n?0:3))<=f;)if(R>1){var I=m(H(F+R,6))%24,G=e(W).getHours()-I;G>1&&(G=-1),F=(F+R)%24,.7>H(((W-=G*t)-h[h.length-1])/c,3)*L||h.push(W)}else h.push(W)}return h}}]}var re=ee(1),te=re[0],le=re[1],ae=re[2],ie=ee(.001),oe=ie[0],se=ie[1],ue=ie[2];function fe(n,e){return n.map((n=>n.map(((r,t)=>0==t||8==t||null==r?r:e(1==t||0==n[8]?r:n[1]+r)))))}function ce(n,e){return(r,t,l,a,i)=>{var o,s,u,f,c,v,h=e.find((n=>i>=n[0]))||e[e.length-1];return t.map((e=>{var r=n(e),t=r.getFullYear(),l=r.getMonth(),a=r.getDate(),i=r.getHours(),d=r.getMinutes(),p=r.getSeconds(),m=t!=o&&h[2]||l!=s&&h[3]||a!=u&&h[4]||i!=f&&h[5]||d!=c&&h[6]||p!=v&&h[7]||h[1];return o=t,s=l,u=a,f=i,c=d,v=p,m(r)}))}}function ve(n,e,r){return new Date(n,e,r)}function he(n,e){return e(n)}function de(n,e){return(r,t)=>e(n(t))}I(2,-53,53,[1]);var pe={show:!0,live:!0,isolate:!1,markers:{show:!0,width:2,stroke:function(n,e){var r=n.series[e];return r.width?r.stroke(n,e):r.points.width?r.points.stroke(n,e):null},fill:function(n,e){return n.series[e].fill(n,e)},dash:"solid"},idx:null,values:[]},me=[0,0];function ge(n,e,r){return n=>{0==n.button&&r(n)}}function xe(n,e,r){return r}var we={show:!0,x:!0,y:!0,lock:!1,move:function(n,e,r){return me[0]=e,me[1]=r,me},points:{show:function(n,e){var r=n.cursor.points,t=yn(),l=r.stroke(n,e),a=r.fill(n,e);t.style.background=a||l;var i=r.size(n,e),o=r.width(n,e,i);o&&(t.style.border=o+"px solid "+l);var s=i/-2;return _n(t,Q,i),_n(t,$,i),_n(t,"marginLeft",s),_n(t,"marginTop",s),t},size:function(n,e){return Re(n.series[e].width,1)},width:0,stroke:function(n,e){return n.series[e].stroke(n,e)},fill:function(n,e){return n.series[e].stroke(n,e)}},bind:{mousedown:ge,mouseup:ge,click:ge,dblclick:ge,mousemove:xe,mouseleave:xe,mouseenter:xe},drag:{setScale:!0,x:!0,y:!1,dist:0,uni:null,_x:!1,_y:!1},focus:{prox:-1},left:-10,top:-10,idx:null,dataIdx:function(n,e,r){return r},idxs:[]},be={show:!0,stroke:"rgba(0,0,0,0.07)",width:2,filter:A},_e=J({},be,{size:10}),ke='12px system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',ye="bold "+ke,Me={show:!0,scale:"x",stroke:ln,space:50,gap:5,size:50,labelGap:0,labelSize:30,labelFont:ye,side:2,grid:be,ticks:_e,font:ke,rotate:0},Se={show:!0,scale:"x",auto:!1,sorted:1,min:S,max:-S,idxs:[]};function Ee(n,e){return e.map((n=>null==n?"":v(n)))}function ze(n,e,r,t,l,a,i){for(var o=[],s=R.get(l)||0,u=r=i?r:H(C(r,l),s);t>=u;u=H(u+l,s))o.push(Object.is(u,-0)?0:u);return o}function De(n,e,r,t,l){var a=[],i=n.scales[n.axes[e].scale].log,o=m((10==i?k:y)(r));l=_(i,o),0>o&&(l=H(l,-o));var s=r;do{a.push(s),l*i>(s=H(s+l,R.get(l)))||(l=s)}while(t>=s);return a}function Te(n,e,r,t,l){var a=n.scales[n.axes[e].scale].asinh,i=t>a?De(n,e,b(a,r),t,l):[a],o=0>t||r>0?[]:[0];return(-a>r?De(n,e,b(a,-t),-r,l):[a]).reverse().map((n=>-n)).concat(o,i)}var Ae=/./,Pe=/[12357]/,We=/[125]/,Ye=/1/;function Ce(n,e,r){var t=n.axes[r],l=t.scale,a=n.scales[l];if(3==a.distr&&2==a.log)return e;var i=n.valToPos,o=t._space,s=i(10,l),u=i(9,l)-s<o?i(7,l)-s<o?i(5,l)-s<o?Ye:We:Pe:Ae;return e.map((n=>4==a.distr&&0==n||u.test(n)?n:null))}function Fe(n,e){return null==e?"":v(e)}var He={show:!0,scale:"y",stroke:ln,space:30,gap:5,size:50,labelGap:0,labelSize:30,labelFont:ye,side:3,grid:be,ticks:_e,font:ke,rotate:0};function Re(n,e){return H((3+2*(n||1))*e,3)}function Le(n,e){var r=n.scales[n.series[e].scale],t=n.bands&&n.bands.some((n=>n.series[0]==e));return 3==r.distr||t?r.min:0}var Ie={scale:"y",auto:!0,sorted:0,show:!0,band:!1,spanGaps:!1,alpha:1,points:{show:function(n,e){var r=n.series[0],t=r.scale,l=r.idxs,a=n.data[0],i=n.valToPos(a[l[0]],t,!0),o=n.valToPos(a[l[1]],t,!0);return p(o-i)/(n.series[e].points.space*Z)>=l[1]-l[0]},filter:null},values:null,min:S,max:-S,idxs:[],path:null,clip:null};function Ge(n,e,r){return r/10}var Be={time:!0,auto:!0,distr:1,log:10,asinh:1,min:null,max:null,dir:1,ori:0},Ne=J({},Be,{time:!1,ori:1}),Oe={};function je(n){var e=Oe[n];return e||(e={key:n,plots:[],sub:function(n){e.plots.push(n)},unsub:function(n){e.plots=e.plots.filter((e=>e!=n))},pub:function(n,r,t,l,a,i,o){for(var s=0;e.plots.length>s;s++)e.plots[s]!=r&&e.plots[s].pub(n,r,t,l,a,i,o)}},null!=n&&(Oe[n]=e)),e}function Ue(n,e,r){var t=n.series[e],l=n.scales,a=n.bbox,i=n._data[0],o=n._data[e],s=l[n.series[0].scale],u=l[t.scale],f=a.left,c=a.top,v=a.width,h=a.height,d=n.valToPosH,p=n.valToPosV;return 0==s.ori?r(t,i,o,s,u,d,p,f,c,v,h,Ke,Qe,nr,rr,lr):r(t,i,o,s,u,p,d,c,f,h,v,Xe,$e,er,tr,ar)}function Ve(n,e,r,t,l){return Ue(n,e,((n,e,a,i,o,s,u,f,c,v,h)=>{var d,p,m=0==i.ori?Qe:$e;1==i.dir*(0==i.ori?1:-1)?(d=r,p=t):(d=t,p=r);var g=E(s(e[d],i,v,f),.5),x=E(u(a[d],o,h,c),.5),w=E(s(e[p],i,v,f),.5),b=E(u(o.max,o,h,c),.5),_=new Path2D(l);return m(_,w,b),m(_,g,b),m(_,g,x),_}))}function Je(n,e,r,t,l,a){var i=null;if(n.length>0){i=new Path2D;for(var o=0==e?nr:er,s=r,u=0;n.length>u;u++){var f=n[u];o(i,s,t,f[0]-s,t+a),s=f[1]}o(i,s,t,r+l-s,t+a)}return i}function qe(n,e,r){if(r>e){var t=n[n.length-1];t&&t[0]==e?t[1]=r:n.push([e,r])}}function Ze(n){return 0==n?T:1==n?g:e=>E(e,n)}function Ke(n,e,r){n.moveTo(e,r)}function Xe(n,e,r){n.moveTo(r,e)}function Qe(n,e,r){n.lineTo(e,r)}function $e(n,e,r){n.lineTo(r,e)}function nr(n,e,r,t,l){n.rect(e,r,t,l)}function er(n,e,r,t,l){n.rect(r,e,l,t)}function rr(n,e,r,t,l,a){n.arc(e,r,t,l,a)}function tr(n,e,r,t,l,a){n.arc(r,e,t,l,a)}function lr(n,e,r,t,l,a,i){n.bezierCurveTo(e,r,t,l,a,i)}function ar(n,e,r,t,l,a,i){n.bezierCurveTo(r,e,l,t,i,a)}function ir(n){return(e,r,t,l,a,i)=>{t!=l&&(a!=t&&i!=t&&n(e,r,t),a!=l&&i!=l&&n(e,r,l),n(e,r,i))}}var or=ir(Qe),sr=ir($e);function ur(){return(n,r,t,l)=>Ue(n,r,((a,i,o,s,u,f,c,v,h,d,p)=>{var m,g,x=a.pxRound;0==s.ori?(m=Qe,g=or):(m=$e,g=sr);var _,k,y,M,z=s.dir*(0==s.ori?1:-1),D={stroke:new Path2D,fill:null,clip:null,band:null,gaps:[],flags:1},T=D.stroke,A=S,P=-S,W=D.gaps,Y=x(f(i[1==z?t:l],s,d,v)),C=!1,F=!1,H=e(o,t,l,1*z),R=e(o,t,l,-1*z),L=E(f(i[H],s,d,v),.5),I=E(f(i[R],s,d,v),.5);L>v&&qe(W,v,L);for(var G=1==z?t:l;G>=t&&l>=G;G+=z){var B=x(f(i[G],s,d,v));if(B==Y)null!=o[G]?(k=x(c(o[G],u,p,h)),A==S&&(m(T,B,k),_=k),A=w(k,A),P=b(k,P)):null===o[G]&&(C=F=!0);else{var N=!1;A!=S?(g(T,Y,A,P,_,k),y=M=Y):C&&(N=!0,C=!1),null!=o[G]?(m(T,B,k=x(c(o[G],u,p,h))),A=P=_=k,F&&B-Y>1&&(N=!0),F=!1):(A=S,P=-S,null===o[G]&&(C=F=!0)),N&&qe(W,y,B),Y=B}}if(A!=S&&A!=P&&M!=Y&&g(T,Y,A,P,_,k),v+d>I&&qe(W,I,v+d),null!=a.fill){var O=D.fill=new Path2D(T),j=x(c(a.fillTo(n,r,a.min,a.max),u,p,h));m(O,I,j),m(O,L,j)}return a.spanGaps||(D.clip=Je(W,s.ori,v,h,d,p)),n.bands.length>0&&(D.band=Ve(n,r,t,l,T)),D}))}function fr(n,e,r,t,l){var a=n.length;if(2>a)return null;var i=new Path2D;if(r(i,n[0],e[0]),2==a)t(i,n[1],e[1]);else{for(var o=Array(a),s=Array(a-1),u=Array(a-1),f=Array(a-1),c=0;a-1>c;c++)u[c]=e[c+1]-e[c],f[c]=n[c+1]-n[c],s[c]=u[c]/f[c];o[0]=s[0];for(var v=1;a-1>v;v++)0===s[v]||0===s[v-1]||s[v-1]>0!=s[v]>0?o[v]=0:(o[v]=3*(f[v-1]+f[v])/((2*f[v]+f[v-1])/s[v-1]+(f[v]+2*f[v-1])/s[v]),isFinite(o[v])||(o[v]=0));o[a-1]=s[a-2];for(var h=0;a-1>h;h++)l(i,n[h]+f[h]/3,e[h]+o[h]*f[h]/3,n[h+1]-f[h]/3,e[h+1]-o[h+1]*f[h]/3,n[h+1],e[h+1])}return i}var cr=new Set;function vr(){cr.forEach((n=>{n.syncRect(!0)}))}zn("resize",xn,vr),zn("scroll",xn,vr,!0);var hr=ur();function dr(n,e,r,t){return(t?[n[0],n[1]].concat(n.slice(2)):[n[0]].concat(n.slice(1))).map(((n,t)=>pr(n,t,e,r)))}function pr(n,e,r,t){return J({},0==e?r:t,n)}function mr(n,e,r){return null==e?B:[e,r]}var gr=mr;function xr(n,e,r){return null==e?B:u(e,r,.1,!0)}function wr(n,e,r,t){return null==e?B:l(e,r,n.scales[t].log,!1)}var br=wr;function _r(n,e,r,t){return null==e?B:a(e,r,n.scales[t].log,!1)}var kr=_r;function yr(n){var e,r;return[n=n.replace(/(\d+)px/,((n,t)=>(e=g((r=+t)*Z))+"px")),e,r]}function Mr(n){n.show&&[n.font,n.labelFont].forEach((n=>{var e=H(n[2]*Z,1);n[0]=n[0].replace(/[0-9.]+px/,e+"px"),n[1]=e}))}function Sr(e,r,t){var o={};function s(n,e){return((3==e.distr?k(n>0?n:e.clamp(o,n,e.min,e.max,e.key)):4==e.distr?M(n,e.asinh):n)-e._min)/(e._max-e._min)}function c(n,e,r,t){var l=s(n,e);return t+r*(-1==e.dir?1-l:l)}function v(n,e,r,t){var l=s(n,e);return t+r*(-1==e.dir?l:1-l)}function y(n,e,r,t){return 0==e.ori?c(n,e,r,t):v(n,e,r,t)}o.valToPosH=c,o.valToPosV=v;var T=!1;o.status=0;var F=o.root=yn("uplot");null!=e.id&&(F.id=e.id),wn(F,e.class),e.title&&(yn("u-title",F).textContent=e.title);var L=kn("canvas"),I=o.ctx=L.getContext("2d"),q=yn("u-wrap",F),K=o.under=yn("u-under",q);q.appendChild(L);var ln=o.over=yn("u-over",q),hn=+f((e=V(e)).pxAlign,1),Sn=Ze(hn);(e.plugins||[]).forEach((n=>{n.opts&&(e=n.opts(o,e)||e)}));var En=e.ms||.001,Tn=o.series=dr(e.series||[],Se,Ie,!1),An=o.axes=dr(e.axes||[],Me,He,!0),Pn=o.scales={},Wn=o.bands=e.bands||[];Wn.forEach((n=>{n.fill=D(n.fill||null)}));var Yn=Tn[0].scale,Cn={axes:function(){An.forEach(((n,e)=>{if(n.show&&n._show){var r,t,l=n.side,a=l%2,i=n.stroke(o,e),s=0==l||3==l?-1:1;if(n.label){var u=g((n._lpos+n.labelGap*s)*Z);I.save(),1==a?(r=t=0,I.translate(u,g(ar+or/2)),I.rotate((3==l?-d:d)/2)):(r=g(lr+ir/2),t=u),I.font=n.labelFont[0],I.fillStyle=i,I.textAlign="center",I.textBaseline=2==l?nn:en,I.fillText(n.label,r,t),I.restore()}var f=n._found,c=f[0],v=f[1];if(0!=v){var h=Pn[n.scale],p=0==a?ir:or,m=0==a?lr:ar,x=g(n.gap*Z),w=n._splits,b=2==h.distr?w.map((n=>Nr[n])):w,_=2==h.distr?Nr[w[1]]-Nr[w[0]]:c,k=n.ticks,M=k.show?g(k.size*Z):0,S=n._rotate*-d/180,E=Sn(n._pos*Z),z=E+(M+x)*s;t=0==a?z:0,r=1==a?z:0,I.font=n.font[0],I.fillStyle=i,I.textAlign=1==n.align?rn:2==n.align?tn:S>0?rn:0>S?tn:0==a?"center":3==l?tn:rn,I.textBaseline=S||1==a?"middle":2==l?nn:en;var D=1.5*n.font[1],T=w.map((n=>Sn(y(n,h,p,m))));n._values.forEach(((n,e)=>{null!=n&&(0==a?r=T[e]:t=T[e],(""+n).split(/\n/gm).forEach(((n,e)=>{S?(I.save(),I.translate(r,t+e*D),I.rotate(S),I.fillText(n,0,0),I.restore()):I.fillText(n,r,t+e*D)})))})),k.show&&Kr(T,k.filter(o,b,e,v,_),a,l,E,M,H(k.width*Z,3),k.stroke(o,e),k.dash,k.cap);var A=n.grid;A.show&&Kr(T,A.filter(o,b,e,v,_),a,0==a?2:1,0==a?ar:lr,0==a?or:ir,H(A.width*Z,3),A.stroke(o,e),A.dash,A.cap)}}})),Zt("drawAxes")},series:function(){Hr>0&&(Tn.forEach(((n,e)=>{if(e>0&&n.show&&null==n._paths){var t=function(n){for(var e=z(Ir-1,0,Hr-1),r=z(Gr+1,0,Hr-1);null==n[e]&&e>0;)e--;for(;null==n[r]&&Hr-1>r;)r++;return[e,r]}(r[e]);n._paths=n.paths(o,e,t[0],t[1])}})),Tn.forEach(((n,e)=>{if(e>0&&n.show){n._paths&&function(n){var e=Tn[n],r=e._paths,t=r.stroke,l=r.fill,a=r.clip,i=r.flags,s=H(e.width*Z,3),u=s%2/2,f=e._stroke=e.stroke(o,n),c=e._fill=e.fill(o,n);I.globalAlpha=e.alpha;var v=1==e.pxAlign;v&&I.translate(u,u),I.save();var h=lr,d=ar,p=ir,m=or,g=s*Z/2;0==e.min&&(m+=g),0==e.max&&(d-=g,m+=g),I.beginPath(),I.rect(h,d,p,m),I.clip(),a&&I.clip(a),function(n,e,r,t,l,a,i,s,u){var f=!1;Wn.forEach(((c,v)=>{if(c.series[0]==n){var h=Tn[c.series[1]],d=(h._paths||G).band;I.save();var p=null;h.show&&d?p=c.fill(o,v)||a:d=null,Jr(e,r,t,l,p,i,s,d,u),I.restore(),f=!0}})),f||Jr(e,r,t,l,a,i,s,null,u)}(n,f,s,e.dash,e.cap,c,t,l,i),I.restore(),v&&I.translate(-u,-u),I.globalAlpha=1}(e);var t=n.points.show(o,e,Ir,Gr),l=n.points.filter(o,e,t,n._paths?n._paths.gaps:null);(t||l)&&function(n,e){var t=Tn[n],l=t.points,a=t.pxRound,i=H(l.width*Z,3),s=i%2/2,u=l.width>0,f=(l.size-l.width)/2*Z,c=H(2*f,3),v=1==t.pxAlign;v&&I.translate(s,s),I.save(),I.beginPath(),I.rect(lr-c,ar-c,ir+2*c,or+2*c),I.clip(),I.globalAlpha=t.alpha;var h,p,m,g,x=new Path2D,w=Pn[t.scale];0==Un.ori?(h=ir,p=lr,m=or,g=ar):(h=or,p=ar,m=ir,g=lr);var b=e=>{if(null!=r[n][e]){var t=a(In(r[0][e],Un,h,p)),l=a(Gn(r[n][e],w,m,g));Bn(x,t+f,l),Nn(x,t,l,f,0,2*d)}};if(e)e.forEach(b);else for(var _=Ir;Gr>=_;_++)b(_);var k=l._stroke=l.stroke(o,n),y=l._fill=l.fill(o,n);Vr(k,i,l.dash,l.cap,y||(u?"#fff":t._stroke)),I.fill(x),u&&I.stroke(x),I.globalAlpha=1,I.restore(),v&&I.translate(-s,-s)}(e,l),Zt("drawSeries",e)}})))}},Fn=(e.drawOrder||["axes","series"]).map((n=>Cn[n]));function Hn(n){var r=Pn[n];if(null==r){var t=(e.scales||G)[n]||G;if(null!=t.from)Hn(t.from),Pn[n]=J({},Pn[t.from],t);else{(r=Pn[n]=J({},n==Yn?Be:Ne,t)).key=n;var l=r.time,a=r.range,o=N(a);if(n!=Yn&&(!o||null!=a[0]&&null!=a[1]||(a={min:null==a[0]?i:{mode:1,hard:a[0],soft:a[0]},max:null==a[1]?i:{mode:1,hard:a[1],soft:a[1]}},o=!1),!o&&j(a))){var s=a;a=(n,e,r)=>null==e?B:u(e,r,s)}r.range=D(a||(l?gr:n==Yn?3==r.distr?br:4==r.distr?kr:mr:3==r.distr?wr:4==r.distr?_r:xr)),r.auto=D(!o&&r.auto),r.clamp=D(r.clamp||Ge),r._min=r._max=null}}}for(var Ln in Hn("x"),Hn("y"),Tn.forEach((n=>{Hn(n.scale)})),An.forEach((n=>{Hn(n.scale)})),e.scales)Hn(Ln);var In,Gn,Bn,Nn,Un=Pn[Yn],Vn=Un.distr;0==Un.ori?(wn(F,"u-hz"),In=c,Gn=v,Bn=Ke,Nn=rr):(wn(F,"u-vt"),In=v,Gn=c,Bn=Xe,Nn=tr);var Jn={};for(var qn in Pn){var Zn=Pn[qn];null==Zn.min&&null==Zn.max||(Jn[qn]={min:Zn.min,max:Zn.max},Zn.min=Zn.max=null)}var Kn,Xn=e.tzDate||(n=>new Date(g(n/En))),Qn=e.fmtDate||Rn,$n=1==En?ae(Xn):ue(Xn),ne=ce(Xn,fe(1==En?le:se,Qn)),ee=de(Xn,he("{YYYY}-{MM}-{DD} {h}:{mm}{aa}",Qn)),re=o.legend=J({},pe,e.legend),ie=re.show,ve=re.markers;ve.width=D(ve.width),ve.dash=D(ve.dash),ve.stroke=D(ve.stroke),ve.fill=D(ve.fill);var me,ge=[],xe=[],be=!1,_e={};if(re.live){var ke=Tn[1]?Tn[1].values:null;for(var ye in me=(be=null!=ke)?ke(o,1,0):{_:0})_e[ye]="--"}if(ie)if(Kn=kn("table","u-legend",F),be){var Ae=kn("tr","u-thead",Kn);for(var Pe in kn("th",null,Ae),me)kn("th",mn,Ae).textContent=Pe}else wn(Kn,"u-inline"),re.live&&wn(Kn,"u-live");var We={show:!0},Ye={show:!1},Oe=new Map;function Ue(n,e,r){var t=Oe.get(e)||{},l=Dr.bind[n](o,e,r);l&&(zn(n,e,t[n]=l),Oe.set(e,t))}function Ve(n,e){var r=Oe.get(e)||{};for(var t in r)null!=n&&t!=n||(Dn(t,e,r[t]),delete r[t]);null==n&&Oe.delete(e)}var Je=0,qe=0,Qe=0,$e=0,nr=0,er=0,lr=0,ar=0,ir=0,or=0;o.bbox={};var sr=!1,ur=!1,fr=!1,vr=!1,Sr=!1;function Er(n,e,r){(r||n!=o.width||e!=o.height)&&zr(n,e),$r(!1),fr=!0,ur=!0,vr=Dr.left>=0,Sr=!0,ht()}function zr(n,e){o.width=Je=Qe=n,o.height=qe=$e=e,nr=er=0,function(){var n=!1,e=!1,r=!1,t=!1;An.forEach((l=>{if(l.show&&l._show){var a=l.side,i=a%2,o=l._size+(l.labelSize=null!=l.label?l.labelSize||30:0);o>0&&(i?(Qe-=o,3==a?(nr+=o,t=!0):r=!0):($e-=o,0==a?(er+=o,n=!0):e=!0))}})),Cr[0]=n,Cr[1]=r,Cr[2]=e,Cr[3]=t,Qe-=Lr[1]+Lr[3],nr+=Lr[3],$e-=Lr[2]+Lr[0],er+=Lr[0]}(),function(){var n=nr+Qe,e=er+$e,r=nr,t=er;function l(l,a){switch(l){case 1:return(n+=a)-a;case 2:return(e+=a)-a;case 3:return(r-=a)+a;case 0:return(t-=a)+a}}An.forEach((n=>{if(n.show&&n._show){var e=n.side;n._pos=l(e,n._size),null!=n.label&&(n._lpos=l(e,n.labelSize))}}))}();var r=o.bbox;lr=r.left=E(nr*Z,.5),ar=r.top=E(er*Z,.5),ir=r.width=E(Qe*Z,.5),or=r.height=E($e*Z,.5)}o.setSize=function(n){Er(n.width,n.height)};var Dr=o.cursor=J({},we,e.cursor);Dr._lock=!1;var Tr=Dr.points;Tr.show=D(Tr.show),Tr.size=D(Tr.size),Tr.stroke=D(Tr.stroke),Tr.width=D(Tr.width),Tr.fill=D(Tr.fill);var Ar=o.focus=J({},e.focus||{alpha:.3},Dr.focus),Pr=Ar.prox>=0,Wr=[null];function Yr(n,e){var r=Pn[n.scale].time,t=n.value;if(n.value=r?O(t)?de(Xn,he(t,Qn)):t||ee:t||Fe,n.label=n.label||(r?"Time":"Value"),e>0){n.width=null==n.width?1:n.width,n.paths=n.paths||hr||P,n.fillTo=D(n.fillTo||Le),n.pxAlign=+f(n.pxAlign,hn),n.pxRound=Ze(n.pxAlign),n.stroke=D(n.stroke||null),n.fill=D(n.fill||null),n._stroke=n._fill=n._paths=n._focus=null;var l=Re(n.width,1),a=n.points=J({},{size:l,width:b(1,.2*l),stroke:n.stroke,space:2*l,_stroke:null,_fill:null},n.points);a.show=D(a.show),a.filter=D(a.filter),a.fill=D(a.fill),a.stroke=D(a.stroke)}if(ie){var i=function(n,e){if(0==e&&(be||!re.live))return B;var r=[],t=kn("tr","u-series",Kn,Kn.childNodes[e]);wn(t,n.class),n.show||wn(t,pn);var l=kn("th",null,t);if(ve.show){var a=yn("u-marker",l);if(e>0){var i=ve.width(o,e);i&&(a.style.border=i+"px "+ve.dash(o,e)+" "+ve.stroke(o,e)),a.style.background=ve.fill(o,e)}}var s=yn(mn,l);for(var u in s.textContent=n.label,e>0&&(ve.show||(s.style.color=n.width>0?ve.stroke(o,e):ve.fill(o,e)),Ue("click",l,(e=>{if(!Dr._lock){var r=Tn.indexOf(n);if(e.ctrlKey!=re.isolate){var t=Tn.some(((n,e)=>e>0&&e!=r&&n.show));Tn.forEach(((n,e)=>{e>0&&zt(e,t?e==r?We:Ye:We,Kt.setSeries)}))}else zt(r,{show:!n.show},Kt.setSeries)}})),Pr&&Ue(fn,l,(()=>{Dr._lock||zt(Tn.indexOf(n),Dt,Kt.setSeries)}))),me){var f=kn("td","u-value",t);f.textContent="--",r.push(f)}return[t,r]}(n,e);ge.splice(e,0,i[0]),xe.splice(e,0,i[1]),re.values.push(null)}if(Dr.show){Dr.idxs.splice(e,0,null);var s=function(n,e){if(e>0){var r=Dr.points.show(o,e);if(r)return wn(r,"u-cursor-pt"),wn(r,n.class),Mn(r,-10,-10,Qe,$e),ln.insertBefore(r,Wr[e]),r}}(n,e);s&&Wr.splice(e,0,s)}}o.addSeries=function(n,e){n=pr(n,e=null==e?Tn.length:e,Se,Ie),Tn.splice(e,0,n),Yr(Tn[e],e)},o.delSeries=function(n){if(Tn.splice(n,1),ie){re.values.splice(n,1),xe.splice(n,1);var e=ge.splice(n,1)[0];Ve(null,e.firstChild),e.remove()}Dr.show&&(Dr.idxs.splice(n,1),Wr.length>1&&Wr.splice(n,1)[0].remove())};var Cr=[!1,!1,!1,!1];function Fr(n,e,r){var t=r[0],l=r[1],a=r[2],i=r[3],o=e%2,s=0;return 0==o&&(i||l)&&(s=0==e&&!t||2==e&&!a?g(Me.size/3):0),1==o&&(t||a)&&(s=1==e&&!l||3==e&&!i?g(He.size/2):0),s}var Hr,Rr=o.padding=(e.padding||[Fr,Fr,Fr,Fr]).map((n=>D(f(n,Fr)))),Lr=o._padding=Rr.map(((n,e)=>n(o,e,Cr,0))),Ir=null,Gr=null,Br=Tn[0].idxs,Nr=null,Or=!1;function jr(n,e){if((r=(n||[]).slice())[0]=r[0]||[],o.data=r.slice(),Hr=(Nr=r[0]).length,2==Vn&&(r[0]=Nr.map(((n,e)=>e))),o._data=r,$r(!0),Zt("setData"),!1!==e){var t=Un;t.auto(o,Or)?Ur():Et(Yn,t.min,t.max),vr=Dr.left>=0,Sr=!0,ht()}}function Ur(){var n,e,t,i,o;Or=!0,Hr>0?(Ir=Br[0]=0,Gr=Br[1]=Hr-1,i=r[0][Ir],o=r[0][Gr],2==Vn?(i=Ir,o=Gr):1==Hr&&(3==Vn?(i=(n=l(i,i,Un.log,!1))[0],o=n[1]):4==Vn?(i=(e=a(i,i,Un.log,!1))[0],o=e[1]):Un.time?o=i+g(86400/En):(i=(t=u(i,o,.1,!0))[0],o=t[1]))):(Ir=Br[0]=i=null,Gr=Br[1]=o=null),Et(Yn,i,o)}function Vr(n,e,r,t,l){I.strokeStyle=n||an,I.lineWidth=e,I.lineJoin="round",I.lineCap=t||"butt",I.setLineDash(r||[]),I.fillStyle=l||an}function Jr(n,e,r,t,l,a,i,o,s){Vr(n,e,r,t,l),o?3==(3&s)?(I.clip(o),Zr(l,i),qr(n,a,e)):2&s?(Zr(l,i),I.clip(o),qr(n,a,e)):1&s&&(I.save(),I.clip(o),Zr(l,i),I.restore(),qr(n,a,e)):(Zr(l,i),qr(n,a,e))}function qr(n,e,r){n&&e&&r&&I.stroke(e)}function Zr(n,e){n&&e&&I.fill(e)}function Kr(n,e,r,t,l,a,i,o,s,u){var f=i%2/2;1==hn&&I.translate(f,f),Vr(o,i,s,u),I.beginPath();var c,v,h,d,p=l+(0==t||3==t?-a:a);0==r?(v=l,d=p):(c=l,h=p),n.forEach(((n,t)=>{null!=e[t]&&(0==r?c=h=n:v=d=n,I.moveTo(c,v),I.lineTo(h,d))})),I.stroke(),1==hn&&I.translate(-f,-f)}function Xr(n){var e=!0;return An.forEach(((r,t)=>{if(r.show){var l=Pn[r.scale];if(null!=l.min){r._show||(e=!1,r._show=!0,$r(!1));var a=r.side,i=l.min,s=l.max,u=function(n,e,r,t){var l,a=An[n];if(t>0){var i=a._space=a.space(o,n,e,r,t),s=a._incrs=a.incrs(o,n,e,r,t,i);l=a._found=function(n,e,r,t,l){for(var a=t/(e-n),i=(""+m(n)).length,o=0;r.length>o;o++){var s=r[o]*a,u=10>r[o]?R.get(r[o]):0;if(s>=l&&17>i+u)return[r[o],s]}return[0,0]}(e,r,s,t,i)}else l=[0,0];return l}(t,i,s,0==a%2?Qe:$e),f=u[0],c=u[1];if(0!=c){var v=r._splits=r.splits(o,t,i,s,f,c,2==l.distr),h=2==l.distr?v.map((n=>Nr[n])):v,d=2==l.distr?Nr[v[1]]-Nr[v[0]]:f,p=r._values=r.values(o,r.filter(o,h,t,c,d),t,c,d);r._rotate=2==a?r.rotate(o,p,t,c):0;var g=r._size;r._size=x(r.size(o,p,t,n)),null!=g&&r._size!=g&&(e=!1)}}else r._show&&(e=!1,r._show=!1,$r(!1))}})),e}function Qr(n){var e=!0;return Rr.forEach(((r,t)=>{var l=r(o,t,Cr,n);l!=Lr[t]&&(e=!1),Lr[t]=l})),e}function $r(n){Tn.forEach(((e,r)=>{r>0&&(e._paths=null,n&&(e.min=null,e.max=null))}))}o.setData=jr;var nt,et,rt,tt,lt,at,it,ot,st,ut,ft,ct,vt=!1;function ht(){vt||(X(dt),vt=!0)}function dt(){sr&&(function(){var e=V(Pn,U);for(var t in e){var l=e[t],a=Jn[t];if(null!=a&&null!=a.min)J(l,a),t==Yn&&$r(!0);else if(t!=Yn)if(0==Hr&&null==l.from){var i=l.range(o,null,null,t);l.min=i[0],l.max=i[1]}else l.min=S,l.max=-S}if(Hr>0)for(var s in Tn.forEach(((t,l)=>{var a=t.scale,i=e[a],s=Jn[a];if(0==l){var u=i.range(o,i.min,i.max,a);i.min=u[0],i.max=u[1],Ir=n(i.min,r[0]),Gr=n(i.max,r[0]),i.min>r[0][Ir]&&Ir++,r[0][Gr]>i.max&&Gr--,t.min=Nr[Ir],t.max=Nr[Gr]}else if(t.show&&t.auto&&i.auto(o,Or)&&(null==s||null==s.min)){var f=null==t.min?3==i.distr?function(n,e,r){for(var t=S,l=-S,a=e;r>=a;a++)n[a]>0&&(t=w(t,n[a]),l=b(l,n[a]));return[t==S?1:t,l==-S?10:l]}(r[l],Ir,Gr):function(n,e,r,t){var l=S,a=-S;if(1==t)l=n[e],a=n[r];else if(-1==t)l=n[r],a=n[e];else for(var i=e;r>=i;i++)null!=n[i]&&(l=w(l,n[i]),a=b(a,n[i]));return[l,a]}(r[l],Ir,Gr,t.sorted):[t.min,t.max];i.min=w(i.min,t.min=f[0]),i.max=b(i.max,t.max=f[1])}t.idxs[0]=Ir,t.idxs[1]=Gr})),e){var u=e[s],f=Jn[s];if(null==u.from&&(null==f||null==f.min)){var c=u.range(o,u.min==S?null:u.min,u.max==-S?null:u.max,s);u.min=c[0],u.max=c[1]}}for(var v in e){var h=e[v];if(null!=h.from){var d=e[h.from],p=h.range(o,d.min,d.max,v);h.min=p[0],h.max=p[1]}}var m={},g=!1;for(var x in e){var _=e[x],y=Pn[x];if(y.min!=_.min||y.max!=_.max){y.min=_.min,y.max=_.max;var E=y.distr;y._min=3==E?k(y.min):4==E?M(y.min,y.asinh):y.min,y._max=3==E?k(y.max):4==E?M(y.max,y.asinh):y.max,m[x]=g=!0}}if(g){for(var z in Tn.forEach((n=>{m[n.scale]&&(n._paths=null)})),m)fr=!0,Zt("setScale",z);Dr.show&&(vr=Dr.left>=0)}for(var D in Jn)Jn[D]=null}(),sr=!1),fr&&(function(){for(var n=!1,e=0;!n;){var r=Xr(++e),t=Qr(e);(n=3==e||r&&t)||(zr(o.width,o.height),ur=!0)}}(),fr=!1),ur&&(_n(K,rn,nr),_n(K,nn,er),_n(K,Q,Qe),_n(K,$,$e),_n(ln,rn,nr),_n(ln,nn,er),_n(ln,Q,Qe),_n(ln,$,$e),_n(q,Q,Je),_n(q,$,qe),L.width=g(Je*Z),L.height=g(qe*Z),It(!1),Zt("setSize"),ur=!1),Je>0&&qe>0&&(I.clearRect(0,0,L.width,L.height),Zt("drawClear"),Fn.forEach((n=>n())),Zt("draw")),Dr.show&&vr&&(Rt(),vr=!1),T||(T=!0,o.status=1,Zt("ready")),Or=!1,vt=!1}function pt(e,t){var l=Pn[e];if(null==l.from){if(0==Hr){var a=l.range(o,t.min,t.max,e);t.min=a[0],t.max=a[1]}if(t.min>t.max){var i=t.min;t.min=t.max,t.max=i}if(Hr>1&&null!=t.min&&null!=t.max&&1e-16>t.max-t.min)return;e==Yn&&2==l.distr&&Hr>0&&(t.min=n(t.min,r[0]),t.max=n(t.max,r[0])),Jn[e]=t,sr=!0,ht()}}o.redraw=(n,e)=>{fr=e||!1,!1!==n?Et(Yn,Un.min,Un.max):ht()},o.setScale=pt;var mt=!1,gt=Dr.drag,xt=gt.x,wt=gt.y;Dr.show&&(Dr.x&&(nt=yn("u-cursor-x",ln)),Dr.y&&(et=yn("u-cursor-y",ln)),0==Un.ori?(rt=nt,tt=et):(rt=et,tt=nt),ft=Dr.left,ct=Dr.top);var bt,_t,kt,yt=o.select=J({show:!0,over:!0,left:0,width:0,top:0,height:0},e.select),Mt=yt.show?yn("u-select",yt.over?ln:K):null;function St(n,e){if(yt.show){for(var r in n)_n(Mt,r,yt[r]=n[r]);!1!==e&&Zt("setSelect")}}function Et(n,e,r){pt(n,{min:e,max:r})}function zt(n,e,r){var t=Tn[n];null!=e.focus&&function(n){if(n!=kt){var e=null==n,r=1!=Ar.alpha;Tn.forEach(((t,l)=>{var a=e||0==l||l==n;t._focus=e?null:a,r&&function(n,e){Tn[n].alpha=e,Dr.show&&Wr[n]&&(Wr[n].style.opacity=e),ie&&ge[n]&&(ge[n].style.opacity=e)}(l,a?1:Ar.alpha)})),kt=n,r&&ht()}}(n),null!=e.show&&(t.show=e.show,function(n){var e=ie?ge[n]:null;Tn[n].show?e&&bn(e,pn):(e&&wn(e,pn),Wr.length>1&&Mn(Wr[n],-10,-10,Qe,$e))}(n),Et(t.scale,null,null),ht()),Zt("setSeries",n,e),r&&$t("setSeries",o,n,e)}o.setSelect=St,o.setSeries=zt,o.addBand=function(n,e){n.fill=D(n.fill||null),Wn.splice(e=null==e?Wn.length:e,0,n)},o.setBand=function(n,e){J(Wn[n],e)},o.delBand=function(n){null==n?Wn.length=0:Wn.splice(n,1)};var Dt={focus:!0},Tt={focus:!1};function At(n,e,r){var t=Pn[e];r&&(n=n/Z-(1==t.ori?er:nr));var l=Qe;1==t.ori&&(n=(l=$e)-n),-1==t.dir&&(n=l-n);var a=t._min,i=a+n/l*(t._max-a),o=t.distr;return 3==o?_(10,i):4==o?((n,e)=>(void 0===e&&(e=1),h.sinh(n/e)))(i,t.asinh):i}function Pt(n,e){_n(Mt,rn,yt.left=n),_n(Mt,Q,yt.width=e)}function Wt(n,e){_n(Mt,nn,yt.top=n),_n(Mt,$,yt.height=e)}ie&&Pr&&zn(cn,Kn,(()=>{Dr._lock||(zt(null,Tt,Kt.setSeries),Rt())})),o.valToIdx=e=>n(e,r[0]),o.posToIdx=function(e,t){return n(At(e,Yn,t),r[0],Ir,Gr)},o.posToVal=At,o.valToPos=(n,e,r)=>0==Pn[e].ori?c(n,Pn[e],r?ir:Qe,r?lr:0):v(n,Pn[e],r?or:$e,r?ar:0),o.batch=function(n){n(o),ht()},o.setCursor=(n,e)=>{ft=n.left,ct=n.top,Rt(null,null,e)};var Yt=0==Un.ori?Pt:Wt,Ct=1==Un.ori?Pt:Wt;function Ft(n,e){if(null!=n){var r=n.idx;re.idx=r,Tn.forEach(((n,e)=>{(e>0||!be)&&Ht(e,r)}))}ie&&re.live&&function(){if(ie&&re.live)for(var n=0;Tn.length>n;n++)if(0!=n||!be){var e=re.values[n],r=0;for(var t in e)xe[n][r++].firstChild.nodeValue=e[t]}}(),Sr=!1,!1!==e&&Zt("setLegend")}function Ht(n,e){var t;if(null==e)t=_e;else{var l=Tn[n],a=0==n&&2==Vn?Nr:r[n];t=be?l.values(o,n,e):{_:l.value(o,a[e],n,e)}}re.values[n]=t}function Rt(e,t,l){var a,i;st=ft,ut=ct,a=Dr.move(o,ft,ct),ft=a[0],ct=a[1],Dr.show&&(rt&&Mn(rt,g(ft),0,Qe,$e),tt&&Mn(tt,0,g(ct),Qe,$e));var s=!1;bt=S;var u=0==Un.ori?Qe:$e,f=1==Un.ori?Qe:$e;if(0>ft||0==Hr||Ir>Gr){i=null;for(var c=0;Tn.length>c;c++)c>0&&Wr.length>1&&Mn(Wr[c],-10,-10,Qe,$e);if(Pr&&zt(null,Dt,Kt.setSeries),re.live){s=!0;for(var v=0;Tn.length>v;v++)re.values[v]=_e}}else{var h=At(0==Un.ori?ft:ct,Yn);i=n(h,r[0],Ir,Gr);for(var d=C(In(r[0][i],Un,u,0),.5),m=0;Tn.length>m;m++){var x=Tn[m],b=Dr.dataIdx(o,m,i,h);Dr.idxs[m]=b;var _=b==i?d:C(In(r[0][b],Un,u,0),.5);if(m>0&&x.show){var k=r[m][b],y=null==k?-10:C(Gn(k,Pn[x.scale],f,0),.5);if(y>0){var M=p(y-ct);M>bt||(bt=M,_t=m)}var E=void 0,z=void 0;0==Un.ori?(E=_,z=y):(E=y,z=_),Wr.length>1&&Mn(Wr[m],E,z,Qe,$e)}if(re.live){if(b==Dr.idx&&!Sr||0==m&&be)continue;s=!0,Ht(m,b)}}}if(s&&(re.idx=i,Ft()),yt.show&&mt)if(null!=t){var D=Kt.scales,A=D[0],P=D[1],W=Kt.match,Y=W[1],F=t.cursor.sync.scales,H=F[0],R=F[1],L=t.cursor.drag;xt=L._x,wt=L._y;var I,G,B,N,O,j=t.select,U=j.left,V=j.top,J=j.width,q=j.height,Z=t.scales[A].ori,K=t.posToVal,X=null!=A&&(0,W[0])(A,H),Q=null!=P&&Y(P,R);X&&(0==Z?(I=U,G=J):(I=V,G=q),xt?(B=Pn[A],N=In(K(I,H),B,u,0),O=In(K(I+G,H),B,u,0),Yt(w(N,O),p(O-N))):Yt(0,u),Q||Ct(0,f)),Q&&(1==Z?(I=U,G=J):(I=V,G=q),wt?(B=Pn[P],N=Gn(K(I,R),B,f,0),O=Gn(K(I+G,R),B,f,0),Ct(w(N,O),p(O-N))):Ct(0,f),X||Yt(0,u))}else{var $=p(st-lt),nn=p(ut-at);if(1==Un.ori){var en=$;$=nn,nn=en}xt=gt.x&&$>=gt.dist,wt=gt.y&&nn>=gt.dist;var rn,tn,ln=gt.uni;null!=ln?xt&&wt&&(wt=nn>=ln,(xt=$>=ln)||wt||(nn>$?wt=!0:xt=!0)):gt.x&&gt.y&&(xt||wt)&&(xt=wt=!0),xt&&(0==Un.ori?(rn=it,tn=ft):(rn=ot,tn=ct),Yt(w(rn,tn),p(tn-rn)),wt||Ct(0,f)),wt&&(1==Un.ori?(rn=it,tn=ft):(rn=ot,tn=ct),Ct(w(rn,tn),p(tn-rn)),xt||Yt(0,u)),xt||wt||(Yt(0,0),Ct(0,0))}if(Dr.idx=i,Dr.left=ft,Dr.top=ct,gt._x=xt,gt._y=wt,null!=e){if(null!=Xt){var an=Kt.scales,sn=an[0],un=an[1];Kt.values[0]=null!=sn?At(0==Un.ori?ft:ct,sn):null,Kt.values[1]=null!=un?At(1==Un.ori?ft:ct,un):null}if($t(on,o,ft,ct,Qe,$e,i),Pr){var fn=Kt.setSeries,cn=Ar.prox;null==kt?bt>cn||zt(_t,Dt,fn):bt>cn?zt(null,Dt,fn):_t!=kt&&zt(_t,Dt,fn)}}T&&!1!==l&&Zt("setCursor")}o.setLegend=Ft;var Lt=null;function It(n){!0===n?Lt=null:Zt("syncRect",Lt=ln.getBoundingClientRect())}function Gt(n,e,r,t,l,a){Dr._lock||(Bt(n,e,r,t,l,a,0,!1,null!=n),null!=n?Rt(1):Rt(null,e))}function Bt(n,e,r,t,l,a,i,s,u){var f;if(null==Lt&&It(!1),null!=n)r=n.clientX-Lt.left,t=n.clientY-Lt.top;else{if(0>r||0>t)return ft=-10,void(ct=-10);var c=Kt.scales,v=c[0],h=c[1],d=e.cursor.sync,p=d.values,m=p[0],g=p[1],x=d.scales,w=x[0],b=x[1],_=Kt.match,k=_[1],M=1==e.scales[w].ori,S=0==Un.ori?Qe:$e,z=1==Un.ori?Qe:$e,D=M?a:l,T=M?l:a,A=M?t:r,P=M?r:t;if(r=null!=w?(0,_[0])(v,w)?y(m,Pn[v],S,0):-10:S*(A/D),t=null!=b?k(h,b)?y(g,Pn[h],z,0):-10:z*(P/T),1==Un.ori){var W=r;r=t,t=W}}u&&(r>1&&Qe-1>r||(r=E(r,Qe)),t>1&&$e-1>t||(t=E(t,$e))),s?(lt=r,at=t,f=Dr.move(o,r,t),it=f[0],ot=f[1]):(ft=r,ct=t)}function Nt(){St({width:0,height:0},!1)}function Ot(n,e,r,t,l,a){mt=!0,xt=wt=gt._x=gt._y=!1,Bt(n,e,r,t,l,a,0,!0,!1),null!=n&&(Ue(un,gn,jt),$t(sn,o,it,ot,Qe,$e,null))}function jt(n,e,r,t,l,a){mt=gt._x=gt._y=!1,Bt(n,e,r,t,l,a,0,!1,!0);var i=yt.left,s=yt.top,u=yt.width,f=yt.height,c=u>0||f>0;if(c&&St(yt),gt.setScale&&c){var v=i,h=u,d=s,p=f;if(1==Un.ori&&(v=s,h=f,d=i,p=u),xt&&Et(Yn,At(v,Yn),At(v+h,Yn)),wt)for(var m in Pn){var g=Pn[m];m!=Yn&&null==g.from&&g.min!=S&&Et(m,At(d+p,m),At(d,m))}Nt()}else Dr.lock&&(Dr._lock=!Dr._lock,Dr._lock||Rt());null!=n&&(Ve(un,gn),$t(un,o,ft,ct,Qe,$e,null))}function Ut(n){Ur(),Nt(),null!=n&&$t(vn,o,ft,ct,Qe,$e,null)}function Vt(){An.forEach(Mr),Er(o.width,o.height,!0)}zn(dn,xn,Vt);var Jt={};Jt.mousedown=Ot,Jt.mousemove=Gt,Jt.mouseup=jt,Jt.dblclick=Ut,Jt.setSeries=(n,e,r,t)=>{zt(r,t)},Dr.show&&(Ue(sn,ln,Ot),Ue(on,ln,Gt),Ue(fn,ln,It),Ue(cn,ln,(function(){if(!Dr._lock){var n=mt;if(mt){var e,r,t=!0,l=!0;0==Un.ori?(e=xt,r=wt):(e=wt,r=xt),e&&r&&(t=10>=ft||ft>=Qe-10,l=10>=ct||ct>=$e-10),e&&t&&(ft=it>ft?0:Qe),r&&l&&(ct=ot>ct?0:$e),Rt(1),mt=!1}ft=-10,ct=-10,Rt(1),n&&(mt=n)}})),Ue(vn,ln,Ut),cr.add(o),o.syncRect=It);var qt=o.hooks=e.hooks||{};function Zt(n,e,r){n in qt&&qt[n].forEach((n=>{n.call(null,o,e,r)}))}(e.plugins||[]).forEach((n=>{for(var e in n.hooks)qt[e]=(qt[e]||[]).concat(n.hooks[e])}));var Kt=J({key:null,setSeries:!1,filters:{pub:W,sub:W},scales:[Yn,Tn[1]?Tn[1].scale:null],match:[Y,Y],values:[null,null]},Dr.sync);Dr.sync=Kt;var Xt=Kt.key,Qt=je(Xt);function $t(n,e,r,t,l,a,i){Kt.filters.pub(n,e,r,t,l,a,i)&&Qt.pub(n,e,r,t,l,a,i)}function nl(){Zt("init",e,r),jr(r||e.data,!1),Jn[Yn]?pt(Yn,Jn[Yn]):Ur(),Er(e.width,e.height),Rt(),St(yt,!1)}return Qt.sub(o),o.pub=function(n,e,r,t,l,a,i){Kt.filters.sub(n,e,r,t,l,a,i)&&Jt[n](null,e,r,t,l,a,i)},o.destroy=function(){Qt.unsub(o),cr.delete(o),Oe.clear(),Dn(dn,xn,Vt),F.remove(),Zt("destroy")},Tn.forEach(Yr),An.forEach((function(n,e){if(n._show=n.show,n.show){var r=Pn[n.scale];null==r&&(n.scale=n.side%2?Tn[1].scale:Yn,r=Pn[n.scale]);var t=r.time;n.size=D(n.size),n.space=D(n.space),n.rotate=D(n.rotate),n.incrs=D(n.incrs||(2==r.distr?On:t?1==En?te:oe:jn)),n.splits=D(n.splits||(t&&1==r.distr?$n:3==r.distr?De:4==r.distr?Te:ze)),n.stroke=D(n.stroke),n.grid.stroke=D(n.grid.stroke),n.ticks.stroke=D(n.ticks.stroke);var l=n.values;n.values=N(l)&&!N(l[0])?D(l):t?N(l)?ce(Xn,fe(l,Qn)):O(l)?function(n,e){var r=Rn(e);return(e,t)=>t.map((e=>r(n(e))))}(Xn,l):l||ne:l||Ee,n.filter=D(n.filter||(3>r.distr?A:Ce)),n.font=yr(n.font),n.labelFont=yr(n.labelFont),n._size=n.size(o,null,e,0),n._space=n._rotate=n._incrs=n._found=n._splits=n._values=null,n._size>0&&(Cr[e]=!0)}})),t?t instanceof HTMLElement?(t.appendChild(F),nl()):t(o,nl):nl(),o}Sr.assign=J,Sr.fmtNum=v,Sr.rangeNum=u,Sr.rangeLog=l,Sr.rangeAsinh=a,Sr.orient=Ue,Sr.join=function(n,e){for(var r=new Set,t=0;n.length>t;t++)for(var l=n[t][0],a=l.length,i=0;a>i;i++)r.add(l[i]);for(var o=[Array.from(r).sort(((n,e)=>n-e))],s=o[0].length,u=new Map,f=0;s>f;f++)u.set(o[0][f],f);for(var c=0;n.length>c;c++)for(var v=n[c],h=v[0],d=1;v.length>d;d++){for(var p=v[d],m=Array(s).fill(void 0),g=e?e[c][d]:1,x=[],w=0;p.length>w;w++){var b=p[w],_=u.get(h[w]);null===b?0!=g&&(m[_]=b,2==g&&x.push(_)):m[_]=b}q(m,x,s),o.push(m)}return o},Sr.fmtDate=Rn,Sr.tzDate=function(n,e){var r;return"UTC"==e||"Etc/UTC"==e?r=new Date(+n+6e4*n.getTimezoneOffset()):e==Ln?r=n:(r=new Date(n.toLocaleString("en-US",{timeZone:e}))).setMilliseconds(n.getMilliseconds()),r},Sr.sync=je,Sr.addGap=qe,Sr.clipGaps=Je;var Er=Sr.paths={};return Er.linear=ur,Er.stepped=function(n){var r=f(n.align,1),t=f(n.ascDesc,!1);return(n,l,a,i)=>Ue(n,l,((o,s,u,f,c,v,h,d,p,m,g)=>{var x=o.pxRound,w=0==f.ori?Qe:$e,b={stroke:new Path2D,fill:null,clip:null,band:null,gaps:[],flags:1},_=b.stroke,k=1*f.dir*(0==f.ori?1:-1);a=e(u,a,i,1),i=e(u,a,i,-1);var y=b.gaps,M=!1,S=x(h(u[1==k?a:i],c,g,p)),E=x(v(s[1==k?a:i],f,m,d)),z=E;w(_,E,S);for(var D=1==k?a:i;D>=a&&i>=D;D+=k){var T=u[D],A=x(v(s[D],f,m,d));if(null!=T){var P=x(h(T,c,g,p));if(M){if(qe(y,z,A),S!=P){var W=o.width*Z/2,Y=y[y.length-1];Y[0]+=t||1==r?W:-W,Y[1]-=t||-1==r?W:-W}M=!1}1==r?w(_,A,S):w(_,z,P),w(_,A,P),S=P,z=A}else null===T&&(qe(y,z,A),M=!0)}if(null!=o.fill){var C=b.fill=new Path2D(_),F=x(h(o.fillTo(n,l,o.min,o.max),c,g,p));w(C,z,F),w(C,E,F)}return o.spanGaps||(b.clip=Je(y,f.ori,d,p,m,g)),n.bands.length>0&&(b.band=Ve(n,l,a,i,_)),b}))},Er.bars=function(n){var r=f((n=n||G).size,[.6,S,1]),t=n.align||0,l=(n.gap||0)*Z,a=1-r[0],i=f(r[1],S)*Z,o=f(r[2],1)*Z;return(n,r,s,u)=>Ue(n,r,((f,c,v,h,d,m,g,x,_,k,y)=>{var M=f.pxRound,S=h.dir*(0==h.ori?1:-1),z=0==h.ori?nr:er,D=k;if(c.length>1)for(var T=1,A=1/0;c.length>T;T++){var P=p(c[T]-c[T-1]);A>P&&(A=P,D=p(m(c[T],h,k,x)-m(c[T-1],h,k,x)))}var W,Y=D*a,C=g(f.fillTo(n,r,f.min,f.max),d,y,_),F=M(f.width*Z),H=M(w(i,b(o,D-Y))-F-l),R=(0==t?H/2:t==S?0:H)-t*S*l/2,L={stroke:new Path2D,fill:null,clip:null,band:null,gaps:null,flags:3},I=n.bands.length>0;I&&(L.band=new Path2D,W=E(g(d.max,d,y,_),.5));for(var G=L.stroke,B=L.band,N=1==S?s:u;N>=s&&u>=N;N+=S){var O=v[N];if(null==O){if(!I)continue;var j=e(v,1==S?s:u,N,-S),U=e(v,N,1==S?u:s,S),V=v[j];O=V+(N-j)/(U-j)*(v[U]-V)}var J=m(2==h.distr?N:c[N],h,k,x),q=g(O,d,y,_),K=M(J-R),X=M(b(q,C)),Q=M(w(q,C)),$=X-Q;null!=v[N]&&z(G,K,Q,H,$),I&&(X=Q,z(B,K,Q=W,H,$=X-Q))}return null!=f.fill&&(L.fill=new Path2D(G)),L}))},Er.spline=function(){return function(n){return(r,t,l,a)=>Ue(r,t,((i,o,s,u,f,c,v,h,d,p,m)=>{var g,x,w,b=i.pxRound;0==u.ori?(g=Ke,w=Qe,x=lr):(g=Xe,w=$e,x=ar);var _=1*u.dir*(0==u.ori?1:-1);l=e(s,l,a,1),a=e(s,l,a,-1);for(var k=[],y=!1,M=b(c(o[1==_?l:a],u,p,h)),S=M,E=[],z=[],D=1==_?l:a;D>=l&&a>=D;D+=_){var T=s[D],A=c(o[D],u,p,h);null!=T?(y&&(qe(k,S,A),y=!1),E.push(S=A),z.push(v(s[D],f,m,d))):null===T&&(qe(k,S,A),y=!0)}var P={stroke:n(E,z,g,w,x,b),fill:null,clip:null,band:null,gaps:k,flags:1},W=P.stroke;if(null!=i.fill&&null!=W){var Y=P.fill=new Path2D(W),C=b(v(i.fillTo(r,t,i.min,i.max),f,m,d));w(Y,S,C),w(Y,M,C)}return i.spanGaps||(P.clip=Je(k,u.ori,h,d,p,m)),r.bands.length>0&&(P.band=Ve(r,t,l,a,W)),P}))}(fr)},Sr}();
