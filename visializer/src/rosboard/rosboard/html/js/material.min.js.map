{"version": 3, "sources": ["tabs.js", "layout.js", "mdlComponentHandler.js", "rAF.js", "button.js", "checkbox.js", "icon-toggle.js", "menu.js", "progress.js", "radio.js", "slider.js", "snackbar.js", "spinner.js", "switch.js", "textfield.js", "tooltip.js", "data-table.js", "ripple.js"], "names": ["MaterialTab", "tab", "ctx", "element_", "classList", "contains", "CssClasses_", "MDL_JS_RIPPLE_EFFECT", "ripple<PERSON><PERSON>r", "document", "createElement", "add", "MDL_RIPPLE_CONTAINER", "ripple", "MDL_RIPPLE", "append<PERSON><PERSON><PERSON>", "addEventListener", "e", "getAttribute", "char<PERSON>t", "preventDefault", "href", "split", "panel", "querySelector", "resetTabState_", "resetPanelState_", "ACTIVE_CLASS", "MaterialLayoutTab", "tabs", "panels", "layout", "selectTab", "content_", "IS_ACTIVE", "tabBar_", "JS_RIPPLE_EFFECT", "RIPPLE_CONTAINER", "RIPPLE", "TAB_MANUAL_SWITCH", "show", "componentHandler", "upgradeDom", "optJsClass", "optCssClass", "upgradeElement", "element", "upgradeElements", "elements", "upgradeAllRegistered", "registerUpgradedCallback", "jsClass", "callback", "register", "config", "downgradeElements", "nodes", "findRegisteredClass_", "name", "optReplace", "i", "registeredComponents_", "length", "className", "getUpgradedListOfElement_", "dataUpgraded", "isElementUpgraded_", "upgradedList", "indexOf", "createEvent_", "eventType", "bubbles", "cancelable", "window", "CustomEvent", "ev", "createEvent", "initEvent", "upgradeDomInternal", "cssClass", "registeredClass", "querySelectorAll", "n", "upgradeElementInternal", "Element", "Error", "upgradingEv", "dispatchEvent", "defaultPrevented", "classesToUpgrade", "push", "for<PERSON>ach", "component", "setAttribute", "join", "instance", "classConstructor", "componentConfigProperty_", "createdComponents_", "j", "m", "callbacks", "widget", "upgradedEv", "upgradeElementsInternal", "Array", "isArray", "prototype", "slice", "call", "HTMLElement", "children", "registerInternal", "widgetMissing", "newConfig", "constructor", "classAsString", "item", "hasOwnProperty", "found", "registerUpgradedCallbackInternal", "regClass", "upgradeAllRegisteredInternal", "deconstructComponentInternal", "componentIndex", "splice", "upgrades", "componentPlace", "downgradeNodesInternal", "downgradeNode", "node", "filter", "NodeList", "Node", "ComponentConfigPublic", "ComponentConfig", "Component", "documentElement", "Date", "now", "getTime", "vendors", "requestAnimationFrame", "vp", "cancelAnimationFrame", "test", "navigator", "userAgent", "lastTime", "nextTime", "Math", "max", "setTimeout", "clearTimeout", "MaterialButton", "this", "init", "Constant_", "RIPPLE_EFFECT", "<PERSON><PERSON><PERSON><PERSON>_", "event", "blur", "disable", "disabled", "enable", "rippleElement_", "boundRippleBlurHandler", "bind", "boundButtonBlurHandler", "MaterialCheckbox", "TINY_TIMEOUT", "INPUT", "BOX_OUTLINE", "FOCUS_HELPER", "TICK_OUTLINE", "RIPPLE_IGNORE_EVENTS", "RIPPLE_CENTER", "IS_FOCUSED", "IS_DISABLED", "IS_CHECKED", "IS_UPGRADED", "onChange_", "updateClasses_", "onFocus_", "onBlur_", "remove", "onMouseUp_", "blur_", "checkDisabled", "checkToggleState", "inputElement_", "checked", "check", "uncheck", "boxOutline", "tick<PERSON><PERSON><PERSON>", "tickOutline", "rippleContainerElement_", "boundRippleMouseUp", "boundInputOnChange", "boundInputOnFocus", "boundInputOnBlur", "boundElementMouseUp", "MaterialIconToggle", "boundElementOnMouseUp", "MaterialMenu", "TRANSITION_DURATION_SECONDS", "TRANSITION_DURATION_FRACTION", "CLOSE_TIMEOUT", "Keycodes_", "ENTER", "ESCAPE", "SPACE", "UP_ARROW", "DOWN_ARROW", "CONTAINER", "OUTLINE", "ITEM", "ITEM_RIPPLE_CONTAINER", "IS_VISIBLE", "IS_ANIMATING", "BOTTOM_LEFT", "BOTTOM_RIGHT", "TOP_LEFT", "TOP_RIGHT", "UNALIGNED", "container", "parentElement", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "container_", "outline", "outline_", "forElId", "forEl", "getElementById", "forElement_", "handleForClick_", "handleForKeyboardEvent_", "items", "boundItemKeydown_", "handleItemKeyboardEvent_", "boundItemClick_", "handleItemClick_", "tabIndex", "evt", "rect", "getBoundingClientRect", "forRect", "style", "right", "top", "offsetTop", "offsetHeight", "left", "offsetLeft", "bottom", "toggle", "keyCode", "focus", "currentIndex", "target", "MouseEvent", "click", "hide", "hasAttribute", "stopPropagation", "closing_", "applyClip_", "height", "width", "clip", "removeAnimationEndListener_", "addAnimationEndListener_", "transitionDuration", "itemDelay", "transitionDelay", "parentNode", "removeEventListener", "removeProperty", "MaterialProgress", "INDETERMINATE_CLASS", "setProgress", "p", "progressbar_", "<PERSON><PERSON><PERSON><PERSON>", "bufferbar_", "auxbar_", "el", "MaterialRadio", "JS_RADIO", "RADIO_BTN", "RADIO_OUTER_CIRCLE", "RADIO_INNER_CIRCLE", "radios", "getElementsByClassName", "button", "btnElement_", "onMouseup_", "boundChangeHandler_", "boundFocusHandler_", "boundBlurHandler_", "boundMouseUpHandler_", "outerCircle", "innerCircle", "MaterialSlider", "isIE_", "msPointer<PERSON><PERSON><PERSON>", "IE_CONTAINER", "SLIDER_CONTAINER", "BACKGROUND_FLEX", "BACKGROUND_LOWER", "BACKGROUND_UPPER", "IS_LOWEST_VALUE", "onInput_", "updateValueStyles_", "onContainerMouseDown_", "newEvent", "buttons", "clientX", "clientY", "y", "fraction", "value", "min", "backgroundLower_", "flex", "webkitFlex", "backgroundUpper_", "change", "containerIE", "backgroundFlex", "boundInputHandler", "boundChangeHandler", "boundMouseUpHandler", "boundContainerMouseDownHandler", "MaterialSnackbar", "textElement_", "cssClasses_", "MESSAGE", "actionElement_", "ACTION", "active", "actionHandler_", "undefined", "message_", "actionText_", "queuedNotifications_", "setActionHidden_", "ANIMATION_LENGTH", "SNACKBAR", "ACTIVE", "displaySnackbar_", "textContent", "cleanup_", "timeout_", "showSnackbar", "data", "checkQueue_", "shift", "Boolean", "removeAttribute", "MaterialSpinner", "MDL_SPINNER_LAYER_COUNT", "MDL_SPINNER_LAYER", "MDL_SPINNER_CIRCLE_CLIPPER", "MDL_SPINNER_CIRCLE", "MDL_SPINNER_GAP_PATCH", "MDL_SPINNER_LEFT", "MDL_SPINNER_RIGHT", "create<PERSON><PERSON>er", "index", "layer", "leftClipper", "gapPatch", "rightClipper", "circleOwners", "circle", "stop", "start", "MaterialSwitch", "TRACK", "THUMB", "on", "off", "track", "thumb", "focusHelper", "boundFocusHandler", "boundBlurHandler", "MaterialTabs", "TAB_CLASS", "PANEL_CLASS", "UPGRADED_CLASS", "MDL_JS_RIPPLE_EFFECT_IGNORE_EVENTS", "initTabs_", "tabs_", "panels_", "k", "MaterialTextfield", "maxRows", "NO_MAX_ROWS", "MAX_ROWS_ATTRIBUTE", "LABEL", "IS_DIRTY", "IS_INVALID", "HAS_PLACEHOLDER", "onKeyDown_", "currentRowCount", "onReset_", "checkValidity", "checkDirty", "checkFocus", "input_", "validity", "valid", "label_", "parseInt", "isNaN", "boundUpdateClassesHandler", "boundResetHandler", "boundKeyDownHandler", "invalid", "MaterialTooltip", "BOTTOM", "LEFT", "RIGHT", "TOP", "handleMouseEnter_", "props", "marginLeft", "offsetWidth", "marginTop", "hideTooltip_", "boundMouseEnterHandler", "boundMouseLeaveAndScrollHandler", "MaterialLayout", "MAX_WIDTH", "TAB_SCROLL_PIXELS", "RESIZE_TIMEOUT", "MENU_ICON", "CHEVRON_LEFT", "CHEVRON_RIGHT", "Mode_", "STANDARD", "SEAMED", "WATERFALL", "SCROLL", "HEADER", "DRAWER", "CONTENT", "DRAWER_BTN", "ICON", "HEADER_SEAMED", "HEADER_WATERFALL", "HEADER_SCROLL", "FIXED_HEADER", "OBFUSCATOR", "TAB_BAR", "TAB_CONTAINER", "TAB", "TAB_BAR_BUTTON", "TAB_BAR_LEFT_BUTTON", "TAB_BAR_RIGHT_BUTTON", "PANEL", "HAS_DRAWER", "HAS_TABS", "HAS_SCROLLING_HEADER", "CASTING_SHADOW", "IS_COMPACT", "IS_SMALL_SCREEN", "IS_DRAWER_OPEN", "ON_LARGE_SCREEN", "ON_SMALL_SCREEN", "contentScrollHandler_", "header_", "headerVisible", "scrollTop", "keyboardEventHandler_", "drawer_", "toggle<PERSON>rawer", "screenSizeHandler_", "screenSizeMediaQuery_", "matches", "obfuscator_", "drawerToggleHandler_", "type", "headerTransitionEndHandler_", "headerClickHandler_", "tabBar", "drawerButton", "focusedElement", "directChildren", "childNodes", "numC<PERSON><PERSON>n", "c", "child", "persisted", "overflowY", "mode", "drawerButtonIcon", "innerHTML", "<PERSON><PERSON><PERSON><PERSON>", "obfuscator", "matchMedia", "addListener", "tabContainer", "leftButton", "leftButtonIcon", "scrollLeft", "rightB<PERSON>on", "rightButtonIcon", "tabUpdate<PERSON>andler", "scrollWidth", "windowResizeHandler", "resizeTimeoutId_", "MaterialDataTable", "DATA_TABLE", "SELECTABLE", "SELECT_ELEMENT", "IS_SELECTED", "selectRow_", "checkbox", "row", "opt_rows", "createCheckbox_", "label", "labelClasses", "firstHeader", "bodyRows", "footRows", "rows", "concat", "th", "headerCheckbox", "firstCell", "td", "nodeName", "toUpperCase", "rowCheckbox", "MaterialRipple", "INITIAL_SCALE", "INITIAL_SIZE", "INITIAL_OPACITY", "FINAL_OPACITY", "FINAL_SCALE", "RIPPLE_EFFECT_IGNORE_EVENTS", "down<PERSON><PERSON><PERSON>_", "boundHeight", "boundWidth", "rippleSize_", "sqrt", "ignoringMouseDown_", "frameCount", "getFrameCount", "setFrameCount", "x", "bound", "currentTarget", "round", "touches", "setRippleXY", "setRippleStyles", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upH<PERSON><PERSON>_", "detail", "recentering", "frameCount_", "x_", "y_", "boundDownHandler", "boundUpHandler", "fC", "getRippleElement", "newX", "newY", "transformString", "scale", "size", "offset", "webkitTransform", "msTransform", "transform"], "mappings": ";;;;;;;wBA6GA,SAAAA,GAAAC,EAAAC,GACA,GAAAD,EAAA,CACA,GAAAC,EAAAC,SAAAC,UAAAC,SAAAH,EAAAI,YAAAC,sBAAA,CACA,GAAAC,GAAAC,SAAAC,cAAA,OACAF,GAAAJ,UAAAO,IAAAT,EAAAI,YAAAM,sBACAJ,EAAAJ,UAAAO,IAAAT,EAAAI,YAAAC,qBACA,IAAAM,GAAAJ,SAAAC,cAAA,OACAG,GAAAT,UAAAO,IAAAT,EAAAI,YAAAQ,YACAN,EAAAO,YAAAF,GACAZ,EAAAc,YAAAP,GAEAP,EAAAe,iBAAA,QAAA,SAAAC,GACA,GAAA,MAAAhB,EAAAiB,aAAA,QAAAC,OAAA,GAAA,CACAF,EAAAG,gBACA,IAAAC,GAAApB,EAAAoB,KAAAC,MAAA,KAAA,GACAC,EAAArB,EAAAC,SAAAqB,cAAA,IAAAH,EACAnB,GAAAuB,iBACAvB,EAAAwB,mBACAzB,EAAAG,UAAAO,IAAAT,EAAAI,YAAAqB,cACAJ,EAAAnB,UAAAO,IAAAT,EAAAI,YAAAqB,kBCwTA,QAAAC,GAAA3B,EAAA4B,EAAAC,EAAAC,GAIA,QAAAC,KACA,GAAAX,GAAApB,EAAAoB,KAAAC,MAAA,KAAA,GACAC,EAAAQ,EAAAE,SAAAT,cAAA,IAAAH,EACAU,GAAAN,eAAAI,GACAE,EAAAL,iBAAAI,GACA7B,EAAAG,UAAAO,IAAAoB,EAAAzB,YAAA4B,WACAX,EAAAnB,UAAAO,IAAAoB,EAAAzB,YAAA4B,WAEA,GAAAH,EAAAI,QAAA/B,UAAAC,SAAA0B,EAAAzB,YAAA8B,kBAAA,CACA,GAAA5B,GAAAC,SAAAC,cAAA,OACAF,GAAAJ,UAAAO,IAAAoB,EAAAzB,YAAA+B,kBACA7B,EAAAJ,UAAAO,IAAAoB,EAAAzB,YAAA8B,iBACA,IAAAvB,GAAAJ,SAAAC,cAAA,OACAG,GAAAT,UAAAO,IAAAoB,EAAAzB,YAAAgC,QACA9B,EAAAO,YAAAF,GACAZ,EAAAc,YAAAP,GAEAuB,EAAAI,QAAA/B,UAAAC,SAAA0B,EAAAzB,YAAAiC,oBACAtC,EAAAe,iBAAA,QAAA,SAAAC,GACA,MAAAhB,EAAAiB,aAAA,QAAAC,OAAA,KACAF,EAAAG,iBACAY,OAIA/B,EAAAuC,KAAAR,ECzbA,GAAAS,IAUAC,WAAA,SAAAC,EAAAC,KAQAC,eAAA,SAAAC,EAAAH,KAOAI,gBAAA,SAAAC,KAKAC,qBAAA,aAWAC,yBAAA,SAAAC,EAAAC,KAMAC,SAAA,SAAAC,KAMAC,kBAAA,SAAAC,KAGAf,GAAA,WAoBA,QAAAgB,GAAAC,EAAAC,GACA,IAAA,GAAAC,GAAA,EAAAA,EAAAC,EAAAC,OAAAF,IACA,GAAAC,EAAAD,GAAAG,YAAAL,EAIA,MAHA,mBAAAC,KACAE,EAAAD,GAAAD,GAEAE,EAAAD,EAGA,QAAA,EAUA,QAAAI,GAAAlB,GACA,GAAAmB,GAAAnB,EAAA5B,aAAA,gBAEA,OAAA,QAAA+C,GAAA,IAAAA,EAAA3C,MAAA,KAYA,QAAA4C,GAAApB,EAAAK,GACA,GAAAgB,GAAAH,EAAAlB,EACA,OAAAqB,GAAAC,QAAAjB,MAAA,EAWA,QAAAkB,GAAAC,EAAAC,EAAAC,GACA,GAAA,eAAAC,SAAA,kBAAAA,QAAAC,YACA,MAAA,IAAAA,aAAAJ,GACAC,QAAAA,EACAC,WAAAA,GAGA,IAAAG,GAAAlE,SAAAmE,YAAA,SAEA,OADAD,GAAAE,UAAAP,EAAAC,EAAAC,GACAG,EAaA,QAAAG,GAAAnC,EAAAC,GACA,GAAA,mBAAAD,IACA,mBAAAC,GACA,IAAA,GAAAgB,GAAA,EAAAA,EAAAC,EAAAC,OAAAF,IACAkB,EAAAjB,EAAAD,GAAAG,UACAF,EAAAD,GAAAmB,cAEA,CACA,GAAA5B,GAAA,CACA,IAAA,mBAAAP,GAAA,CACA,GAAAoC,GAAAvB,EAAAN,EACA6B,KACApC,EAAAoC,EAAAD,UAKA,IAAA,GADA/B,GAAAvC,SAAAwE,iBAAA,IAAArC,GACAsC,EAAA,EAAAA,EAAAlC,EAAAc,OAAAoB,IACAC,EAAAnC,EAAAkC,GAAA/B,IAYA,QAAAgC,GAAArC,EAAAH,GAEA,KAAA,gBAAAG,IAAAA,YAAAsC,UACA,KAAA,IAAAC,OAAA,oDAGA,IAAAC,GAAAjB,EAAA,0BAAA,GAAA,EAEA,IADAvB,EAAAyC,cAAAD,IACAA,EAAAE,iBAAA,CAIA,GAAArB,GAAAH,EAAAlB,GACA2C,IAGA,IAAA9C,EAUAuB,EAAApB,EAAAH,IACA8C,EAAAC,KAAAjC,EAAAd,QAXA,CACA,GAAAvC,GAAA0C,EAAA1C,SACAyD,GAAA8B,QAAA,SAAAC,GAEAxF,EAAAC,SAAAuF,EAAAb,WACAU,EAAArB,QAAAwB,MAAA,IACA1B,EAAApB,EAAA8C,EAAA7B,YACA0B,EAAAC,KAAAE,KAQA,IAAA,GAAAZ,GAAApB,EAAA,EAAAsB,EAAAO,EAAA3B,OAAAF,EAAAsB,EAAAtB,IAAA,CAEA,GADAoB,EAAAS,EAAA7B,IACAoB,EAiBA,KAAA,IAAAK,OACA,6DAhBAlB,GAAAuB,KAAAV,EAAAjB,WACAjB,EAAA+C,aAAA,gBAAA1B,EAAA2B,KAAA,KACA,IAAAC,GAAA,GAAAf,GAAAgB,iBAAAlD,EACAiD,GAAAE,GAAAjB,EACAkB,EAAAR,KAAAK,EAEA,KAAA,GAAAI,GAAA,EAAAC,EAAApB,EAAAqB,UAAAvC,OAAAqC,EAAAC,EAAAD,IACAnB,EAAAqB,UAAAF,GAAArD,EAGAkC,GAAAsB,SAEAxD,EAAAkC,EAAAjB,WAAAgC,EAOA,IAAAQ,GAAAlC,EAAA,yBAAA,GAAA,EACAvB,GAAAyC,cAAAgB,KAUA,QAAAC,GAAAxD,GACAyD,MAAAC,QAAA1D,KAEAA,EADAA,YAAAoC,UACApC,GAEAyD,MAAAE,UAAAC,MAAAC,KAAA7D,GAGA,KAAA,GAAAF,GAAAc,EAAA,EAAAsB,EAAAlC,EAAAc,OAAAF,EAAAsB,EAAAtB,IACAd,EAAAE,EAAAY,GACAd,YAAAgE,eACA3B,EAAArC,GACAA,EAAAiE,SAAAjD,OAAA,GACA0C,EAAA1D,EAAAiE,WAWA,QAAAC,GAAA1D,GAKA,GAAA2D,GAAA,mBAAA3D,GAAAgD,QACA,mBAAAhD,GAAA,OACAgD,GAAA,CAEAW,KACAX,EAAAhD,EAAAgD,QAAAhD,EAAA,OAGA,IAAA4D,IACAlB,iBAAA1C,EAAA6D,aAAA7D,EAAA,YACAS,UAAAT,EAAA8D,eAAA9D,EAAA,cACAyB,SAAAzB,EAAAyB,UAAAzB,EAAA,SACAgD,OAAAA,EACAD,aAYA,IATAxC,EAAA8B,QAAA,SAAA0B,GACA,GAAAA,EAAAtC,WAAAmC,EAAAnC,SACA,KAAA,IAAAM,OAAA,sDAAAgC,EAAAtC,SAEA,IAAAsC,EAAAtD,YAAAmD,EAAAnD,UACA,KAAA,IAAAsB,OAAA,wDAIA/B,EAAA6D,YAAAR,UACAW,eAAArB,GACA,KAAA,IAAAZ,OACA,uCAAAY,EACA,0BAGA,IAAAsB,GAAA9D,EAAAH,EAAA8D,cAAAF,EAEAK,IACA1D,EAAA6B,KAAAwB,GAcA,QAAAM,GAAArE,EAAAC,GACA,GAAAqE,GAAAhE,EAAAN,EACAsE,IACAA,EAAApB,UAAAX,KAAAtC,GAQA,QAAAsE,KACA,IAAA,GAAAxC,GAAA,EAAAA,EAAArB,EAAAC,OAAAoB,IACAJ,EAAAjB,EAAAqB,GAAAnB,WAWA,QAAA4D,GAAA/B,GACA,GAAAA,EAAA,CACA,GAAAgC,GAAA1B,EAAA9B,QAAAwB,EACAM,GAAA2B,OAAAD,EAAA,EAEA,IAAAE,GAAAlC,EAAAzF,SAAAe,aAAA,iBAAAI,MAAA,KACAyG,EAAAD,EAAA1D,QAAAwB,EAAAK,GAAAmB,cACAU,GAAAD,OAAAE,EAAA,GACAnC,EAAAzF,SAAA0F,aAAA,gBAAAiC,EAAAhC,KAAA,KAEA,IAAAnB,GAAAN,EAAA,2BAAA,GAAA,EACAuB,GAAAzF,SAAAoF,cAAAZ,IASA,QAAAqD,GAAAxE,GAKA,GAAAyE,GAAA,SAAAC,GACAhC,EAAAiC,OAAA,SAAAd,GACA,MAAAA,GAAAlH,WAAA+H,IACAvC,QAAAgC,GAEA,IAAAnE,YAAAiD,QAAAjD,YAAA4E,UACA,IAAA,GAAAlD,GAAA,EAAAA,EAAA1B,EAAAM,OAAAoB,IACA+C,EAAAzE,EAAA0B,QAEA,CAAA,KAAA1B,YAAA6E,OAGA,KAAA,IAAAhD,OAAA,oDAFA4C,GAAAzE,IA7TA,GAAAK,MAGAqC,KAEAD,EAAA,6BAgUA,QACAvD,WAAAoC,EACAjC,eAAAsC,EACApC,gBAAAyD,EACAvD,qBAAAyE,EACAxE,yBAAAsE,EACAnE,SAAA2D,EACAzD,kBAAAyE,MAeAvF,EAAA6F,sBAcA7F,EAAA8F,gBAcA9F,EAAA+F,UAIA/F,EAAA,WAAAA,EAAAC,WACAD,EAAA,eAAAA,EAAAI,eACAJ,EAAA,gBAAAA,EAAAM,gBACAN,EAAA,qBACAA,EAAAQ,qBACAR,EAAA,yBACAA,EAAAS,yBACAT,EAAA,SAAAA,EAAAY,SACAZ,EAAA,kBAAAA,EAAAc,kBACAkB,OAAAhC,iBAAAA,EACAgC,OAAA,iBAAAhC,EAEAgC,OAAAzD,iBAAA,OAAA,WAQA,aAAAP,UAAAC,cAAA,QACA,iBAAAD,WACA,oBAAAgE,SAAAgC,MAAAE,UAAAhB,SACAlF,SAAAgI,gBAAArI,UAAAO,IAAA,UACA8B,EAAAQ,yBAKAR,EAAAI,eAAA,aAIAJ,EAAAY,SAAA,gBC7eAqF,KAAAC,MAKAD,KAAAC,IAAA,WACA,OAAA,GAAAD,OAAAE,WAEAF,KAAA,IAAAA,KAAAC,IAMA,KAAA,GAJAE,IACA,SACA,OAEAjF,EAAA,EAAAA,EAAAiF,EAAA/E,SAAAW,OAAAqE,wBAAAlF,EAAA,CACA,GAAAmF,GAAAF,EAAAjF,EACAa,QAAAqE,sBAAArE,OAAAsE,EAAA,yBACAtE,OAAAuE,qBAAAvE,OAAAsE,EAAA,yBAAAtE,OAAAsE,EAAA,+BACAtE,OAAA,sBAAAA,OAAAqE,sBACArE,OAAA,qBAAAA,OAAAuE,qBAEA,GAAA,uBAAAC,KAAAxE,OAAAyE,UAAAC,aAAA1E,OAAAqE,wBAAArE,OAAAuE,qBAAA,CACA,GAAAI,GAAA,CAKA3E,QAAAqE,sBAAA,SAAA1F,GACA,GAAAuF,GAAAD,KAAAC,MACAU,EAAAC,KAAAC,IAAAH,EAAA,GAAAT,EACA,OAAAa,YAAA,WACApG,EAAAgG,EAAAC,IACAA,EAAAV,IAEAlE,OAAAuE,qBAAAS,aACAhF,OAAA,sBAAAA,OAAAqE,sBACArE,OAAA,qBAAAA,OAAAuE,qBCpBA,GAAAU,GAAA,SAAA5G,GACA6G,KAAAxJ,SAAA2C,EAEA6G,KAAAC,OAEAnF,QAAA,eAAAiF,EAOAA,EAAA/C,UAAAkD,aASAH,EAAA/C,UAAArG,aACAwJ,cAAA,uBACAzH,iBAAA,+BACAC,OAAA,cAQAoH,EAAA/C,UAAAoD,aAAA,SAAAC,GACAA,GACAL,KAAAxJ,SAAA8J,QASAP,EAAA/C,UAAAuD,QAAA,WACAP,KAAAxJ,SAAAgK,UAAA,GAEAT,EAAA/C,UAAA,QAAA+C,EAAA/C,UAAAuD,QAMAR,EAAA/C,UAAAyD,OAAA,WACAT,KAAAxJ,SAAAgK,UAAA,GAEAT,EAAA/C,UAAA,OAAA+C,EAAA/C,UAAAyD,OAIAV,EAAA/C,UAAAiD,KAAA,WACA,GAAAD,KAAAxJ,SAAA,CACA,GAAAwJ,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAwJ,eAAA,CACA,GAAAtJ,GAAAC,SAAAC,cAAA,OACAF,GAAAJ,UAAAO,IAAAgJ,KAAArJ,YAAA+B,kBACAsH,KAAAU,eAAA5J,SAAAC,cAAA,QACAiJ,KAAAU,eAAAjK,UAAAO,IAAAgJ,KAAArJ,YAAAgC,QACA9B,EAAAO,YAAA4I,KAAAU,gBACAV,KAAAW,uBAAAX,KAAAI,aAAAQ,KAAAZ,MACAA,KAAAU,eAAArJ,iBAAA,UAAA2I,KAAAW,wBACAX,KAAAxJ,SAAAY,YAAAP,GAEAmJ,KAAAa,uBAAAb,KAAAI,aAAAQ,KAAAZ,MACAA,KAAAxJ,SAAAa,iBAAA,UAAA2I,KAAAa,wBACAb,KAAAxJ,SAAAa,iBAAA,aAAA2I,KAAAa,0BAKA/H,EAAAY,UACA8D,YAAAuC,EACAtC,cAAA,iBACArC,SAAA,gBACAuB,QAAA,GCjFA,IAAAmE,GAAA,SAAA3H,GACA6G,KAAAxJ,SAAA2C,EAEA6G,KAAAC,OAEAnF,QAAA,iBAAAgG,EAOAA,EAAA9D,UAAAkD,WAAAa,aAAA,MASAD,EAAA9D,UAAArG,aACAqK,MAAA,sBACAC,YAAA,4BACAC,aAAA,6BACAC,aAAA,6BACAhB,cAAA,uBACAiB,qBAAA,sCACA1I,iBAAA,iCACA2I,cAAA,qBACA1I,OAAA,aACA2I,WAAA,aACAC,YAAA,cACAC,WAAA,aACAC,YAAA,eAQAX,EAAA9D,UAAA0E,UAAA,SAAArB,GACAL,KAAA2B,kBAQAb,EAAA9D,UAAA4E,SAAA,SAAAvB,GACAL,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA2K,aAQAR,EAAA9D,UAAA6E,QAAA,SAAAxB,GACAL,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA2K,aAQAR,EAAA9D,UAAA+E,WAAA,SAAA1B,GACAL,KAAAgC,SAOAlB,EAAA9D,UAAA2E,eAAA,WACA3B,KAAAiC,gBACAjC,KAAAkC,oBAOApB,EAAA9D,UAAAgF,MAAA,WAGAlH,OAAA+E,WAAA,WACAG,KAAAmC,cAAA7B,QACAM,KAAAZ,MAAAA,KAAAE,UAAAa,eAQAD,EAAA9D,UAAAkF,iBAAA,WACAlC,KAAAmC,cAAAC,QACApC,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA6K,YAEAxB,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA6K,aAGAV,EAAA9D,UAAA,iBAAA8D,EAAA9D,UAAAkF,iBAMApB,EAAA9D,UAAAiF,cAAA,WACAjC,KAAAmC,cAAA3B,SACAR,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA4K,aAEAvB,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA4K,cAGAT,EAAA9D,UAAA,cAAA8D,EAAA9D,UAAAiF,cAMAnB,EAAA9D,UAAAuD,QAAA,WACAP,KAAAmC,cAAA3B,UAAA,EACAR,KAAA2B,kBAEAb,EAAA9D,UAAA,QAAA8D,EAAA9D,UAAAuD,QAMAO,EAAA9D,UAAAyD,OAAA,WACAT,KAAAmC,cAAA3B,UAAA,EACAR,KAAA2B,kBAEAb,EAAA9D,UAAA,OAAA8D,EAAA9D,UAAAyD,OAMAK,EAAA9D,UAAAqF,MAAA,WACArC,KAAAmC,cAAAC,SAAA,EACApC,KAAA2B,kBAEAb,EAAA9D,UAAA,MAAA8D,EAAA9D,UAAAqF,MAMAvB,EAAA9D,UAAAsF,QAAA,WACAtC,KAAAmC,cAAAC,SAAA,EACApC,KAAA2B,kBAEAb,EAAA9D,UAAA,QAAA8D,EAAA9D,UAAAsF,QAIAxB,EAAA9D,UAAAiD,KAAA,WACA,GAAAD,KAAAxJ,SAAA,CACAwJ,KAAAmC,cAAAnC,KAAAxJ,SAAAqB,cAAA,IAAAmI,KAAArJ,YAAAqK,MACA,IAAAuB,GAAAzL,SAAAC,cAAA,OACAwL,GAAA9L,UAAAO,IAAAgJ,KAAArJ,YAAAsK,YACA,IAAAuB,GAAA1L,SAAAC,cAAA,OACAyL,GAAA/L,UAAAO,IAAAgJ,KAAArJ,YAAAuK,aACA,IAAAuB,GAAA3L,SAAAC,cAAA,OAKA,IAJA0L,EAAAhM,UAAAO,IAAAgJ,KAAArJ,YAAAwK,cACAoB,EAAAnL,YAAAqL,GACAzC,KAAAxJ,SAAAY,YAAAoL,GACAxC,KAAAxJ,SAAAY,YAAAmL,GACAvC,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAwJ,eAAA,CACAH,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAAyK,sBACApB,KAAA0C,wBAAA5L,SAAAC,cAAA,QACAiJ,KAAA0C,wBAAAjM,UAAAO,IAAAgJ,KAAArJ,YAAA+B,kBACAsH,KAAA0C,wBAAAjM,UAAAO,IAAAgJ,KAAArJ,YAAAwJ,eACAH,KAAA0C,wBAAAjM,UAAAO,IAAAgJ,KAAArJ,YAAA0K,eACArB,KAAA2C,mBAAA3C,KAAA+B,WAAAnB,KAAAZ,MACAA,KAAA0C,wBAAArL,iBAAA,UAAA2I,KAAA2C,mBACA,IAAAzL,GAAAJ,SAAAC,cAAA,OACAG,GAAAT,UAAAO,IAAAgJ,KAAArJ,YAAAgC,QACAqH,KAAA0C,wBAAAtL,YAAAF,GACA8I,KAAAxJ,SAAAY,YAAA4I,KAAA0C,yBAEA1C,KAAA4C,mBAAA5C,KAAA0B,UAAAd,KAAAZ,MACAA,KAAA6C,kBAAA7C,KAAA4B,SAAAhB,KAAAZ,MACAA,KAAA8C,iBAAA9C,KAAA6B,QAAAjB,KAAAZ,MACAA,KAAA+C,oBAAA/C,KAAA+B,WAAAnB,KAAAZ,MACAA,KAAAmC,cAAA9K,iBAAA,SAAA2I,KAAA4C,oBACA5C,KAAAmC,cAAA9K,iBAAA,QAAA2I,KAAA6C,mBACA7C,KAAAmC,cAAA9K,iBAAA,OAAA2I,KAAA8C,kBACA9C,KAAAxJ,SAAAa,iBAAA,UAAA2I,KAAA+C,qBACA/C,KAAA2B,iBACA3B,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA8K,eAKA3I,EAAAY,UACA8D,YAAAsD,EACArD,cAAA,mBACArC,SAAA,kBACAuB,QAAA,GC9MA,IAAAqG,GAAA,SAAA7J,GACA6G,KAAAxJ,SAAA2C,EAEA6G,KAAAC,OAEAnF,QAAA,mBAAAkI,EAOAA,EAAAhG,UAAAkD,WAAAa,aAAA,MASAiC,EAAAhG,UAAArG,aACAqK,MAAA,yBACAvI,iBAAA,uBACA2I,qBAAA,sCACA1I,iBAAA,oCACA2I,cAAA,qBACA1I,OAAA,aACA2I,WAAA,aACAC,YAAA,cACAC,WAAA,cAQAwB,EAAAhG,UAAA0E,UAAA,SAAArB,GACAL,KAAA2B,kBAQAqB,EAAAhG,UAAA4E,SAAA,SAAAvB,GACAL,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA2K,aAQA0B,EAAAhG,UAAA6E,QAAA,SAAAxB,GACAL,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA2K,aAQA0B,EAAAhG,UAAA+E,WAAA,SAAA1B,GACAL,KAAAgC,SAOAgB,EAAAhG,UAAA2E,eAAA,WACA3B,KAAAiC,gBACAjC,KAAAkC,oBAOAc,EAAAhG,UAAAgF,MAAA,WAGAlH,OAAA+E,WAAA,WACAG,KAAAmC,cAAA7B,QACAM,KAAAZ,MAAAA,KAAAE,UAAAa,eAQAiC,EAAAhG,UAAAkF,iBAAA,WACAlC,KAAAmC,cAAAC,QACApC,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA6K,YAEAxB,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA6K,aAGAwB,EAAAhG,UAAA,iBAAAgG,EAAAhG,UAAAkF,iBAMAc,EAAAhG,UAAAiF,cAAA,WACAjC,KAAAmC,cAAA3B,SACAR,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA4K,aAEAvB,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA4K,cAGAyB,EAAAhG,UAAA,cAAAgG,EAAAhG,UAAAiF,cAMAe,EAAAhG,UAAAuD,QAAA,WACAP,KAAAmC,cAAA3B,UAAA,EACAR,KAAA2B,kBAEAqB,EAAAhG,UAAA,QAAAgG,EAAAhG,UAAAuD,QAMAyC,EAAAhG,UAAAyD,OAAA,WACAT,KAAAmC,cAAA3B,UAAA,EACAR,KAAA2B,kBAEAqB,EAAAhG,UAAA,OAAAgG,EAAAhG,UAAAyD,OAMAuC,EAAAhG,UAAAqF,MAAA,WACArC,KAAAmC,cAAAC,SAAA,EACApC,KAAA2B,kBAEAqB,EAAAhG,UAAA,MAAAgG,EAAAhG,UAAAqF,MAMAW,EAAAhG,UAAAsF,QAAA,WACAtC,KAAAmC,cAAAC,SAAA,EACApC,KAAA2B,kBAEAqB,EAAAhG,UAAA,QAAAgG,EAAAhG,UAAAsF,QAIAU,EAAAhG,UAAAiD,KAAA,WACA,GAAAD,KAAAxJ,SAAA,CAEA,GADAwJ,KAAAmC,cAAAnC,KAAAxJ,SAAAqB,cAAA,IAAAmI,KAAArJ,YAAAqK,OACAhB,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAA8B,kBAAA,CACAuH,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAAyK,sBACApB,KAAA0C,wBAAA5L,SAAAC,cAAA,QACAiJ,KAAA0C,wBAAAjM,UAAAO,IAAAgJ,KAAArJ,YAAA+B,kBACAsH,KAAA0C,wBAAAjM,UAAAO,IAAAgJ,KAAArJ,YAAA8B,kBACAuH,KAAA0C,wBAAAjM,UAAAO,IAAAgJ,KAAArJ,YAAA0K,eACArB,KAAA2C,mBAAA3C,KAAA+B,WAAAnB,KAAAZ,MACAA,KAAA0C,wBAAArL,iBAAA,UAAA2I,KAAA2C,mBACA,IAAAzL,GAAAJ,SAAAC,cAAA,OACAG,GAAAT,UAAAO,IAAAgJ,KAAArJ,YAAAgC,QACAqH,KAAA0C,wBAAAtL,YAAAF,GACA8I,KAAAxJ,SAAAY,YAAA4I,KAAA0C,yBAEA1C,KAAA4C,mBAAA5C,KAAA0B,UAAAd,KAAAZ,MACAA,KAAA6C,kBAAA7C,KAAA4B,SAAAhB,KAAAZ,MACAA,KAAA8C,iBAAA9C,KAAA6B,QAAAjB,KAAAZ,MACAA,KAAAiD,sBAAAjD,KAAA+B,WAAAnB,KAAAZ,MACAA,KAAAmC,cAAA9K,iBAAA,SAAA2I,KAAA4C,oBACA5C,KAAAmC,cAAA9K,iBAAA,QAAA2I,KAAA6C,mBACA7C,KAAAmC,cAAA9K,iBAAA,OAAA2I,KAAA8C,kBACA9C,KAAAxJ,SAAAa,iBAAA,UAAA2I,KAAAiD,uBACAjD,KAAA2B,iBACA3B,KAAAxJ,SAAAC,UAAAO,IAAA,iBAKA8B,EAAAY,UACA8D,YAAAwF,EACAvF,cAAA,qBACArC,SAAA,qBACAuB,QAAA,GCjMA,IAAAuG,GAAA,SAAA/J,GACA6G,KAAAxJ,SAAA2C,EAEA6G,KAAAC,OAEAnF,QAAA,aAAAoI,EAOAA,EAAAlG,UAAAkD,WAEAiD,4BAAA,GAEAC,6BAAA,GAGAC,cAAA,KAQAH,EAAAlG,UAAAsG,WACAC,MAAA,GACAC,OAAA,GACAC,MAAA,GACAC,SAAA,GACAC,WAAA,IAUAT,EAAAlG,UAAArG,aACAiN,UAAA,sBACAC,QAAA,oBACAC,KAAA,iBACAC,sBAAA,kCACA5D,cAAA,uBACAiB,qBAAA,sCACAzI,OAAA,aAEA8I,YAAA,cACAuC,WAAA,aACAC,aAAA,eAEAC,YAAA,wBAEAC,aAAA,yBACAC,SAAA,qBACAC,UAAA,sBACAC,UAAA,uBAKApB,EAAAlG,UAAAiD,KAAA,WACA,GAAAD,KAAAxJ,SAAA,CAEA,GAAA+N,GAAAzN,SAAAC,cAAA,MACAwN,GAAA9N,UAAAO,IAAAgJ,KAAArJ,YAAAiN,WACA5D,KAAAxJ,SAAAgO,cAAAC,aAAAF,EAAAvE,KAAAxJ,UACAwJ,KAAAxJ,SAAAgO,cAAAE,YAAA1E,KAAAxJ,UACA+N,EAAAnN,YAAA4I,KAAAxJ,UACAwJ,KAAA2E,WAAAJ,CAEA,IAAAK,GAAA9N,SAAAC,cAAA,MACA6N,GAAAnO,UAAAO,IAAAgJ,KAAArJ,YAAAkN,SACA7D,KAAA6E,SAAAD,EACAL,EAAAE,aAAAG,EAAA5E,KAAAxJ,SAEA,IAAAsO,GAAA9E,KAAAxJ,SAAAe,aAAA,QAAAyI,KAAAxJ,SAAAe,aAAA,gBACAwN,EAAA,IACAD,KACAC,EAAAjO,SAAAkO,eAAAF,GACAC,IACA/E,KAAAiF,YAAAF,EACAA,EAAA1N,iBAAA,QAAA2I,KAAAkF,gBAAAtE,KAAAZ,OACA+E,EAAA1N,iBAAA,UAAA2I,KAAAmF,wBAAAvE,KAAAZ,QAGA,IAAAoF,GAAApF,KAAAxJ,SAAA8E,iBAAA,IAAA0E,KAAArJ,YAAAmN,KACA9D,MAAAqF,kBAAArF,KAAAsF,yBAAA1E,KAAAZ,MACAA,KAAAuF,gBAAAvF,KAAAwF,iBAAA5E,KAAAZ,KACA,KAAA,GAAA/F,GAAA,EAAAA,EAAAmL,EAAAjL,OAAAF,IAEAmL,EAAAnL,GAAA5C,iBAAA,QAAA2I,KAAAuF,iBAEAH,EAAAnL,GAAAwL,SAAA,KAEAL,EAAAnL,GAAA5C,iBAAA,UAAA2I,KAAAqF,kBAGA,IAAArF,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAwJ,eAEA,IADAH,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAAyK,sBACAnH,EAAA,EAAAA,EAAAmL,EAAAjL,OAAAF,IAAA,CACA,GAAAyD,GAAA0H,EAAAnL,GACApD,EAAAC,SAAAC,cAAA,OACAF,GAAAJ,UAAAO,IAAAgJ,KAAArJ,YAAAoN,sBACA,IAAA7M,GAAAJ,SAAAC,cAAA,OACAG,GAAAT,UAAAO,IAAAgJ,KAAArJ,YAAAgC,QACA9B,EAAAO,YAAAF,GACAwG,EAAAtG,YAAAP,GACA6G,EAAAjH,UAAAO,IAAAgJ,KAAArJ,YAAAwJ,eAIAH,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAuN,cACAlE,KAAA6E,SAAApO,UAAAO,IAAAgJ,KAAArJ,YAAAuN,aAEAlE,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAwN,eACAnE,KAAA6E,SAAApO,UAAAO,IAAAgJ,KAAArJ,YAAAwN,cAEAnE,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAyN,WACApE,KAAA6E,SAAApO,UAAAO,IAAAgJ,KAAArJ,YAAAyN,UAEApE,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAA0N,YACArE,KAAA6E,SAAApO,UAAAO,IAAAgJ,KAAArJ,YAAA0N,WAEArE,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAA2N,YACAtE,KAAA6E,SAAApO,UAAAO,IAAAgJ,KAAArJ,YAAA2N,WAEAC,EAAA9N,UAAAO,IAAAgJ,KAAArJ,YAAA8K,eAUAyB,EAAAlG,UAAAkI,gBAAA,SAAAQ,GACA,GAAA1F,KAAAxJ,UAAAwJ,KAAAiF,YAAA,CACA,GAAAU,GAAA3F,KAAAiF,YAAAW,wBACAC,EAAA7F,KAAAiF,YAAAT,cAAAoB,uBACA5F,MAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAA2N,aACAtE,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAwN,eAEAnE,KAAA2E,WAAAmB,MAAAC,MAAAF,EAAAE,MAAAJ,EAAAI,MAAA,KACA/F,KAAA2E,WAAAmB,MAAAE,IAAAhG,KAAAiF,YAAAgB,UAAAjG,KAAAiF,YAAAiB,aAAA,MACAlG,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAyN,WAEApE,KAAA2E,WAAAmB,MAAAK,KAAAnG,KAAAiF,YAAAmB,WAAA,KACApG,KAAA2E,WAAAmB,MAAAO,OAAAR,EAAAQ,OAAAV,EAAAK,IAAA,MACAhG,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAA0N,YAEArE,KAAA2E,WAAAmB,MAAAC,MAAAF,EAAAE,MAAAJ,EAAAI,MAAA,KACA/F,KAAA2E,WAAAmB,MAAAO,OAAAR,EAAAQ,OAAAV,EAAAK,IAAA,OAGAhG,KAAA2E,WAAAmB,MAAAK,KAAAnG,KAAAiF,YAAAmB,WAAA,KACApG,KAAA2E,WAAAmB,MAAAE,IAAAhG,KAAAiF,YAAAgB,UAAAjG,KAAAiF,YAAAiB,aAAA,OAGAlG,KAAAsG,OAAAZ,IAQAxC,EAAAlG,UAAAmI,wBAAA,SAAAO,GACA,GAAA1F,KAAAxJ,UAAAwJ,KAAA2E,YAAA3E,KAAAiF,YAAA,CACA,GAAAG,GAAApF,KAAAxJ,SAAA8E,iBAAA,IAAA0E,KAAArJ,YAAAmN,KAAA,mBACAsB,IAAAA,EAAAjL,OAAA,GAAA6F,KAAA2E,WAAAlO,UAAAC,SAAAsJ,KAAArJ,YAAAqN,cACA0B,EAAAa,UAAAvG,KAAAsD,UAAAI,UACAgC,EAAAjO,iBACA2N,EAAAA,EAAAjL,OAAA,GAAAqM,SACAd,EAAAa,UAAAvG,KAAAsD,UAAAK,aACA+B,EAAAjO,iBACA2N,EAAA,GAAAoB,YAWAtD,EAAAlG,UAAAsI,yBAAA,SAAAI,GACA,GAAA1F,KAAAxJ,UAAAwJ,KAAA2E,WAAA,CACA,GAAAS,GAAApF,KAAAxJ,SAAA8E,iBAAA,IAAA0E,KAAArJ,YAAAmN,KAAA,mBACA,IAAAsB,GAAAA,EAAAjL,OAAA,GAAA6F,KAAA2E,WAAAlO,UAAAC,SAAAsJ,KAAArJ,YAAAqN,YAAA,CACA,GAAAyC,GAAA3J,MAAAE,UAAAC,MAAAC,KAAAkI,GAAA3K,QAAAiL,EAAAgB,OACA,IAAAhB,EAAAa,UAAAvG,KAAAsD,UAAAI,SACAgC,EAAAjO,iBACAgP,EAAA,EACArB,EAAAqB,EAAA,GAAAD,QAEApB,EAAAA,EAAAjL,OAAA,GAAAqM,YAEA,IAAAd,EAAAa,UAAAvG,KAAAsD,UAAAK,WACA+B,EAAAjO,iBACA2N,EAAAjL,OAAAsM,EAAA,EACArB,EAAAqB,EAAA,GAAAD,QAEApB,EAAA,GAAAoB,YAEA,IAAAd,EAAAa,UAAAvG,KAAAsD,UAAAG,OAAAiC,EAAAa,UAAAvG,KAAAsD,UAAAC,MAAA,CACAmC,EAAAjO,gBAEA,IAAAH,GAAA,GAAAqP,YAAA,YACAjB,GAAAgB,OAAA9K,cAAAtE,GACAA,EAAA,GAAAqP,YAAA,WACAjB,EAAAgB,OAAA9K,cAAAtE,GAEAoO,EAAAgB,OAAAE,YACAlB,GAAAa,UAAAvG,KAAAsD,UAAAE,SACAkC,EAAAjO,iBACAuI,KAAA6G,WAWA3D,EAAAlG,UAAAwI,iBAAA,SAAAE,GACAA,EAAAgB,OAAAI,aAAA,YACApB,EAAAqB,mBAGA/G,KAAAgH,UAAA,EACAlM,OAAA+E,WAAA,SAAA6F,GACA1F,KAAA6G,OACA7G,KAAAgH,UAAA,GACApG,KAAAZ,MAAAA,KAAAE,UAAAmD,iBAYAH,EAAAlG,UAAAiK,WAAA,SAAAC,EAAAC,GACAnH,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAA2N,WAEAtE,KAAAxJ,SAAAsP,MAAAsB,KAAA,GACApH,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAwN,cAEAnE,KAAAxJ,SAAAsP,MAAAsB,KAAA,UAAAD,EAAA,QAAAA,EAAA,MACAnH,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAyN,UAEApE,KAAAxJ,SAAAsP,MAAAsB,KAAA,QAAAF,EAAA,QAAAA,EAAA,QACAlH,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAA0N,WAEArE,KAAAxJ,SAAAsP,MAAAsB,KAAA,QAAAF,EAAA,MAAAC,EAAA,MAAAD,EAAA,MAAAC,EAAA,MAGAnH,KAAAxJ,SAAAsP,MAAAsB,KAAA,IASAlE,EAAAlG,UAAAqK,4BAAA,SAAA3B,GACAA,EAAAgB,OAAAjQ,UAAAqL,OAAAoB,EAAAlG,UAAArG,YAAAsN,eAOAf,EAAAlG,UAAAsK,yBAAA,WACAtH,KAAAxJ,SAAAa,iBAAA,gBAAA2I,KAAAqH,6BACArH,KAAAxJ,SAAAa,iBAAA,sBAAA2I,KAAAqH,8BAOAnE,EAAAlG,UAAAnE,KAAA,SAAA6M,GACA,GAAA1F,KAAAxJ,UAAAwJ,KAAA2E,YAAA3E,KAAA6E,SAAA,CAEA,GAAAqC,GAAAlH,KAAAxJ,SAAAoP,wBAAAsB,OACAC,EAAAnH,KAAAxJ,SAAAoP,wBAAAuB,KAEAnH,MAAA2E,WAAAmB,MAAAqB,MAAAA,EAAA,KACAnH,KAAA2E,WAAAmB,MAAAoB,OAAAA,EAAA,KACAlH,KAAA6E,SAAAiB,MAAAqB,MAAAA,EAAA,KACAnH,KAAA6E,SAAAiB,MAAAoB,OAAAA,EAAA,IAKA,KAAA,GAJAK,GAAAvH,KAAAE,UAAAiD,4BAAAnD,KAAAE,UAAAkD,6BAGAgC,EAAApF,KAAAxJ,SAAA8E,iBAAA,IAAA0E,KAAArJ,YAAAmN,MACA7J,EAAA,EAAAA,EAAAmL,EAAAjL,OAAAF,IAAA,CACA,GAAAuN,GAAA,IAEAA,GADAxH,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAyN,WAAApE,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAA0N,YACA6C,EAAA9B,EAAAnL,GAAAgM,UAAAb,EAAAnL,GAAAiM,cAAAgB,EAAAK,EAAA,IAEAnC,EAAAnL,GAAAgM,UAAAiB,EAAAK,EAAA,IAEAnC,EAAAnL,GAAA6L,MAAA2B,gBAAAD,EAGAxH,KAAAiH,WAAAC,EAAAC,GAGArM,OAAAqE,sBAAA,WACAa,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAAsN,cACAjE,KAAAxJ,SAAAsP,MAAAsB,KAAA,UAAAD,EAAA,MAAAD,EAAA,QACAlH,KAAA2E,WAAAlO,UAAAO,IAAAgJ,KAAArJ,YAAAqN,aACApD,KAAAZ,OAEAA,KAAAsH,0BAEA,IAAA7N,GAAA,SAAAnC,GAOAA,IAAAoO,GAAA1F,KAAAgH,UAAA1P,EAAAoP,OAAAgB,aAAA1H,KAAAxJ,WACAM,SAAA6Q,oBAAA,QAAAlO,GACAuG,KAAA6G,SAEAjG,KAAAZ,KACAlJ,UAAAO,iBAAA,QAAAoC,KAGAyJ,EAAAlG,UAAA,KAAAkG,EAAAlG,UAAAnE,KAMAqK,EAAAlG,UAAA6J,KAAA,WACA,GAAA7G,KAAAxJ,UAAAwJ,KAAA2E,YAAA3E,KAAA6E,SAAA,CAGA,IAAA,GAFAO,GAAApF,KAAAxJ,SAAA8E,iBAAA,IAAA0E,KAAArJ,YAAAmN,MAEA7J,EAAA,EAAAA,EAAAmL,EAAAjL,OAAAF,IACAmL,EAAAnL,GAAA6L,MAAA8B,eAAA,mBAGA,IAAAjC,GAAA3F,KAAAxJ,SAAAoP,wBACAsB,EAAAvB,EAAAuB,OACAC,EAAAxB,EAAAwB,KAGAnH,MAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAAsN,cACAjE,KAAAiH,WAAAC,EAAAC,GACAnH,KAAA2E,WAAAlO,UAAAqL,OAAA9B,KAAArJ,YAAAqN,YAEAhE,KAAAsH,6BAGApE,EAAAlG,UAAA,KAAAkG,EAAAlG,UAAA6J,KAMA3D,EAAAlG,UAAAsJ,OAAA,SAAAZ,GACA1F,KAAA2E,WAAAlO,UAAAC,SAAAsJ,KAAArJ,YAAAqN,YACAhE,KAAA6G,OAEA7G,KAAAnH,KAAA6M,IAGAxC,EAAAlG,UAAA,OAAAkG,EAAAlG,UAAAsJ,OAGAxN,EAAAY,UACA8D,YAAA0F,EACAzF,cAAA,eACArC,SAAA,cACAuB,QAAA,GCvYA,IAAAkL,GAAA,SAAA1O,GACA6G,KAAAxJ,SAAA2C,EAEA6G,KAAAC,OAEAnF,QAAA,iBAAA+M,EAOAA,EAAA7K,UAAAkD,aASA2H,EAAA7K,UAAArG,aAAAmR,oBAAA,+BAOAD,EAAA7K,UAAA+K,YAAA,SAAAC,GACAhI,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAmR,uBAGA9H,KAAAiI,aAAAnC,MAAAqB,MAAAa,EAAA,MAEAH,EAAA7K,UAAA,YAAA6K,EAAA7K,UAAA+K,YAOAF,EAAA7K,UAAAkL,UAAA,SAAAF,GACAhI,KAAAmI,WAAArC,MAAAqB,MAAAa,EAAA,IACAhI,KAAAoI,QAAAtC,MAAAqB,MAAA,IAAAa,EAAA,KAEAH,EAAA7K,UAAA,UAAA6K,EAAA7K,UAAAkL,UAIAL,EAAA7K,UAAAiD,KAAA,WACA,GAAAD,KAAAxJ,SAAA,CACA,GAAA6R,GAAAvR,SAAAC,cAAA,MACAsR,GAAAjO,UAAA,uBACA4F,KAAAxJ,SAAAY,YAAAiR,GACArI,KAAAiI,aAAAI,EACAA,EAAAvR,SAAAC,cAAA,OACAsR,EAAAjO,UAAA,qBACA4F,KAAAxJ,SAAAY,YAAAiR,GACArI,KAAAmI,WAAAE,EACAA,EAAAvR,SAAAC,cAAA,OACAsR,EAAAjO,UAAA,kBACA4F,KAAAxJ,SAAAY,YAAAiR,GACArI,KAAAoI,QAAAC,EACArI,KAAAiI,aAAAnC,MAAAqB,MAAA,KACAnH,KAAAmI,WAAArC,MAAAqB,MAAA,OACAnH,KAAAoI,QAAAtC,MAAAqB,MAAA,KACAnH,KAAAxJ,SAAAC,UAAAO,IAAA,iBAKA8B,EAAAY,UACA8D,YAAAqK,EACApK,cAAA,mBACArC,SAAA,kBACAuB,QAAA,GC3EA,IAAA2L,GAAA,SAAAnP,GACA6G,KAAAxJ,SAAA2C,EAEA6G,KAAAC,OAEAnF,QAAA,cAAAwN,EAOAA,EAAAtL,UAAAkD,WAAAa,aAAA,MASAuH,EAAAtL,UAAArG,aACA2K,WAAA,aACAC,YAAA,cACAC,WAAA,aACAC,YAAA,cACA8G,SAAA,eACAC,UAAA,oBACAC,mBAAA,0BACAC,mBAAA,0BACAvI,cAAA,uBACAiB,qBAAA,sCACA1I,iBAAA,8BACA2I,cAAA,qBACA1I,OAAA,cAQA2P,EAAAtL,UAAA0E,UAAA,SAAArB,GAIA,IAAA,GADAsI,GAAA7R,SAAA8R,uBAAA5I,KAAArJ,YAAA4R,UACAtO,EAAA,EAAAA,EAAA0O,EAAAxO,OAAAF,IAAA,CACA,GAAA4O,GAAAF,EAAA1O,GAAApC,cAAA,IAAAmI,KAAArJ,YAAA6R,UAEAK,GAAAtR,aAAA,UAAAyI,KAAA8I,YAAAvR,aAAA,SACA,mBAAAoR,GAAA1O,GAAA,eACA0O,EAAA1O,GAAA,cAAA0H,mBAWA2G,EAAAtL,UAAA4E,SAAA,SAAAvB,GACAL,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA2K,aAQAgH,EAAAtL,UAAA6E,QAAA,SAAAxB,GACAL,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA2K,aAQAgH,EAAAtL,UAAA+L,WAAA,SAAA1I,GACAL,KAAAgC,SAOAsG,EAAAtL,UAAA2E,eAAA,WACA3B,KAAAiC,gBACAjC,KAAAkC,oBAOAoG,EAAAtL,UAAAgF,MAAA,WAGAlH,OAAA+E,WAAA,WACAG,KAAA8I,YAAAxI,QACAM,KAAAZ,MAAAA,KAAAE,UAAAa,eAQAuH,EAAAtL,UAAAiF,cAAA,WACAjC,KAAA8I,YAAAtI,SACAR,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA4K,aAEAvB,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA4K,cAGA+G,EAAAtL,UAAA,cAAAsL,EAAAtL,UAAAiF,cAMAqG,EAAAtL,UAAAkF,iBAAA,WACAlC,KAAA8I,YAAA1G,QACApC,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA6K,YAEAxB,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA6K,aAGA8G,EAAAtL,UAAA,iBAAAsL,EAAAtL,UAAAkF,iBAMAoG,EAAAtL,UAAAuD,QAAA,WACAP,KAAA8I,YAAAtI,UAAA,EACAR,KAAA2B,kBAEA2G,EAAAtL,UAAA,QAAAsL,EAAAtL,UAAAuD,QAMA+H,EAAAtL,UAAAyD,OAAA,WACAT,KAAA8I,YAAAtI,UAAA,EACAR,KAAA2B,kBAEA2G,EAAAtL,UAAA,OAAAsL,EAAAtL,UAAAyD,OAMA6H,EAAAtL,UAAAqF,MAAA,WACArC,KAAA8I,YAAA1G,SAAA,EACApC,KAAA0B,UAAA,OAEA4G,EAAAtL,UAAA,MAAAsL,EAAAtL,UAAAqF,MAMAiG,EAAAtL,UAAAsF,QAAA,WACAtC,KAAA8I,YAAA1G,SAAA,EACApC,KAAA0B,UAAA,OAEA4G,EAAAtL,UAAA,QAAAsL,EAAAtL,UAAAsF,QAIAgG,EAAAtL,UAAAiD,KAAA,WACA,GAAAD,KAAAxJ,SAAA,CACAwJ,KAAA8I,YAAA9I,KAAAxJ,SAAAqB,cAAA,IAAAmI,KAAArJ,YAAA6R,WACAxI,KAAAgJ,oBAAAhJ,KAAA0B,UAAAd,KAAAZ,MACAA,KAAAiJ,mBAAAjJ,KAAA0B,UAAAd,KAAAZ,MACAA,KAAAkJ,kBAAAlJ,KAAA6B,QAAAjB,KAAAZ,MACAA,KAAAmJ,qBAAAnJ,KAAA+I,WAAAnI,KAAAZ,KACA,IAAAoJ,GAAAtS,SAAAC,cAAA,OACAqS,GAAA3S,UAAAO,IAAAgJ,KAAArJ,YAAA8R,mBACA,IAAAY,GAAAvS,SAAAC,cAAA,OACAsS,GAAA5S,UAAAO,IAAAgJ,KAAArJ,YAAA+R,oBACA1I,KAAAxJ,SAAAY,YAAAgS,GACApJ,KAAAxJ,SAAAY,YAAAiS,EACA,IAAAxS,EACA,IAAAmJ,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAwJ,eAAA,CACAH,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAAyK,sBACAvK,EAAAC,SAAAC,cAAA,QACAF,EAAAJ,UAAAO,IAAAgJ,KAAArJ,YAAA+B,kBACA7B,EAAAJ,UAAAO,IAAAgJ,KAAArJ,YAAAwJ,eACAtJ,EAAAJ,UAAAO,IAAAgJ,KAAArJ,YAAA0K,eACAxK,EAAAQ,iBAAA,UAAA2I,KAAAmJ,qBACA,IAAAjS,GAAAJ,SAAAC,cAAA,OACAG,GAAAT,UAAAO,IAAAgJ,KAAArJ,YAAAgC,QACA9B,EAAAO,YAAAF,GACA8I,KAAAxJ,SAAAY,YAAAP,GAEAmJ,KAAA8I,YAAAzR,iBAAA,SAAA2I,KAAAgJ,qBACAhJ,KAAA8I,YAAAzR,iBAAA,QAAA2I,KAAAiJ,oBACAjJ,KAAA8I,YAAAzR,iBAAA,OAAA2I,KAAAkJ,mBACAlJ,KAAAxJ,SAAAa,iBAAA,UAAA2I,KAAAmJ,sBACAnJ,KAAA2B,iBACA3B,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA8K,eAKA3I,EAAAY,UACA8D,YAAA8K,EACA7K,cAAA,gBACArC,SAAA,eACAuB,QAAA,GCtNA,IAAA2M,GAAA,SAAAnQ,GACA6G,KAAAxJ,SAAA2C,EAEA6G,KAAAuJ,MAAAzO,OAAAyE,UAAAiK,iBAEAxJ,KAAAC,OAEAnF,QAAA,eAAAwO,EAOAA,EAAAtM,UAAAkD,aASAoJ,EAAAtM,UAAArG,aACA8S,aAAA,2BACAC,iBAAA,wBACAC,gBAAA,8BACAC,iBAAA,+BACAC,iBAAA,+BACAC,gBAAA,kBACArI,YAAA,eAQA6H,EAAAtM,UAAA+M,SAAA,SAAA1J,GACAL,KAAAgK,sBAQAV,EAAAtM,UAAA0E,UAAA,SAAArB,GACAL,KAAAgK,sBAQAV,EAAAtM,UAAA+E,WAAA,SAAA1B,GACAA,EAAAqG,OAAApG,QAYAgJ,EAAAtM,UAAAiN,sBAAA,SAAA5J,GAGA,GAAAA,EAAAqG,SAAA1G,KAAAxJ,SAAAgO,cAAA,CAKAnE,EAAA5I,gBACA,IAAAyS,GAAA,GAAAvD,YAAA,aACAD,OAAArG,EAAAqG,OACAyD,QAAA9J,EAAA8J,QACAC,QAAA/J,EAAA+J,QACAC,QAAArK,KAAAxJ,SAAAoP,wBAAA0E,GAEAtK,MAAAxJ,SAAAoF,cAAAsO,KAOAZ,EAAAtM,UAAAgN,mBAAA,WAEA,GAAAO,IAAAvK,KAAAxJ,SAAAgU,MAAAxK,KAAAxJ,SAAAiU,MAAAzK,KAAAxJ,SAAAoJ,IAAAI,KAAAxJ,SAAAiU,IACA,KAAAF,EACAvK,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAAmT,iBAEA9J,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAAmT,iBAEA9J,KAAAuJ,QACAvJ,KAAA0K,iBAAA5E,MAAA6E,KAAAJ,EACAvK,KAAA0K,iBAAA5E,MAAA8E,WAAAL,EACAvK,KAAA6K,iBAAA/E,MAAA6E,KAAA,EAAAJ,EACAvK,KAAA6K,iBAAA/E,MAAA8E,WAAA,EAAAL,IASAjB,EAAAtM,UAAAuD,QAAA,WACAP,KAAAxJ,SAAAgK,UAAA,GAEA8I,EAAAtM,UAAA,QAAAsM,EAAAtM,UAAAuD,QAMA+I,EAAAtM,UAAAyD,OAAA,WACAT,KAAAxJ,SAAAgK,UAAA,GAEA8I,EAAAtM,UAAA,OAAAsM,EAAAtM,UAAAyD,OAOA6I,EAAAtM,UAAA8N,OAAA,SAAAN,GACA,mBAAAA,KACAxK,KAAAxJ,SAAAgU,MAAAA,GAEAxK,KAAAgK,sBAEAV,EAAAtM,UAAA,OAAAsM,EAAAtM,UAAA8N,OAIAxB,EAAAtM,UAAAiD,KAAA,WACA,GAAAD,KAAAxJ,SAAA,CACA,GAAAwJ,KAAAuJ,MAAA,CAIA,GAAAwB,GAAAjU,SAAAC,cAAA,MACAgU,GAAAtU,UAAAO,IAAAgJ,KAAArJ,YAAA8S,cACAzJ,KAAAxJ,SAAAgO,cAAAC,aAAAsG,EAAA/K,KAAAxJ,UACAwJ,KAAAxJ,SAAAgO,cAAAE,YAAA1E,KAAAxJ,UACAuU,EAAA3T,YAAA4I,KAAAxJ,cACA,CAIA,GAAA+N,GAAAzN,SAAAC,cAAA,MACAwN,GAAA9N,UAAAO,IAAAgJ,KAAArJ,YAAA+S,kBACA1J,KAAAxJ,SAAAgO,cAAAC,aAAAF,EAAAvE,KAAAxJ,UACAwJ,KAAAxJ,SAAAgO,cAAAE,YAAA1E,KAAAxJ,UACA+N,EAAAnN,YAAA4I,KAAAxJ,SACA,IAAAwU,GAAAlU,SAAAC,cAAA,MACAiU,GAAAvU,UAAAO,IAAAgJ,KAAArJ,YAAAgT,iBACApF,EAAAnN,YAAA4T,GACAhL,KAAA0K,iBAAA5T,SAAAC,cAAA,OACAiJ,KAAA0K,iBAAAjU,UAAAO,IAAAgJ,KAAArJ,YAAAiT,kBACAoB,EAAA5T,YAAA4I,KAAA0K,kBACA1K,KAAA6K,iBAAA/T,SAAAC,cAAA,OACAiJ,KAAA6K,iBAAApU,UAAAO,IAAAgJ,KAAArJ,YAAAkT,kBACAmB,EAAA5T,YAAA4I,KAAA6K,kBAEA7K,KAAAiL,kBAAAjL,KAAA+J,SAAAnJ,KAAAZ,MACAA,KAAAkL,mBAAAlL,KAAA0B,UAAAd,KAAAZ,MACAA,KAAAmL,oBAAAnL,KAAA+B,WAAAnB,KAAAZ,MACAA,KAAAoL,+BAAApL,KAAAiK,sBAAArJ,KAAAZ,MACAA,KAAAxJ,SAAAa,iBAAA,QAAA2I,KAAAiL,mBACAjL,KAAAxJ,SAAAa,iBAAA,SAAA2I,KAAAkL,oBACAlL,KAAAxJ,SAAAa,iBAAA,UAAA2I,KAAAmL,qBACAnL,KAAAxJ,SAAAgO,cAAAnN,iBAAA,YAAA2I,KAAAoL,gCACApL,KAAAgK,qBACAhK,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA8K,eAKA3I,EAAAY,UACA8D,YAAA8L,EACA7L,cAAA,iBACArC,SAAA,gBACAuB,QAAA,GC9LA,IAAA0O,GAAA,SAAAlS,GAIA,GAHA6G,KAAAxJ,SAAA2C,EACA6G,KAAAsL,aAAAtL,KAAAxJ,SAAAqB,cAAA,IAAAmI,KAAAuL,YAAAC,SACAxL,KAAAyL,eAAAzL,KAAAxJ,SAAAqB,cAAA,IAAAmI,KAAAuL,YAAAG,SACA1L,KAAAsL,aACA,KAAA,IAAA5P,OAAA,kDAEA,KAAAsE,KAAAyL,eACA,KAAA,IAAA/P,OAAA,kDAEAsE,MAAA2L,QAAA,EACA3L,KAAA4L,eAAAC,OACA7L,KAAA8L,SAAAD,OACA7L,KAAA+L,YAAAF,OACA7L,KAAAgM,wBACAhM,KAAAiM,kBAAA,GAEAnR,QAAA,iBAAAuQ,EAOAA,EAAArO,UAAAkD,WAEAgM,iBAAA,KAUAb,EAAArO,UAAAuO,aACAY,SAAA,eACAX,QAAA,qBACAE,OAAA,uBACAU,OAAA,wBAOAf,EAAArO,UAAAqP,iBAAA,WACArM,KAAAxJ,SAAA0F,aAAA,cAAA;AACA8D,KAAA4L,iBACA5L,KAAAyL,eAAAa,YAAAtM,KAAA+L,YACA/L,KAAAyL,eAAApU,iBAAA,QAAA2I,KAAA4L,gBACA5L,KAAAiM,kBAAA,IAEAjM,KAAAsL,aAAAgB,YAAAtM,KAAA8L,SACA9L,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAAuL,YAAAa,QACApM,KAAAxJ,SAAA0F,aAAA,cAAA,SACA2D,WAAAG,KAAAuM,SAAA3L,KAAAZ,MAAAA,KAAAwM,WAQAnB,EAAArO,UAAAyP,aAAA,SAAAC,GACA,GAAAb,SAAAa,EACA,KAAA,IAAAhR,OAAA,mEAEA,IAAAmQ,SAAAa,EAAA,QACA,KAAA,IAAAhR,OAAA,4CAEA,IAAAgR,EAAA,gBAAAA,EAAA,WACA,KAAA,IAAAhR,OAAA,+CAEAsE,MAAA2L,OACA3L,KAAAgM,qBAAAjQ,KAAA2Q,IAEA1M,KAAA2L,QAAA,EACA3L,KAAA8L,SAAAY,EAAA,QACAA,EAAA,QACA1M,KAAAwM,SAAAE,EAAA,QAEA1M,KAAAwM,SAAA,KAEAE,EAAA,gBACA1M,KAAA4L,eAAAc,EAAA,eAEAA,EAAA,aACA1M,KAAA+L,YAAAW,EAAA,YAEA1M,KAAAqM,qBAGAhB,EAAArO,UAAA,aAAAqO,EAAArO,UAAAyP,aAOApB,EAAArO,UAAA2P,YAAA,WACA3M,KAAAgM,qBAAA7R,OAAA,GACA6F,KAAAyM,aAAAzM,KAAAgM,qBAAAY,UAQAvB,EAAArO,UAAAuP,SAAA,WACAvM,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAAuL,YAAAa,QACAvM,WAAA,WACAG,KAAAxJ,SAAA0F,aAAA,cAAA,QACA8D,KAAAsL,aAAAgB,YAAA,GACAO,QAAA7M,KAAAyL,eAAAlU,aAAA,kBACAyI,KAAAiM,kBAAA,GACAjM,KAAAyL,eAAAa,YAAA,GACAtM,KAAAyL,eAAA9D,oBAAA,QAAA3H,KAAA4L,iBAEA5L,KAAA4L,eAAAC,OACA7L,KAAA8L,SAAAD,OACA7L,KAAA+L,YAAAF,OACA7L,KAAA2L,QAAA,EACA3L,KAAA2M,eACA/L,KAAAZ,MAAAA,KAAAE,UAAAgM,mBAQAb,EAAArO,UAAAiP,iBAAA,SAAAzB,GACAA,EACAxK,KAAAyL,eAAAvP,aAAA,cAAA,QAEA8D,KAAAyL,eAAAqB,gBAAA,gBAKAhU,EAAAY,UACA8D,YAAA6N,EACA5N,cAAA,mBACArC,SAAA,kBACAuB,QAAA,GClJA,IAAAoQ,GAAA,SAAA5T,GACA6G,KAAAxJ,SAAA2C,EAEA6G,KAAAC,OAEAnF,QAAA,gBAAAiS,EAOAA,EAAA/P,UAAAkD,WAAA8M,wBAAA,GASAD,EAAA/P,UAAArG,aACAsW,kBAAA,qBACAC,2BAAA,8BACAC,mBAAA,sBACAC,sBAAA,yBACAC,iBAAA,oBACAC,kBAAA,sBAQAP,EAAA/P,UAAAuQ,YAAA,SAAAC,GACA,GAAAC,GAAA3W,SAAAC,cAAA,MACA0W,GAAAhX,UAAAO,IAAAgJ,KAAArJ,YAAAsW,mBACAQ,EAAAhX,UAAAO,IAAAgJ,KAAArJ,YAAAsW,kBAAA,IAAAO,EACA,IAAAE,GAAA5W,SAAAC,cAAA,MACA2W,GAAAjX,UAAAO,IAAAgJ,KAAArJ,YAAAuW,4BACAQ,EAAAjX,UAAAO,IAAAgJ,KAAArJ,YAAA0W,iBACA,IAAAM,GAAA7W,SAAAC,cAAA,MACA4W,GAAAlX,UAAAO,IAAAgJ,KAAArJ,YAAAyW,sBACA,IAAAQ,GAAA9W,SAAAC,cAAA,MACA6W,GAAAnX,UAAAO,IAAAgJ,KAAArJ,YAAAuW,4BACAU,EAAAnX,UAAAO,IAAAgJ,KAAArJ,YAAA2W,kBAMA,KAAA,GALAO,IACAH,EACAC,EACAC,GAEA3T,EAAA,EAAAA,EAAA4T,EAAA1T,OAAAF,IAAA,CACA,GAAA6T,GAAAhX,SAAAC,cAAA,MACA+W,GAAArX,UAAAO,IAAAgJ,KAAArJ,YAAAwW,oBACAU,EAAA5T,GAAA7C,YAAA0W,GAEAL,EAAArW,YAAAsW,GACAD,EAAArW,YAAAuW,GACAF,EAAArW,YAAAwW,GACA5N,KAAAxJ,SAAAY,YAAAqW,IAEAV,EAAA/P,UAAA,YAAA+P,EAAA/P,UAAAuQ,YAOAR,EAAA/P,UAAA+Q,KAAA,WACA/N,KAAAxJ,SAAAC,UAAAqL,OAAA,cAEAiL,EAAA/P,UAAA,KAAA+P,EAAA/P,UAAA+Q,KAQAhB,EAAA/P,UAAAgR,MAAA,WACAhO,KAAAxJ,SAAAC,UAAAO,IAAA,cAEA+V,EAAA/P,UAAA,MAAA+P,EAAA/P,UAAAgR,MAIAjB,EAAA/P,UAAAiD,KAAA,WACA,GAAAD,KAAAxJ,SAAA,CACA,IAAA,GAAAyD,GAAA,EAAAA,GAAA+F,KAAAE,UAAA8M,wBAAA/S,IACA+F,KAAAuN,YAAAtT,EAEA+F,MAAAxJ,SAAAC,UAAAO,IAAA,iBAKA8B,EAAAY,UACA8D,YAAAuP,EACAtP,cAAA,kBACArC,SAAA,iBACAuB,QAAA,GCrGA,IAAAsR,GAAA,SAAA9U,GACA6G,KAAAxJ,SAAA2C,EAEA6G,KAAAC,OAEAnF,QAAA,eAAAmT,EAOAA,EAAAjR,UAAAkD,WAAAa,aAAA,MASAkN,EAAAjR,UAAArG,aACAqK,MAAA,oBACAkN,MAAA,oBACAC,MAAA,oBACAjN,aAAA,2BACAf,cAAA,uBACAiB,qBAAA,sCACA1I,iBAAA,+BACA2I,cAAA,qBACA1I,OAAA,aACA2I,WAAA,aACAC,YAAA,cACAC,WAAA,cAQAyM,EAAAjR,UAAA0E,UAAA,SAAArB,GACAL,KAAA2B,kBAQAsM,EAAAjR,UAAA4E,SAAA,SAAAvB,GACAL,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA2K,aAQA2M,EAAAjR,UAAA6E,QAAA,SAAAxB,GACAL,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA2K,aAQA2M,EAAAjR,UAAA+E,WAAA,SAAA1B,GACAL,KAAAgC,SAOAiM,EAAAjR,UAAA2E,eAAA,WACA3B,KAAAiC,gBACAjC,KAAAkC,oBAOA+L,EAAAjR,UAAAgF,MAAA,WAGAlH,OAAA+E,WAAA,WACAG,KAAAmC,cAAA7B,QACAM,KAAAZ,MAAAA,KAAAE,UAAAa,eAQAkN,EAAAjR,UAAAiF,cAAA,WACAjC,KAAAmC,cAAA3B,SACAR,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA4K,aAEAvB,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA4K,cAGA0M,EAAAjR,UAAA,cAAAiR,EAAAjR,UAAAiF,cAMAgM,EAAAjR,UAAAkF,iBAAA,WACAlC,KAAAmC,cAAAC,QACApC,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA6K,YAEAxB,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA6K,aAGAyM,EAAAjR,UAAA,iBAAAiR,EAAAjR,UAAAkF,iBAMA+L,EAAAjR,UAAAuD,QAAA,WACAP,KAAAmC,cAAA3B,UAAA,EACAR,KAAA2B,kBAEAsM,EAAAjR,UAAA,QAAAiR,EAAAjR,UAAAuD,QAMA0N,EAAAjR,UAAAyD,OAAA,WACAT,KAAAmC,cAAA3B,UAAA,EACAR,KAAA2B,kBAEAsM,EAAAjR,UAAA,OAAAiR,EAAAjR,UAAAyD,OAMAwN,EAAAjR,UAAAoR,GAAA,WACApO,KAAAmC,cAAAC,SAAA,EACApC,KAAA2B,kBAEAsM,EAAAjR,UAAA,GAAAiR,EAAAjR,UAAAoR,GAMAH,EAAAjR,UAAAqR,IAAA,WACArO,KAAAmC,cAAAC,SAAA,EACApC,KAAA2B,kBAEAsM,EAAAjR,UAAA,IAAAiR,EAAAjR,UAAAqR,IAIAJ,EAAAjR,UAAAiD,KAAA,WACA,GAAAD,KAAAxJ,SAAA,CACAwJ,KAAAmC,cAAAnC,KAAAxJ,SAAAqB,cAAA,IAAAmI,KAAArJ,YAAAqK,MACA,IAAAsN,GAAAxX,SAAAC,cAAA,MACAuX,GAAA7X,UAAAO,IAAAgJ,KAAArJ,YAAAuX,MACA,IAAAK,GAAAzX,SAAAC,cAAA,MACAwX,GAAA9X,UAAAO,IAAAgJ,KAAArJ,YAAAwX,MACA,IAAAK,GAAA1X,SAAAC,cAAA,OAMA,IALAyX,EAAA/X,UAAAO,IAAAgJ,KAAArJ,YAAAuK,cACAqN,EAAAnX,YAAAoX,GACAxO,KAAAxJ,SAAAY,YAAAkX,GACAtO,KAAAxJ,SAAAY,YAAAmX,GACAvO,KAAAmL,oBAAAnL,KAAA+B,WAAAnB,KAAAZ,MACAA,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAwJ,eAAA,CACAH,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAAyK,sBACApB,KAAA0C,wBAAA5L,SAAAC,cAAA,QACAiJ,KAAA0C,wBAAAjM,UAAAO,IAAAgJ,KAAArJ,YAAA+B,kBACAsH,KAAA0C,wBAAAjM,UAAAO,IAAAgJ,KAAArJ,YAAAwJ,eACAH,KAAA0C,wBAAAjM,UAAAO,IAAAgJ,KAAArJ,YAAA0K,eACArB,KAAA0C,wBAAArL,iBAAA,UAAA2I,KAAAmL,oBACA,IAAAjU,GAAAJ,SAAAC,cAAA,OACAG,GAAAT,UAAAO,IAAAgJ,KAAArJ,YAAAgC,QACAqH,KAAA0C,wBAAAtL,YAAAF,GACA8I,KAAAxJ,SAAAY,YAAA4I,KAAA0C,yBAEA1C,KAAAkL,mBAAAlL,KAAA0B,UAAAd,KAAAZ,MACAA,KAAAyO,kBAAAzO,KAAA4B,SAAAhB,KAAAZ,MACAA,KAAA0O,iBAAA1O,KAAA6B,QAAAjB,KAAAZ,MACAA,KAAAmC,cAAA9K,iBAAA,SAAA2I,KAAAkL,oBACAlL,KAAAmC,cAAA9K,iBAAA,QAAA2I,KAAAyO,mBACAzO,KAAAmC,cAAA9K,iBAAA,OAAA2I,KAAA0O,kBACA1O,KAAAxJ,SAAAa,iBAAA,UAAA2I,KAAAmL,qBACAnL,KAAA2B,iBACA3B,KAAAxJ,SAAAC,UAAAO,IAAA,iBAKA8B,EAAAY,UACA8D,YAAAyQ,EACAxQ,cAAA,iBACArC,SAAA,gBACAuB,QAAA,Gb5MA,IAAAgS,GAAA,SAAAxV,GAEA6G,KAAAxJ,SAAA2C,EAEA6G,KAAAC,OAEAnF,QAAA,aAAA6T,EAOAA,EAAA3R,UAAAkD,aASAyO,EAAA3R,UAAArG,aACAiY,UAAA,gBACAC,YAAA,kBACA7W,aAAA,YACA8W,eAAA,cACAlY,qBAAA,uBACAK,qBAAA,6BACAE,WAAA,aACA4X,mCAAA,uCAOAJ,EAAA3R,UAAAgS,UAAA,WACAhP,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAC,uBACAoJ,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAAoY,oCAGA/O,KAAAiP,MAAAjP,KAAAxJ,SAAA8E,iBAAA,IAAA0E,KAAArJ,YAAAiY,WACA5O,KAAAkP,QAAAlP,KAAAxJ,SAAA8E,iBAAA,IAAA0E,KAAArJ,YAAAkY,YAEA,KAAA,GAAA5U,GAAA,EAAAA,EAAA+F,KAAAiP,MAAA9U,OAAAF,IACA,GAAA5D,GAAA2J,KAAAiP,MAAAhV,GAAA+F,KAEAA,MAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAAmY,iBAOAH,EAAA3R,UAAAlF,eAAA,WACA,IAAA,GAAAqX,GAAA,EAAAA,EAAAnP,KAAAiP,MAAA9U,OAAAgV,IACAnP,KAAAiP,MAAAE,GAAA1Y,UAAAqL,OAAA9B,KAAArJ,YAAAqB,eAQA2W,EAAA3R,UAAAjF,iBAAA,WACA,IAAA,GAAAyE,GAAA,EAAAA,EAAAwD,KAAAkP,QAAA/U,OAAAqC,IACAwD,KAAAkP,QAAA1S,GAAA/F,UAAAqL,OAAA9B,KAAArJ,YAAAqB,eAMA2W,EAAA3R,UAAAiD,KAAA,WACAD,KAAAxJ,UACAwJ,KAAAgP,aAoCAlW,EAAAY,UACA8D,YAAAmR,EACAlR,cAAA,eACArC,SAAA,eclHA,IAAAgU,GAAA,SAAAjW,GACA6G,KAAAxJ,SAAA2C,EACA6G,KAAAqP,QAAArP,KAAAE,UAAAoP,YAEAtP,KAAAC,OAEAnF,QAAA,kBAAAsU,EAOAA,EAAApS,UAAAkD,WACAoP,aAAA,EACAC,mBAAA,WAUAH,EAAApS,UAAArG,aACA6Y,MAAA,uBACAxO,MAAA,uBACAyO,SAAA,WACAnO,WAAA,aACAC,YAAA,cACAmO,WAAA,aACAjO,YAAA,cACAkO,gBAAA,mBAQAP,EAAApS,UAAA4S,WAAA,SAAAvP,GACA,GAAAwP,GAAAxP,EAAAqG,OAAA8D,MAAA7S,MAAA,MAAAwC,MACA,MAAAkG,EAAAkG,SACAsJ,GAAA7P,KAAAqP,SACAhP,EAAA5I,kBAUA2X,EAAApS,UAAA4E,SAAA,SAAAvB,GACAL,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA2K,aAQA8N,EAAApS,UAAA6E,QAAA,SAAAxB,GACAL,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA2K,aAQA8N,EAAApS,UAAA8S,SAAA,SAAAzP,GACAL,KAAA2B,kBAOAyN,EAAApS,UAAA2E,eAAA,WACA3B,KAAAiC,gBACAjC,KAAA+P,gBACA/P,KAAAgQ,aACAhQ,KAAAiQ,cAQAb,EAAApS,UAAAiF,cAAA,WACAjC,KAAAkQ,OAAA1P,SACAR,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA4K,aAEAvB,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA4K,cAGA6N,EAAApS,UAAA,cAAAoS,EAAApS,UAAAiF,cAMAmN,EAAApS,UAAAiT,WAAA,WACApD,QAAA7M,KAAAxJ,SAAAqB,cAAA,WACAmI,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA2K,YAEAtB,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA2K,aAGA8N,EAAApS,UAAA,WAAAoS,EAAApS,UAAAiT,WAMAb,EAAApS,UAAA+S,cAAA,WACA/P,KAAAkQ,OAAAC,WACAnQ,KAAAkQ,OAAAC,SAAAC,MACApQ,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA+Y,YAEA1P,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA+Y,cAIAN,EAAApS,UAAA,cAAAoS,EAAApS,UAAA+S,cAMAX,EAAApS,UAAAgT,WAAA,WACAhQ,KAAAkQ,OAAA1F,OAAAxK,KAAAkQ,OAAA1F,MAAArQ,OAAA,EACA6F,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA8Y,UAEAzP,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA8Y,WAGAL,EAAApS,UAAA,WAAAoS,EAAApS,UAAAgT,WAMAZ,EAAApS,UAAAuD,QAAA,WACAP,KAAAkQ,OAAA1P,UAAA,EACAR,KAAA2B,kBAEAyN,EAAApS,UAAA,QAAAoS,EAAApS,UAAAuD,QAMA6O,EAAApS,UAAAyD,OAAA,WACAT,KAAAkQ,OAAA1P,UAAA,EACAR,KAAA2B,kBAEAyN,EAAApS,UAAA,OAAAoS,EAAApS,UAAAyD,OAOA2O,EAAApS,UAAA8N,OAAA,SAAAN,GACAxK,KAAAkQ,OAAA1F,MAAAA,GAAA,GACAxK,KAAA2B,kBAEAyN,EAAApS,UAAA,OAAAoS,EAAApS,UAAA8N,OAIAsE,EAAApS,UAAAiD,KAAA,WACA,GAAAD,KAAAxJ,WACAwJ,KAAAqQ,OAAArQ,KAAAxJ,SAAAqB,cAAA,IAAAmI,KAAArJ,YAAA6Y,OACAxP,KAAAkQ,OAAAlQ,KAAAxJ,SAAAqB,cAAA,IAAAmI,KAAArJ,YAAAqK,OACAhB,KAAAkQ,QAAA,CACAlQ,KAAAkQ,OAAApJ,aAAA9G,KAAAE,UAAAqP,sBACAvP,KAAAqP,QAAAiB,SAAAtQ,KAAAkQ,OAAA3Y,aAAAyI,KAAAE,UAAAqP,oBAAA,IACAgB,MAAAvQ,KAAAqP,WACArP,KAAAqP,QAAArP,KAAAE,UAAAoP,cAGAtP,KAAAkQ,OAAApJ,aAAA,gBACA9G,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAAgZ,iBAEA3P,KAAAwQ,0BAAAxQ,KAAA2B,eAAAf,KAAAZ,MACAA,KAAAyO,kBAAAzO,KAAA4B,SAAAhB,KAAAZ,MACAA,KAAA0O,iBAAA1O,KAAA6B,QAAAjB,KAAAZ,MACAA,KAAAyQ,kBAAAzQ,KAAA8P,SAAAlP,KAAAZ,MACAA,KAAAkQ,OAAA7Y,iBAAA,QAAA2I,KAAAwQ,2BACAxQ,KAAAkQ,OAAA7Y,iBAAA,QAAA2I,KAAAyO,mBACAzO,KAAAkQ,OAAA7Y,iBAAA,OAAA2I,KAAA0O,kBACA1O,KAAAkQ,OAAA7Y,iBAAA,QAAA2I,KAAAyQ,mBACAzQ,KAAAqP,UAAArP,KAAAE,UAAAoP,cAGAtP,KAAA0Q,oBAAA1Q,KAAA4P,WAAAhP,KAAAZ,MACAA,KAAAkQ,OAAA7Y,iBAAA,UAAA2I,KAAA0Q,qBAEA,IAAAC,GAAA3Q,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAA+Y,WACA1P,MAAA2B,iBACA3B,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA8K,aACAkP,GACA3Q,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA+Y,YAEA1P,KAAAkQ,OAAApJ,aAAA,eACA9G,KAAAxJ,SAAAgQ,QACAxG,KAAAiQ,gBAOAnX,EAAAY,UACA8D,YAAA4R,EACA3R,cAAA,oBACArC,SAAA,mBACAuB,QAAA,GC/NA,IAAAiU,GAAA,SAAAzX,GACA6G,KAAAxJ,SAAA2C,EAEA6G,KAAAC,OAEAnF,QAAA,gBAAA8V,EAOAA,EAAA5T,UAAAkD,aASA0Q,EAAA5T,UAAArG,aACA4B,UAAA,YACAsY,OAAA,sBACAC,KAAA,oBACAC,MAAA,qBACAC,IAAA,oBAQAJ,EAAA5T,UAAAiU,kBAAA,SAAA5Q,GACA,GAAA6Q,GAAA7Q,EAAAqG,OAAAd,wBACAO,EAAA+K,EAAA/K,KAAA+K,EAAA/J,MAAA,EACAnB,EAAAkL,EAAAlL,IAAAkL,EAAAhK,OAAA,EACAiK,GAAA,GAAAnR,KAAAxJ,SAAA4a,YAAA,GACAC,GAAA,GAAArR,KAAAxJ,SAAA0P,aAAA,EACAlG,MAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAma,OAAA9Q,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAoa,QACA5K,EAAA+K,EAAA/J,MAAA,EACAnB,EAAAqL,EAAA,GACArR,KAAAxJ,SAAAsP,MAAAE,IAAA,IACAhG,KAAAxJ,SAAAsP,MAAAuL,UAAA,MAEArR,KAAAxJ,SAAAsP,MAAAE,IAAAA,EAAA,KACAhG,KAAAxJ,SAAAsP,MAAAuL,UAAAA,EAAA,OAGAlL,EAAAgL,EAAA,GACAnR,KAAAxJ,SAAAsP,MAAAK,KAAA,IACAnG,KAAAxJ,SAAAsP,MAAAqL,WAAA,MAEAnR,KAAAxJ,SAAAsP,MAAAK,KAAAA,EAAA,KACAnG,KAAAxJ,SAAAsP,MAAAqL,WAAAA,EAAA,MAGAnR,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAqa,KACAhR,KAAAxJ,SAAAsP,MAAAE,IAAAkL,EAAAlL,IAAAhG,KAAAxJ,SAAA0P,aAAA,GAAA,KACAlG,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAoa,OACA/Q,KAAAxJ,SAAAsP,MAAAK,KAAA+K,EAAA/K,KAAA+K,EAAA/J,MAAA,GAAA,KACAnH,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAma,MACA9Q,KAAAxJ,SAAAsP,MAAAK,KAAA+K,EAAA/K,KAAAnG,KAAAxJ,SAAA4a,YAAA,GAAA,KAEApR,KAAAxJ,SAAAsP,MAAAE,IAAAkL,EAAAlL,IAAAkL,EAAAhK,OAAA,GAAA,KAEAlH,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA4B,YAOAqY,EAAA5T,UAAAsU,aAAA,WACAtR,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAA4B,YAKAqY,EAAA5T,UAAAiD,KAAA,WACA,GAAAD,KAAAxJ,SAAA,CACA,GAAAsO,GAAA9E,KAAAxJ,SAAAe,aAAA,QAAAyI,KAAAxJ,SAAAe,aAAA,eACAuN,KACA9E,KAAAiF,YAAAnO,SAAAkO,eAAAF,IAEA9E,KAAAiF,cAEAjF,KAAAiF,YAAA6B,aAAA,aACA9G,KAAAiF,YAAA/I,aAAA,WAAA,KAEA8D,KAAAuR,uBAAAvR,KAAAiR,kBAAArQ,KAAAZ,MACAA,KAAAwR,gCAAAxR,KAAAsR,aAAA1Q,KAAAZ,MACAA,KAAAiF,YAAA5N,iBAAA,aAAA2I,KAAAuR,wBAAA,GACAvR,KAAAiF,YAAA5N,iBAAA,WAAA2I,KAAAuR,wBAAA,GACAvR,KAAAiF,YAAA5N,iBAAA,aAAA2I,KAAAwR,iCAAA,GACA1W,OAAAzD,iBAAA,SAAA2I,KAAAwR,iCAAA,GACA1W,OAAAzD,iBAAA,aAAA2I,KAAAwR,oCAMA1Y,EAAAY,UACA8D,YAAAoT,EACAnT,cAAA,kBACArC,SAAA,ed1GA,IAAAqW,GAAA,SAAAtY,GACA6G,KAAAxJ,SAAA2C,EAEA6G,KAAAC,OAEAnF,QAAA,eAAA2W,EAOAA,EAAAzU,UAAAkD,WACAwR,UAAA,sBACAC,kBAAA,IACAC,eAAA,IACAC,UAAA,WACAC,aAAA,eACAC,cAAA,iBAQAN,EAAAzU,UAAAsG,WACAC,MAAA,GACAC,OAAA,GACAC,MAAA,IAQAgO,EAAAzU,UAAAgV,OACAC,SAAA,EACAC,OAAA,EACAC,UAAA,EACAC,OAAA,GAUAX,EAAAzU,UAAArG,aACAiN,UAAA,wBACAyO,OAAA,qBACAC,OAAA,qBACAC,QAAA,sBACAC,WAAA,4BACAC,KAAA,iBACAha,iBAAA,uBACAC,iBAAA,mCACAC,OAAA,aACAyI,qBAAA,sCACAsR,cAAA,6BACAC,iBAAA,gCACAC,cAAA,6BACAC,aAAA,2BACAC,WAAA,yBACAC,QAAA,sBACAC,cAAA,gCACAC,IAAA,kBACAC,eAAA,6BACAC,oBAAA,kCACAC,qBAAA,mCACAxa,kBAAA,gCACAya,MAAA,wBACAC,WAAA,aACAC,SAAA,WACAC,qBAAA,uBACAC,eAAA,oBACAC,WAAA,aACAC,gBAAA,kBACAC,eAAA,aACArb,UAAA,YACAkJ,YAAA,cACAwC,aAAA,eACA4P,gBAAA,gCACAC,gBAAA,iCAOArC,EAAAzU,UAAA+W,sBAAA,WACA,IAAA/T,KAAAgU,QAAAvd,UAAAC,SAAAsJ,KAAArJ,YAAAsN,cAAA,CAGA,GAAAgQ,IAAAjU,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAgd,kBAAA3T,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAkc,aACA7S,MAAA1H,SAAA4b,UAAA,IAAAlU,KAAAgU,QAAAvd,UAAAC,SAAAsJ,KAAArJ,YAAA+c,aACA1T,KAAAgU,QAAAvd,UAAAO,IAAAgJ,KAAArJ,YAAA8c,gBACAzT,KAAAgU,QAAAvd,UAAAO,IAAAgJ,KAAArJ,YAAA+c,YACAO,GACAjU,KAAAgU,QAAAvd,UAAAO,IAAAgJ,KAAArJ,YAAAsN,eAEAjE,KAAA1H,SAAA4b,WAAA,GAAAlU,KAAAgU,QAAAvd,UAAAC,SAAAsJ,KAAArJ,YAAA+c,cACA1T,KAAAgU,QAAAvd,UAAAqL,OAAA9B,KAAArJ,YAAA8c,gBACAzT,KAAAgU,QAAAvd,UAAAqL,OAAA9B,KAAArJ,YAAA+c,YACAO,GACAjU,KAAAgU,QAAAvd,UAAAO,IAAAgJ,KAAArJ,YAAAsN,iBAUAwN,EAAAzU,UAAAmX,sBAAA,SAAAzO,GAEAA,EAAAa,UAAAvG,KAAAsD,UAAAE,QAAAxD,KAAAoU,QAAA3d,UAAAC,SAAAsJ,KAAArJ,YAAAid,iBACA5T,KAAAqU,gBAQA5C,EAAAzU,UAAAsX,mBAAA,WACAtU,KAAAuU,sBAAAC,QACAxU,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAAgd,kBAEA3T,KAAAxJ,SAAAC,UAAAqL,OAAA9B,KAAArJ,YAAAgd,iBAEA3T,KAAAoU,UACApU,KAAAoU,QAAA3d,UAAAqL,OAAA9B,KAAArJ,YAAAid,gBACA5T,KAAAyU,YAAAhe,UAAAqL,OAAA9B,KAAArJ,YAAAid,mBAUAnC,EAAAzU,UAAA0X,qBAAA,SAAAhP,GACA,GAAAA,GAAA,YAAAA,EAAAiP,KAAA,CACA,GAAAjP,EAAAa,UAAAvG,KAAAsD,UAAAG,OAAAiC,EAAAa,UAAAvG,KAAAsD,UAAAC,MAKA,MAHAmC,GAAAjO,iBAMAuI,KAAAqU,gBAOA5C,EAAAzU,UAAA4X,4BAAA,WACA5U,KAAAgU,QAAAvd,UAAAqL,OAAA9B,KAAArJ,YAAAsN,eAOAwN,EAAAzU,UAAA6X,oBAAA,WACA7U,KAAAgU,QAAAvd,UAAAC,SAAAsJ,KAAArJ,YAAA+c,cACA1T,KAAAgU,QAAAvd,UAAAqL,OAAA9B,KAAArJ,YAAA+c,YACA1T,KAAAgU,QAAAvd,UAAAO,IAAAgJ,KAAArJ,YAAAsN,gBAQAwN,EAAAzU,UAAAlF,eAAA,SAAAgd,GACA,IAAA,GAAA3F,GAAA,EAAAA,EAAA2F,EAAA3a,OAAAgV,IACA2F,EAAA3F,GAAA1Y,UAAAqL,OAAA9B,KAAArJ,YAAA4B,YAQAkZ,EAAAzU,UAAAjF,iBAAA,SAAAI,GACA,IAAA,GAAAqE,GAAA,EAAAA,EAAArE,EAAAgC,OAAAqC,IACArE,EAAAqE,GAAA/F,UAAAqL,OAAA9B,KAAArJ,YAAA4B,YAQAkZ,EAAAzU,UAAAqX,aAAA,WACA,GAAAU,GAAA/U,KAAAxJ,SAAAqB,cAAA,IAAAmI,KAAArJ,YAAA6b,WACAxS,MAAAoU,QAAA3d,UAAA6P,OAAAtG,KAAArJ,YAAAid,gBACA5T,KAAAyU,YAAAhe,UAAA6P,OAAAtG,KAAArJ,YAAAid,gBAEA5T,KAAAoU,QAAA3d,UAAAC,SAAAsJ,KAAArJ,YAAAid,iBACA5T,KAAAoU,QAAAlY,aAAA,cAAA,SACA6Y,EAAA7Y,aAAA,gBAAA,UAEA8D,KAAAoU,QAAAlY,aAAA,cAAA,QACA6Y,EAAA7Y,aAAA,gBAAA,WAGAuV,EAAAzU,UAAA,aAAAyU,EAAAzU,UAAAqX,aAIA5C,EAAAzU,UAAAiD,KAAA,WACA,GAAAD,KAAAxJ,SAAA,CACA,GAAA+N,GAAAzN,SAAAC,cAAA,MACAwN,GAAA9N,UAAAO,IAAAgJ,KAAArJ,YAAAiN,UACA,IAAAoR,GAAAhV,KAAAxJ,SAAAqB,cAAA,SACAmI,MAAAxJ,SAAAgO,cAAAC,aAAAF,EAAAvE,KAAAxJ,UACAwJ,KAAAxJ,SAAAgO,cAAAE,YAAA1E,KAAAxJ,UACA+N,EAAAnN,YAAA4I,KAAAxJ,UACAwe,GACAA,EAAAxO,OAIA,KAAA,GAFAyO,GAAAjV,KAAAxJ,SAAA0e,WACAC,EAAAF,EAAA9a,OACAib,EAAA,EAAAA,EAAAD,EAAAC,IAAA,CACA,GAAAC,GAAAJ,EAAAG,EACAC,GAAA5e,WAAA4e,EAAA5e,UAAAC,SAAAsJ,KAAArJ,YAAA0b,UACArS,KAAAgU,QAAAqB,GAEAA,EAAA5e,WAAA4e,EAAA5e,UAAAC,SAAAsJ,KAAArJ,YAAA2b,UACAtS,KAAAoU,QAAAiB,GAEAA,EAAA5e,WAAA4e,EAAA5e,UAAAC,SAAAsJ,KAAArJ,YAAA4b,WACAvS,KAAA1H,SAAA+c,GAGAva,OAAAzD,iBAAA,WAAA,SAAAC,GACAA,EAAAge,YAGAtV,KAAAxJ,SAAAsP,MAAAyP,UAAA,SACApW,sBAAA,WACAa,KAAAxJ,SAAAsP,MAAAyP,UAAA,IACA3U,KAAAZ,SAEAY,KAAAZ,OAAA,GACAA,KAAAgU,UACAhU,KAAAxH,QAAAwH,KAAAgU,QAAAnc,cAAA,IAAAmI,KAAArJ,YAAAoc,SAEA,IAAAyC,GAAAxV,KAAAgS,MAAAC,QA+BA,IA9BAjS,KAAAgU,UACAhU,KAAAgU,QAAAvd,UAAAC,SAAAsJ,KAAArJ,YAAA+b,eACA8C,EAAAxV,KAAAgS,MAAAE,OACAlS,KAAAgU,QAAAvd,UAAAC,SAAAsJ,KAAArJ,YAAAgc,mBACA6C,EAAAxV,KAAAgS,MAAAG,UACAnS,KAAAgU,QAAA3c,iBAAA,gBAAA2I,KAAA4U,4BAAAhU,KAAAZ,OACAA,KAAAgU,QAAA3c,iBAAA,QAAA2I,KAAA6U,oBAAAjU,KAAAZ,QACAA,KAAAgU,QAAAvd,UAAAC,SAAAsJ,KAAArJ,YAAAic,iBACA4C,EAAAxV,KAAAgS,MAAAI,OACA7N,EAAA9N,UAAAO,IAAAgJ,KAAArJ,YAAA6c,uBAEAgC,IAAAxV,KAAAgS,MAAAC,UACAjS,KAAAgU,QAAAvd,UAAAO,IAAAgJ,KAAArJ,YAAA8c,gBACAzT,KAAAxH,SACAwH,KAAAxH,QAAA/B,UAAAO,IAAAgJ,KAAArJ,YAAA8c,iBAEA+B,IAAAxV,KAAAgS,MAAAE,QAAAsD,IAAAxV,KAAAgS,MAAAI,QACApS,KAAAgU,QAAAvd,UAAAqL,OAAA9B,KAAArJ,YAAA8c,gBACAzT,KAAAxH,SACAwH,KAAAxH,QAAA/B,UAAAqL,OAAA9B,KAAArJ,YAAA8c,iBAEA+B,IAAAxV,KAAAgS,MAAAG,YAIAnS,KAAA1H,SAAAjB,iBAAA,SAAA2I,KAAA+T,sBAAAnT,KAAAZ,OACAA,KAAA+T,0BAIA/T,KAAAoU,QAAA,CACA,GAAAW,GAAA/U,KAAAxJ,SAAAqB,cAAA,IAAAmI,KAAArJ,YAAA6b,WACA,KAAAuC,EAAA,CACAA,EAAAje,SAAAC,cAAA,OACAge,EAAA7Y,aAAA,gBAAA,SACA6Y,EAAA7Y,aAAA,OAAA,UACA6Y,EAAA7Y,aAAA,WAAA,KACA6Y,EAAAte,UAAAO,IAAAgJ,KAAArJ,YAAA6b,WACA,IAAAiD,GAAA3e,SAAAC,cAAA,IACA0e,GAAAhf,UAAAO,IAAAgJ,KAAArJ,YAAA8b,MACAgD,EAAAC,UAAA1V,KAAAE,UAAA2R,UACAkD,EAAA3d,YAAAqe,GAEAzV,KAAAoU,QAAA3d,UAAAC,SAAAsJ,KAAArJ,YAAAkd,iBAEAkB,EAAAte,UAAAO,IAAAgJ,KAAArJ,YAAAkd,iBACA7T,KAAAoU,QAAA3d,UAAAC,SAAAsJ,KAAArJ,YAAAmd,kBAEAiB,EAAAte,UAAAO,IAAAgJ,KAAArJ,YAAAmd,iBAEAiB,EAAA1d,iBAAA,QAAA2I,KAAA0U,qBAAA9T,KAAAZ,OACA+U,EAAA1d,iBAAA,UAAA2I,KAAA0U,qBAAA9T,KAAAZ,OAIAA,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA2c,YAGAtT,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAkc,cACA7S,KAAAgU,QAAAvP,aAAAsQ,EAAA/U,KAAAgU,QAAA2B,YAEA3V,KAAAxJ,SAAAiO,aAAAsQ,EAAA/U,KAAA1H,SAEA,IAAAsd,GAAA9e,SAAAC,cAAA,MACA6e,GAAAnf,UAAAO,IAAAgJ,KAAArJ,YAAAmc,YACA9S,KAAAxJ,SAAAY,YAAAwe,GACAA,EAAAve,iBAAA,QAAA2I,KAAA0U,qBAAA9T,KAAAZ,OACAA,KAAAyU,YAAAmB,EACA5V,KAAAoU,QAAA/c,iBAAA,UAAA2I,KAAAmU,sBAAAvT,KAAAZ,OACAA,KAAAoU,QAAAlY,aAAA,cAAA,QAQA,GAJA8D,KAAAuU,sBAAAzZ,OAAA+a,WAAA7V,KAAAE,UAAAwR,WACA1R,KAAAuU,sBAAAuB,YAAA9V,KAAAsU,mBAAA1T,KAAAZ,OACAA,KAAAsU,qBAEAtU,KAAAgU,SAAAhU,KAAAxH,QAAA,CACAwH,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA4c,SACA,IAAAwC,GAAAjf,SAAAC,cAAA,MACAgf,GAAAtf,UAAAO,IAAAgJ,KAAArJ,YAAAqc,eACAhT,KAAAgU,QAAAvP,aAAAsR,EAAA/V,KAAAxH,SACAwH,KAAAgU,QAAAtP,YAAA1E,KAAAxH,QACA,IAAAwd,GAAAlf,SAAAC,cAAA,MACAif,GAAAvf,UAAAO,IAAAgJ,KAAArJ,YAAAuc,gBACA8C,EAAAvf,UAAAO,IAAAgJ,KAAArJ,YAAAwc,oBACA,IAAA8C,GAAAnf,SAAAC,cAAA,IACAkf,GAAAxf,UAAAO,IAAAgJ,KAAArJ,YAAA8b,MACAwD,EAAA3J,YAAAtM,KAAAE,UAAA4R,aACAkE,EAAA5e,YAAA6e,GACAD,EAAA3e,iBAAA,QAAA,WACA2I,KAAAxH,QAAA0d,YAAAlW,KAAAE,UAAAyR,mBACA/Q,KAAAZ,MACA,IAAAmW,GAAArf,SAAAC,cAAA,MACAof,GAAA1f,UAAAO,IAAAgJ,KAAArJ,YAAAuc,gBACAiD,EAAA1f,UAAAO,IAAAgJ,KAAArJ,YAAAyc,qBACA,IAAAgD,GAAAtf,SAAAC,cAAA,IACAqf,GAAA3f,UAAAO,IAAAgJ,KAAArJ,YAAA8b,MACA2D,EAAA9J,YAAAtM,KAAAE,UAAA6R,cACAoE,EAAA/e,YAAAgf,GACAD,EAAA9e,iBAAA,QAAA,WACA2I,KAAAxH,QAAA0d,YAAAlW,KAAAE,UAAAyR,mBACA/Q,KAAAZ,OACA+V,EAAA3e,YAAA4e,GACAD,EAAA3e,YAAA4I,KAAAxH,SACAud,EAAA3e,YAAA+e,EAGA,IAAAE,GAAA,WACArW,KAAAxH,QAAA0d,WAAA,EACAF,EAAAvf,UAAAO,IAAAgJ,KAAArJ,YAAA4B,WAEAyd,EAAAvf,UAAAqL,OAAA9B,KAAArJ,YAAA4B,WAEAyH,KAAAxH,QAAA0d,WAAAlW,KAAAxH,QAAA8d,YAAAtW,KAAAxH,QAAA4Y,YACA+E,EAAA1f,UAAAO,IAAAgJ,KAAArJ,YAAA4B,WAEA4d,EAAA1f,UAAAqL,OAAA9B,KAAArJ,YAAA4B,YAEAqI,KAAAZ,KACAA,MAAAxH,QAAAnB,iBAAA,SAAAgf,GACAA,GAEA,IAAAE,GAAA,WAEAvW,KAAAwW,kBACA1W,aAAAE,KAAAwW,kBAEAxW,KAAAwW,iBAAA3W,WAAA,WACAwW,IACArW,KAAAwW,iBAAA,MACA5V,KAAAZ,MAAAA,KAAAE,UAAA0R,iBACAhR,KAAAZ,KACAlF,QAAAzD,iBAAA,SAAAkf,GACAvW,KAAAxH,QAAA/B,UAAAC,SAAAsJ,KAAArJ,YAAA8B,mBACAuH,KAAAxH,QAAA/B,UAAAO,IAAAgJ,KAAArJ,YAAAyK,qBAMA,KAAA,GAHAlJ,GAAA8H,KAAAxH,QAAA8C,iBAAA,IAAA0E,KAAArJ,YAAAsc,KACA9a,EAAA6H,KAAA1H,SAAAgD,iBAAA,IAAA0E,KAAArJ,YAAA0c,OAEApZ,EAAA,EAAAA,EAAA/B,EAAAiC,OAAAF,IACA,GAAAhC,GAAAC,EAAA+B,GAAA/B,EAAAC,EAAA6H,MAGAA,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA8K,eA2CA3G,OAAA,kBAAA7C,EAGAa,EAAAY,UACA8D,YAAAiU,EACAhU,cAAA,iBACArC,SAAA,iBercA,IAAAqb,GAAA,SAAAtd,GACA6G,KAAAxJ,SAAA2C,EAEA6G,KAAAC,OAEAnF,QAAA,kBAAA2b,EAOAA,EAAAzZ,UAAAkD,aASAuW,EAAAzZ,UAAArG,aACA+f,WAAA,iBACAC,WAAA,6BACAC,eAAA,yBACAC,YAAA,cACApV,YAAA,eAWAgV,EAAAzZ,UAAA8Z,WAAA,SAAAC,EAAAC,EAAAC,GACA,MAAAD,GACA,WACAD,EAAA3U,QACA4U,EAAAvgB,UAAAO,IAAAgJ,KAAArJ,YAAAkgB,aAEAG,EAAAvgB,UAAAqL,OAAA9B,KAAArJ,YAAAkgB,cAEAjW,KAAAZ,MAEAiX,EACA,WACA,GAAAhd,GACAoO,CACA,IAAA0O,EAAA3U,QACA,IAAAnI,EAAA,EAAAA,EAAAgd,EAAA9c,OAAAF,IACAoO,EAAA4O,EAAAhd,GAAApC,cAAA,MAAAA,cAAA,iBACAwQ,EAAA,iBAAAhG,QACA4U,EAAAhd,GAAAxD,UAAAO,IAAAgJ,KAAArJ,YAAAkgB,iBAGA,KAAA5c,EAAA,EAAAA,EAAAgd,EAAA9c,OAAAF,IACAoO,EAAA4O,EAAAhd,GAAApC,cAAA,MAAAA,cAAA,iBACAwQ,EAAA,iBAAA/F,UACA2U,EAAAhd,GAAAxD,UAAAqL,OAAA9B,KAAArJ,YAAAkgB,cAGAjW,KAAAZ,MAjBA,QA4BAyW,EAAAzZ,UAAAka,gBAAA,SAAAF,EAAAC,GACA,GAAAE,GAAArgB,SAAAC,cAAA,SACAqgB,GACA,eACA,kBACA,uBACApX,KAAArJ,YAAAigB,eAEAO,GAAA/c,UAAAgd,EAAAjb,KAAA,IACA,IAAA4a,GAAAjgB,SAAAC,cAAA,QAWA,OAVAggB,GAAApC,KAAA,WACAoC,EAAAtgB,UAAAO,IAAA,uBACAggB,GACAD,EAAA3U,QAAA4U,EAAAvgB,UAAAC,SAAAsJ,KAAArJ,YAAAkgB,aACAE,EAAA1f,iBAAA,SAAA2I,KAAA8W,WAAAC,EAAAC,KACAC,GACAF,EAAA1f,iBAAA,SAAA2I,KAAA8W,WAAAC,EAAA,KAAAE,IAEAE,EAAA/f,YAAA2f,GACAje,EAAAI,eAAAie,EAAA,oBACAA,GAKAV,EAAAzZ,UAAAiD,KAAA,WACA,GAAAD,KAAAxJ,SAAA,CACA,GAAA6gB,GAAArX,KAAAxJ,SAAAqB,cAAA,MACAyf,EAAAxa,MAAAE,UAAAC,MAAAC,KAAA8C,KAAAxJ,SAAA8E,iBAAA,aACAic,EAAAza,MAAAE,UAAAC,MAAAC,KAAA8C,KAAAxJ,SAAA8E,iBAAA,aACAkc,EAAAF,EAAAG,OAAAF,EACA,IAAAvX,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAAggB,YAAA,CACA,GAAAe,GAAA5gB,SAAAC,cAAA,MACA4gB,EAAA3X,KAAAkX,gBAAA,KAAAM,EACAE,GAAAtgB,YAAAugB,GACAN,EAAA7S,cAAAC,aAAAiT,EAAAL,EACA,KAAA,GAAApd,GAAA,EAAAA,EAAAud,EAAArd,OAAAF,IAAA,CACA,GAAA2d,GAAAJ,EAAAvd,GAAApC,cAAA,KACA,IAAA+f,EAAA,CACA,GAAAC,GAAA/gB,SAAAC,cAAA,KACA,IAAA,UAAAygB,EAAAvd,GAAAyN,WAAAoQ,SAAAC,cAAA,CACA,GAAAC,GAAAhY,KAAAkX,gBAAAM,EAAAvd,GACA4d,GAAAzgB,YAAA4gB,GAEAR,EAAAvd,GAAAwK,aAAAoT,EAAAD,IAGA5X,KAAAxJ,SAAAC,UAAAO,IAAAgJ,KAAArJ,YAAA8K,gBAMA3I,EAAAY,UACA8D,YAAAiZ,EACAhZ,cAAA,oBACArC,SAAA,qBCnIA,IAAA6c,GAAA,SAAA9e,GACA6G,KAAAxJ,SAAA2C,EAEA6G,KAAAC,OAEAnF,QAAA,eAAAmd,EAOAA,EAAAjb,UAAAkD,WACAgY,cAAA,wBACAC,aAAA,MACAC,gBAAA,MACAC,cAAA,IACAC,YAAA,IAUAL,EAAAjb,UAAArG,aACA0K,cAAA,qBACAkX,4BAAA,sCACA5f,OAAA,aACAsL,aAAA,eACAD,WAAA,cAQAiU,EAAAjb,UAAAwb,aAAA,SAAAnY,GACA,IAAAL,KAAAU,eAAAoF,MAAAqB,QAAAnH,KAAAU,eAAAoF,MAAAoB,OAAA,CACA,GAAAvB,GAAA3F,KAAAxJ,SAAAoP,uBACA5F,MAAAyY,YAAA9S,EAAAuB,OACAlH,KAAA0Y,WAAA/S,EAAAwB,MACAnH,KAAA2Y,YAAA,EAAAhZ,KAAAiZ,KAAAjT,EAAAwB,MAAAxB,EAAAwB,MAAAxB,EAAAuB,OAAAvB,EAAAuB,QAAA,EACAlH,KAAAU,eAAAoF,MAAAqB,MAAAnH,KAAA2Y,YAAA,KACA3Y,KAAAU,eAAAoF,MAAAoB,OAAAlH,KAAA2Y,YAAA,KAGA,GADA3Y,KAAAU,eAAAjK,UAAAO,IAAAgJ,KAAArJ,YAAAqN,YACA,cAAA3D,EAAAsU,MAAA3U,KAAA6Y,mBACA7Y,KAAA6Y,oBAAA,MACA,CACA,eAAAxY,EAAAsU,OACA3U,KAAA6Y,oBAAA,EAEA,IAAAC,GAAA9Y,KAAA+Y,eACA,IAAAD,EAAA,EACA,MAEA9Y,MAAAgZ,cAAA,EACA,IACAC,GACA3O,EAFA4O,EAAA7Y,EAAA8Y,cAAAvT,uBAIA,IAAA,IAAAvF,EAAA+J,SAAA,IAAA/J,EAAAgK,QACA4O,EAAAtZ,KAAAyZ,MAAAF,EAAA/R,MAAA,GACAmD,EAAA3K,KAAAyZ,MAAAF,EAAAhS,OAAA,OACA,CACA,GAAAkD,GAAAyB,SAAAxL,EAAA+J,QAAA/J,EAAA+J,QAAA/J,EAAAgZ,QAAA,GAAAjP,QACAC,EAAAwB,SAAAxL,EAAAgK,QAAAhK,EAAAgK,QAAAhK,EAAAgZ,QAAA,GAAAhP,OACA4O,GAAAtZ,KAAAyZ,MAAAhP,EAAA8O,EAAA/S,MACAmE,EAAA3K,KAAAyZ,MAAA/O,EAAA6O,EAAAlT,KAEAhG,KAAAsZ,YAAAL,EAAA3O,GACAtK,KAAAuZ,iBAAA,GACAze,OAAAqE,sBAAAa,KAAAwZ,iBAAA5Y,KAAAZ,SASAiY,EAAAjb,UAAAyc,WAAA,SAAApZ,GAEAA,GAAA,IAAAA,EAAAqZ,QAIA5e,OAAA+E,WAAA,WACAG,KAAAU,eAAAjK,UAAAqL,OAAA9B,KAAArJ,YAAAqN,aACApD,KAAAZ,MAAA,IAMAiY,EAAAjb,UAAAiD,KAAA,WACA,GAAAD,KAAAxJ,SAAA,CACA,GAAAmjB,GAAA3Z,KAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAA0K,cACArB,MAAAxJ,SAAAC,UAAAC,SAAAsJ,KAAArJ,YAAA4hB,+BACAvY,KAAAU,eAAAV,KAAAxJ,SAAAqB,cAAA,IAAAmI,KAAArJ,YAAAgC,QACAqH,KAAA4Z,YAAA,EACA5Z,KAAA2Y,YAAA,EACA3Y,KAAA6Z,GAAA,EACA7Z,KAAA8Z,GAAA,EAIA9Z,KAAA6Y,oBAAA,EACA7Y,KAAA+Z,iBAAA/Z,KAAAwY,aAAA5X,KAAAZ,MACAA,KAAAxJ,SAAAa,iBAAA,YAAA2I,KAAA+Z,kBACA/Z,KAAAxJ,SAAAa,iBAAA,aAAA2I,KAAA+Z,kBACA/Z,KAAAga,eAAAha,KAAAyZ,WAAA7Y,KAAAZ,MACAA,KAAAxJ,SAAAa,iBAAA,UAAA2I,KAAAga,gBACAha,KAAAxJ,SAAAa,iBAAA,aAAA2I,KAAAga,gBACAha,KAAAxJ,SAAAa,iBAAA,WAAA2I,KAAAga,gBACAha,KAAAxJ,SAAAa,iBAAA,OAAA2I,KAAAga,gBAKAha,KAAA+Y,cAAA,WACA,MAAA/Y,MAAA4Z,aAMA5Z,KAAAgZ,cAAA,SAAAiB,GACAja,KAAA4Z,YAAAK,GAMAja,KAAAka,iBAAA,WACA,MAAAla,MAAAU,gBAOAV,KAAAsZ,YAAA,SAAAa,EAAAC,GACApa,KAAA6Z,GAAAM,EACAna,KAAA8Z,GAAAM,GAMApa,KAAAuZ,gBAAA,SAAAvL,GACA,GAAA,OAAAhO,KAAAU,eAAA,CACA,GAAA2Z,GACAC,EACAC,EACAC,EAAA,aAAAxa,KAAA6Z,GAAA,OAAA7Z,KAAA8Z,GAAA,KACA9L,IACAsM,EAAAta,KAAAE,UAAAgY,cACAqC,EAAAva,KAAAE,UAAAiY,eAEAmC,EAAAta,KAAAE,UAAAoY,YACAiC,EAAAva,KAAA2Y,YAAA,KACAgB,IACAa,EAAA,aAAAxa,KAAA0Y,WAAA,EAAA,OAAA1Y,KAAAyY,YAAA,EAAA,QAGA4B,EAAA,yBAAAG,EAAAF,EACAta,KAAAU,eAAAoF,MAAA2U,gBAAAJ,EACAra,KAAAU,eAAAoF,MAAA4U,YAAAL,EACAra,KAAAU,eAAAoF,MAAA6U,UAAAN,EACArM,EACAhO,KAAAU,eAAAjK,UAAAqL,OAAA9B,KAAArJ,YAAAsN,cAEAjE,KAAAU,eAAAjK,UAAAO,IAAAgJ,KAAArJ,YAAAsN,gBAOAjE,KAAAwZ,iBAAA,WACAxZ,KAAA4Z,eAAA,EACA9e,OAAAqE,sBAAAa,KAAAwZ,iBAAA5Y,KAAAZ,OAEAA,KAAAuZ,iBAAA,OAQAzgB,EAAAY,UACA8D,YAAAya,EACAxa,cAAA,iBACArC,SAAA,uBACAuB,QAAA", "file": "material.min.js", "sourcesContent": ["/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for Tabs MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @constructor\n   * @param {Element} element The element that will be upgraded.\n   */\nvar MaterialTabs = function MaterialTabs(element) {\n    // Stores the HTML element.\n    this.element_ = element;\n    // Initialize instance.\n    this.init();\n};\nwindow['MaterialTabs'] = MaterialTabs;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialTabs.prototype.Constant_ = {};\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialTabs.prototype.CssClasses_ = {\n    TAB_CLASS: 'mdl-tabs__tab',\n    PANEL_CLASS: 'mdl-tabs__panel',\n    ACTIVE_CLASS: 'is-active',\n    UPGRADED_CLASS: 'is-upgraded',\n    MDL_JS_RIPPLE_EFFECT: 'mdl-js-ripple-effect',\n    MDL_RIPPLE_CONTAINER: 'mdl-tabs__ripple-container',\n    MDL_RIPPLE: 'mdl-ripple',\n    MDL_JS_RIPPLE_EFFECT_IGNORE_EVENTS: 'mdl-js-ripple-effect--ignore-events'\n};\n/**\n   * Handle clicks to a tabs component\n   *\n   * @private\n   */\nMaterialTabs.prototype.initTabs_ = function () {\n    if (this.element_.classList.contains(this.CssClasses_.MDL_JS_RIPPLE_EFFECT)) {\n        this.element_.classList.add(this.CssClasses_.MDL_JS_RIPPLE_EFFECT_IGNORE_EVENTS);\n    }\n    // Select element tabs, document panels\n    this.tabs_ = this.element_.querySelectorAll('.' + this.CssClasses_.TAB_CLASS);\n    this.panels_ = this.element_.querySelectorAll('.' + this.CssClasses_.PANEL_CLASS);\n    // Create new tabs for each tab element\n    for (var i = 0; i < this.tabs_.length; i++) {\n        new MaterialTab(this.tabs_[i], this);\n    }\n    this.element_.classList.add(this.CssClasses_.UPGRADED_CLASS);\n};\n/**\n   * Reset tab state, dropping active classes\n   *\n   * @private\n   */\nMaterialTabs.prototype.resetTabState_ = function () {\n    for (var k = 0; k < this.tabs_.length; k++) {\n        this.tabs_[k].classList.remove(this.CssClasses_.ACTIVE_CLASS);\n    }\n};\n/**\n   * Reset panel state, droping active classes\n   *\n   * @private\n   */\nMaterialTabs.prototype.resetPanelState_ = function () {\n    for (var j = 0; j < this.panels_.length; j++) {\n        this.panels_[j].classList.remove(this.CssClasses_.ACTIVE_CLASS);\n    }\n};\n/**\n   * Initialize element.\n   */\nMaterialTabs.prototype.init = function () {\n    if (this.element_) {\n        this.initTabs_();\n    }\n};\n/**\n   * Constructor for an individual tab.\n   *\n   * @constructor\n   * @param {Element} tab The HTML element for the tab.\n   * @param {MaterialTabs} ctx The MaterialTabs object that owns the tab.\n   */\nfunction MaterialTab(tab, ctx) {\n    if (tab) {\n        if (ctx.element_.classList.contains(ctx.CssClasses_.MDL_JS_RIPPLE_EFFECT)) {\n            var rippleContainer = document.createElement('span');\n            rippleContainer.classList.add(ctx.CssClasses_.MDL_RIPPLE_CONTAINER);\n            rippleContainer.classList.add(ctx.CssClasses_.MDL_JS_RIPPLE_EFFECT);\n            var ripple = document.createElement('span');\n            ripple.classList.add(ctx.CssClasses_.MDL_RIPPLE);\n            rippleContainer.appendChild(ripple);\n            tab.appendChild(rippleContainer);\n        }\n        tab.addEventListener('click', function (e) {\n            if (tab.getAttribute('href').charAt(0) === '#') {\n                e.preventDefault();\n                var href = tab.href.split('#')[1];\n                var panel = ctx.element_.querySelector('#' + href);\n                ctx.resetTabState_();\n                ctx.resetPanelState_();\n                tab.classList.add(ctx.CssClasses_.ACTIVE_CLASS);\n                panel.classList.add(ctx.CssClasses_.ACTIVE_CLASS);\n            }\n        });\n    }\n}\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialTabs,\n    classAsString: 'MaterialTabs',\n    cssClass: 'mdl-js-tabs'\n});", "/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for Layout MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @constructor\n   * @param {HTMLElement} element The element that will be upgraded.\n   */\nvar MaterialLayout = function MaterialLayout(element) {\n    this.element_ = element;\n    // Initialize instance.\n    this.init();\n};\nwindow['MaterialLayout'] = MaterialLayout;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string | number}\n   * @private\n   */\nMaterialLayout.prototype.Constant_ = {\n    MAX_WIDTH: '(max-width: 1024px)',\n    TAB_SCROLL_PIXELS: 100,\n    RESIZE_TIMEOUT: 100,\n    MENU_ICON: '&#xE5D2;',\n    CHEVRON_LEFT: 'chevron_left',\n    CHEVRON_RIGHT: 'chevron_right'\n};\n/**\n   * Keycodes, for code readability.\n   *\n   * @enum {number}\n   * @private\n   */\nMaterialLayout.prototype.Keycodes_ = {\n    ENTER: 13,\n    ESCAPE: 27,\n    SPACE: 32\n};\n/**\n   * Modes.\n   *\n   * @enum {number}\n   * @private\n   */\nMaterialLayout.prototype.Mode_ = {\n    STANDARD: 0,\n    SEAMED: 1,\n    WATERFALL: 2,\n    SCROLL: 3\n};\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialLayout.prototype.CssClasses_ = {\n    CONTAINER: 'mdl-layout__container',\n    HEADER: 'mdl-layout__header',\n    DRAWER: 'mdl-layout__drawer',\n    CONTENT: 'mdl-layout__content',\n    DRAWER_BTN: 'mdl-layout__drawer-button',\n    ICON: 'material-icons',\n    JS_RIPPLE_EFFECT: 'mdl-js-ripple-effect',\n    RIPPLE_CONTAINER: 'mdl-layout__tab-ripple-container',\n    RIPPLE: 'mdl-ripple',\n    RIPPLE_IGNORE_EVENTS: 'mdl-js-ripple-effect--ignore-events',\n    HEADER_SEAMED: 'mdl-layout__header--seamed',\n    HEADER_WATERFALL: 'mdl-layout__header--waterfall',\n    HEADER_SCROLL: 'mdl-layout__header--scroll',\n    FIXED_HEADER: 'mdl-layout--fixed-header',\n    OBFUSCATOR: 'mdl-layout__obfuscator',\n    TAB_BAR: 'mdl-layout__tab-bar',\n    TAB_CONTAINER: 'mdl-layout__tab-bar-container',\n    TAB: 'mdl-layout__tab',\n    TAB_BAR_BUTTON: 'mdl-layout__tab-bar-button',\n    TAB_BAR_LEFT_BUTTON: 'mdl-layout__tab-bar-left-button',\n    TAB_BAR_RIGHT_BUTTON: 'mdl-layout__tab-bar-right-button',\n    TAB_MANUAL_SWITCH: 'mdl-layout__tab-manual-switch',\n    PANEL: 'mdl-layout__tab-panel',\n    HAS_DRAWER: 'has-drawer',\n    HAS_TABS: 'has-tabs',\n    HAS_SCROLLING_HEADER: 'has-scrolling-header',\n    CASTING_SHADOW: 'is-casting-shadow',\n    IS_COMPACT: 'is-compact',\n    IS_SMALL_SCREEN: 'is-small-screen',\n    IS_DRAWER_OPEN: 'is-visible',\n    IS_ACTIVE: 'is-active',\n    IS_UPGRADED: 'is-upgraded',\n    IS_ANIMATING: 'is-animating',\n    ON_LARGE_SCREEN: 'mdl-layout--large-screen-only',\n    ON_SMALL_SCREEN: 'mdl-layout--small-screen-only'\n};\n/**\n   * Handles scrolling on the content.\n   *\n   * @private\n   */\nMaterialLayout.prototype.contentScrollHandler_ = function () {\n    if (this.header_.classList.contains(this.CssClasses_.IS_ANIMATING)) {\n        return;\n    }\n    var headerVisible = !this.element_.classList.contains(this.CssClasses_.IS_SMALL_SCREEN) || this.element_.classList.contains(this.CssClasses_.FIXED_HEADER);\n    if (this.content_.scrollTop > 0 && !this.header_.classList.contains(this.CssClasses_.IS_COMPACT)) {\n        this.header_.classList.add(this.CssClasses_.CASTING_SHADOW);\n        this.header_.classList.add(this.CssClasses_.IS_COMPACT);\n        if (headerVisible) {\n            this.header_.classList.add(this.CssClasses_.IS_ANIMATING);\n        }\n    } else if (this.content_.scrollTop <= 0 && this.header_.classList.contains(this.CssClasses_.IS_COMPACT)) {\n        this.header_.classList.remove(this.CssClasses_.CASTING_SHADOW);\n        this.header_.classList.remove(this.CssClasses_.IS_COMPACT);\n        if (headerVisible) {\n            this.header_.classList.add(this.CssClasses_.IS_ANIMATING);\n        }\n    }\n};\n/**\n   * Handles a keyboard event on the drawer.\n   *\n   * @param {Event} evt The event that fired.\n   * @private\n   */\nMaterialLayout.prototype.keyboardEventHandler_ = function (evt) {\n    // Only react when the drawer is open.\n    if (evt.keyCode === this.Keycodes_.ESCAPE && this.drawer_.classList.contains(this.CssClasses_.IS_DRAWER_OPEN)) {\n        this.toggleDrawer();\n    }\n};\n/**\n   * Handles changes in screen size.\n   *\n   * @private\n   */\nMaterialLayout.prototype.screenSizeHandler_ = function () {\n    if (this.screenSizeMediaQuery_.matches) {\n        this.element_.classList.add(this.CssClasses_.IS_SMALL_SCREEN);\n    } else {\n        this.element_.classList.remove(this.CssClasses_.IS_SMALL_SCREEN);\n        // Collapse drawer (if any) when moving to a large screen size.\n        if (this.drawer_) {\n            this.drawer_.classList.remove(this.CssClasses_.IS_DRAWER_OPEN);\n            this.obfuscator_.classList.remove(this.CssClasses_.IS_DRAWER_OPEN);\n        }\n    }\n};\n/**\n   * Handles events of drawer button.\n   *\n   * @param {Event} evt The event that fired.\n   * @private\n   */\nMaterialLayout.prototype.drawerToggleHandler_ = function (evt) {\n    if (evt && evt.type === 'keydown') {\n        if (evt.keyCode === this.Keycodes_.SPACE || evt.keyCode === this.Keycodes_.ENTER) {\n            // prevent scrolling in drawer nav\n            evt.preventDefault();\n        } else {\n            // prevent other keys\n            return;\n        }\n    }\n    this.toggleDrawer();\n};\n/**\n   * Handles (un)setting the `is-animating` class\n   *\n   * @private\n   */\nMaterialLayout.prototype.headerTransitionEndHandler_ = function () {\n    this.header_.classList.remove(this.CssClasses_.IS_ANIMATING);\n};\n/**\n   * Handles expanding the header on click\n   *\n   * @private\n   */\nMaterialLayout.prototype.headerClickHandler_ = function () {\n    if (this.header_.classList.contains(this.CssClasses_.IS_COMPACT)) {\n        this.header_.classList.remove(this.CssClasses_.IS_COMPACT);\n        this.header_.classList.add(this.CssClasses_.IS_ANIMATING);\n    }\n};\n/**\n   * Reset tab state, dropping active classes\n   *\n   * @private\n   */\nMaterialLayout.prototype.resetTabState_ = function (tabBar) {\n    for (var k = 0; k < tabBar.length; k++) {\n        tabBar[k].classList.remove(this.CssClasses_.IS_ACTIVE);\n    }\n};\n/**\n   * Reset panel state, droping active classes\n   *\n   * @private\n   */\nMaterialLayout.prototype.resetPanelState_ = function (panels) {\n    for (var j = 0; j < panels.length; j++) {\n        panels[j].classList.remove(this.CssClasses_.IS_ACTIVE);\n    }\n};\n/**\n  * Toggle drawer state\n  *\n  * @public\n  */\nMaterialLayout.prototype.toggleDrawer = function () {\n    var drawerButton = this.element_.querySelector('.' + this.CssClasses_.DRAWER_BTN);\n    this.drawer_.classList.toggle(this.CssClasses_.IS_DRAWER_OPEN);\n    this.obfuscator_.classList.toggle(this.CssClasses_.IS_DRAWER_OPEN);\n    // Set accessibility properties.\n    if (this.drawer_.classList.contains(this.CssClasses_.IS_DRAWER_OPEN)) {\n        this.drawer_.setAttribute('aria-hidden', 'false');\n        drawerButton.setAttribute('aria-expanded', 'true');\n    } else {\n        this.drawer_.setAttribute('aria-hidden', 'true');\n        drawerButton.setAttribute('aria-expanded', 'false');\n    }\n};\nMaterialLayout.prototype['toggleDrawer'] = MaterialLayout.prototype.toggleDrawer;\n/**\n   * Initialize element.\n   */\nMaterialLayout.prototype.init = function () {\n    if (this.element_) {\n        var container = document.createElement('div');\n        container.classList.add(this.CssClasses_.CONTAINER);\n        var focusedElement = this.element_.querySelector(':focus');\n        this.element_.parentElement.insertBefore(container, this.element_);\n        this.element_.parentElement.removeChild(this.element_);\n        container.appendChild(this.element_);\n        if (focusedElement) {\n            focusedElement.focus();\n        }\n        var directChildren = this.element_.childNodes;\n        var numChildren = directChildren.length;\n        for (var c = 0; c < numChildren; c++) {\n            var child = directChildren[c];\n            if (child.classList && child.classList.contains(this.CssClasses_.HEADER)) {\n                this.header_ = child;\n            }\n            if (child.classList && child.classList.contains(this.CssClasses_.DRAWER)) {\n                this.drawer_ = child;\n            }\n            if (child.classList && child.classList.contains(this.CssClasses_.CONTENT)) {\n                this.content_ = child;\n            }\n        }\n        window.addEventListener('pageshow', function (e) {\n            if (e.persisted) {\n                // when page is loaded from back/forward cache\n                // trigger repaint to let layout scroll in safari\n                this.element_.style.overflowY = 'hidden';\n                requestAnimationFrame(function () {\n                    this.element_.style.overflowY = '';\n                }.bind(this));\n            }\n        }.bind(this), false);\n        if (this.header_) {\n            this.tabBar_ = this.header_.querySelector('.' + this.CssClasses_.TAB_BAR);\n        }\n        var mode = this.Mode_.STANDARD;\n        if (this.header_) {\n            if (this.header_.classList.contains(this.CssClasses_.HEADER_SEAMED)) {\n                mode = this.Mode_.SEAMED;\n            } else if (this.header_.classList.contains(this.CssClasses_.HEADER_WATERFALL)) {\n                mode = this.Mode_.WATERFALL;\n                this.header_.addEventListener('transitionend', this.headerTransitionEndHandler_.bind(this));\n                this.header_.addEventListener('click', this.headerClickHandler_.bind(this));\n            } else if (this.header_.classList.contains(this.CssClasses_.HEADER_SCROLL)) {\n                mode = this.Mode_.SCROLL;\n                container.classList.add(this.CssClasses_.HAS_SCROLLING_HEADER);\n            }\n            if (mode === this.Mode_.STANDARD) {\n                this.header_.classList.add(this.CssClasses_.CASTING_SHADOW);\n                if (this.tabBar_) {\n                    this.tabBar_.classList.add(this.CssClasses_.CASTING_SHADOW);\n                }\n            } else if (mode === this.Mode_.SEAMED || mode === this.Mode_.SCROLL) {\n                this.header_.classList.remove(this.CssClasses_.CASTING_SHADOW);\n                if (this.tabBar_) {\n                    this.tabBar_.classList.remove(this.CssClasses_.CASTING_SHADOW);\n                }\n            } else if (mode === this.Mode_.WATERFALL) {\n                // Add and remove shadows depending on scroll position.\n                // Also add/remove auxiliary class for styling of the compact version of\n                // the header.\n                this.content_.addEventListener('scroll', this.contentScrollHandler_.bind(this));\n                this.contentScrollHandler_();\n            }\n        }\n        // Add drawer toggling button to our layout, if we have an openable drawer.\n        if (this.drawer_) {\n            var drawerButton = this.element_.querySelector('.' + this.CssClasses_.DRAWER_BTN);\n            if (!drawerButton) {\n                drawerButton = document.createElement('div');\n                drawerButton.setAttribute('aria-expanded', 'false');\n                drawerButton.setAttribute('role', 'button');\n                drawerButton.setAttribute('tabindex', '0');\n                drawerButton.classList.add(this.CssClasses_.DRAWER_BTN);\n                var drawerButtonIcon = document.createElement('i');\n                drawerButtonIcon.classList.add(this.CssClasses_.ICON);\n                drawerButtonIcon.innerHTML = this.Constant_.MENU_ICON;\n                drawerButton.appendChild(drawerButtonIcon);\n            }\n            if (this.drawer_.classList.contains(this.CssClasses_.ON_LARGE_SCREEN)) {\n                //If drawer has ON_LARGE_SCREEN class then add it to the drawer toggle button as well.\n                drawerButton.classList.add(this.CssClasses_.ON_LARGE_SCREEN);\n            } else if (this.drawer_.classList.contains(this.CssClasses_.ON_SMALL_SCREEN)) {\n                //If drawer has ON_SMALL_SCREEN class then add it to the drawer toggle button as well.\n                drawerButton.classList.add(this.CssClasses_.ON_SMALL_SCREEN);\n            }\n            drawerButton.addEventListener('click', this.drawerToggleHandler_.bind(this));\n            drawerButton.addEventListener('keydown', this.drawerToggleHandler_.bind(this));\n            // Add a class if the layout has a drawer, for altering the left padding.\n            // Adds the HAS_DRAWER to the elements since this.header_ may or may\n            // not be present.\n            this.element_.classList.add(this.CssClasses_.HAS_DRAWER);\n            // If we have a fixed header, add the button to the header rather than\n            // the layout.\n            if (this.element_.classList.contains(this.CssClasses_.FIXED_HEADER)) {\n                this.header_.insertBefore(drawerButton, this.header_.firstChild);\n            } else {\n                this.element_.insertBefore(drawerButton, this.content_);\n            }\n            var obfuscator = document.createElement('div');\n            obfuscator.classList.add(this.CssClasses_.OBFUSCATOR);\n            this.element_.appendChild(obfuscator);\n            obfuscator.addEventListener('click', this.drawerToggleHandler_.bind(this));\n            this.obfuscator_ = obfuscator;\n            this.drawer_.addEventListener('keydown', this.keyboardEventHandler_.bind(this));\n            this.drawer_.setAttribute('aria-hidden', 'true');\n        }\n        // Keep an eye on screen size, and add/remove auxiliary class for styling\n        // of small screens.\n        this.screenSizeMediaQuery_ = window.matchMedia(this.Constant_.MAX_WIDTH);\n        this.screenSizeMediaQuery_.addListener(this.screenSizeHandler_.bind(this));\n        this.screenSizeHandler_();\n        // Initialize tabs, if any.\n        if (this.header_ && this.tabBar_) {\n            this.element_.classList.add(this.CssClasses_.HAS_TABS);\n            var tabContainer = document.createElement('div');\n            tabContainer.classList.add(this.CssClasses_.TAB_CONTAINER);\n            this.header_.insertBefore(tabContainer, this.tabBar_);\n            this.header_.removeChild(this.tabBar_);\n            var leftButton = document.createElement('div');\n            leftButton.classList.add(this.CssClasses_.TAB_BAR_BUTTON);\n            leftButton.classList.add(this.CssClasses_.TAB_BAR_LEFT_BUTTON);\n            var leftButtonIcon = document.createElement('i');\n            leftButtonIcon.classList.add(this.CssClasses_.ICON);\n            leftButtonIcon.textContent = this.Constant_.CHEVRON_LEFT;\n            leftButton.appendChild(leftButtonIcon);\n            leftButton.addEventListener('click', function () {\n                this.tabBar_.scrollLeft -= this.Constant_.TAB_SCROLL_PIXELS;\n            }.bind(this));\n            var rightButton = document.createElement('div');\n            rightButton.classList.add(this.CssClasses_.TAB_BAR_BUTTON);\n            rightButton.classList.add(this.CssClasses_.TAB_BAR_RIGHT_BUTTON);\n            var rightButtonIcon = document.createElement('i');\n            rightButtonIcon.classList.add(this.CssClasses_.ICON);\n            rightButtonIcon.textContent = this.Constant_.CHEVRON_RIGHT;\n            rightButton.appendChild(rightButtonIcon);\n            rightButton.addEventListener('click', function () {\n                this.tabBar_.scrollLeft += this.Constant_.TAB_SCROLL_PIXELS;\n            }.bind(this));\n            tabContainer.appendChild(leftButton);\n            tabContainer.appendChild(this.tabBar_);\n            tabContainer.appendChild(rightButton);\n            // Add and remove tab buttons depending on scroll position and total\n            // window size.\n            var tabUpdateHandler = function () {\n                if (this.tabBar_.scrollLeft > 0) {\n                    leftButton.classList.add(this.CssClasses_.IS_ACTIVE);\n                } else {\n                    leftButton.classList.remove(this.CssClasses_.IS_ACTIVE);\n                }\n                if (this.tabBar_.scrollLeft < this.tabBar_.scrollWidth - this.tabBar_.offsetWidth) {\n                    rightButton.classList.add(this.CssClasses_.IS_ACTIVE);\n                } else {\n                    rightButton.classList.remove(this.CssClasses_.IS_ACTIVE);\n                }\n            }.bind(this);\n            this.tabBar_.addEventListener('scroll', tabUpdateHandler);\n            tabUpdateHandler();\n            // Update tabs when the window resizes.\n            var windowResizeHandler = function () {\n                // Use timeouts to make sure it doesn't happen too often.\n                if (this.resizeTimeoutId_) {\n                    clearTimeout(this.resizeTimeoutId_);\n                }\n                this.resizeTimeoutId_ = setTimeout(function () {\n                    tabUpdateHandler();\n                    this.resizeTimeoutId_ = null;\n                }.bind(this), this.Constant_.RESIZE_TIMEOUT);\n            }.bind(this);\n            window.addEventListener('resize', windowResizeHandler);\n            if (this.tabBar_.classList.contains(this.CssClasses_.JS_RIPPLE_EFFECT)) {\n                this.tabBar_.classList.add(this.CssClasses_.RIPPLE_IGNORE_EVENTS);\n            }\n            // Select element tabs, document panels\n            var tabs = this.tabBar_.querySelectorAll('.' + this.CssClasses_.TAB);\n            var panels = this.content_.querySelectorAll('.' + this.CssClasses_.PANEL);\n            // Create new tabs for each tab element\n            for (var i = 0; i < tabs.length; i++) {\n                new MaterialLayoutTab(tabs[i], tabs, panels, this);\n            }\n        }\n        this.element_.classList.add(this.CssClasses_.IS_UPGRADED);\n    }\n};\n/**\n   * Constructor for an individual tab.\n   *\n   * @constructor\n   * @param {HTMLElement} tab The HTML element for the tab.\n   * @param {!Array<HTMLElement>} tabs Array with HTML elements for all tabs.\n   * @param {!Array<HTMLElement>} panels Array with HTML elements for all panels.\n   * @param {MaterialLayout} layout The MaterialLayout object that owns the tab.\n   */\nfunction MaterialLayoutTab(tab, tabs, panels, layout) {\n    /**\n     * Auxiliary method to programmatically select a tab in the UI.\n     */\n    function selectTab() {\n        var href = tab.href.split('#')[1];\n        var panel = layout.content_.querySelector('#' + href);\n        layout.resetTabState_(tabs);\n        layout.resetPanelState_(panels);\n        tab.classList.add(layout.CssClasses_.IS_ACTIVE);\n        panel.classList.add(layout.CssClasses_.IS_ACTIVE);\n    }\n    if (layout.tabBar_.classList.contains(layout.CssClasses_.JS_RIPPLE_EFFECT)) {\n        var rippleContainer = document.createElement('span');\n        rippleContainer.classList.add(layout.CssClasses_.RIPPLE_CONTAINER);\n        rippleContainer.classList.add(layout.CssClasses_.JS_RIPPLE_EFFECT);\n        var ripple = document.createElement('span');\n        ripple.classList.add(layout.CssClasses_.RIPPLE);\n        rippleContainer.appendChild(ripple);\n        tab.appendChild(rippleContainer);\n    }\n    if (!layout.tabBar_.classList.contains(layout.CssClasses_.TAB_MANUAL_SWITCH)) {\n        tab.addEventListener('click', function (e) {\n            if (tab.getAttribute('href').charAt(0) === '#') {\n                e.preventDefault();\n                selectTab();\n            }\n        });\n    }\n    tab.show = selectTab;\n}\nwindow['MaterialLayoutTab'] = MaterialLayoutTab;\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialLayout,\n    classAsString: 'MaterialLayout',\n    cssClass: 'mdl-js-layout'\n});", "/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * A component handler interface using the revealing module design pattern.\n * More details on this design pattern here:\n * https://github.com/jasonmayes/mdl-component-design-pattern\n *\n * <AUTHOR>\n */\n/* exported componentHandler */\n\n// Pre-defining the componentHandler interface, for closure documentation and\n// static verification.\nvar componentHandler = {\n  /**\n   * Searches existing DOM for elements of our component type and upgrades them\n   * if they have not already been upgraded.\n   *\n   * @param {string=} optJsClass the programatic name of the element class we\n   * need to create a new instance of.\n   * @param {string=} optCssClass the name of the CSS class elements of this\n   * type will have.\n   */\n  upgradeDom: function(optJsClass, optCssClass) {},\n  /**\n   * Upgrades a specific element rather than all in the DOM.\n   *\n   * @param {!Element} element The element we wish to upgrade.\n   * @param {string=} optJsClass Optional name of the class we want to upgrade\n   * the element to.\n   */\n  upgradeElement: function(element, optJsClass) {},\n  /**\n   * Upgrades a specific list of elements rather than all in the DOM.\n   *\n   * @param {!Element|!Array<!Element>|!NodeList|!HTMLCollection} elements\n   * The elements we wish to upgrade.\n   */\n  upgradeElements: function(elements) {},\n  /**\n   * Upgrades all registered components found in the current DOM. This is\n   * automatically called on window load.\n   */\n  upgradeAllRegistered: function() {},\n  /**\n   * Allows user to be alerted to any upgrades that are performed for a given\n   * component type\n   *\n   * @param {string} jsClass The class name of the MDL component we wish\n   * to hook into for any upgrades performed.\n   * @param {function(!HTMLElement)} callback The function to call upon an\n   * upgrade. This function should expect 1 parameter - the HTMLElement which\n   * got upgraded.\n   */\n  registerUpgradedCallback: function(jsClass, callback) {},\n  /**\n   * Registers a class for future use and attempts to upgrade existing DOM.\n   *\n   * @param {componentHandler.ComponentConfigPublic} config the registration configuration\n   */\n  register: function(config) {},\n  /**\n   * Downgrade either a given node, an array of nodes, or a NodeList.\n   *\n   * @param {!Node|!Array<!Node>|!NodeList} nodes\n   */\n  downgradeElements: function(nodes) {}\n};\n\ncomponentHandler = (function() {\n  'use strict';\n\n  /** @type {!Array<componentHandler.ComponentConfig>} */\n  var registeredComponents_ = [];\n\n  /** @type {!Array<componentHandler.Component>} */\n  var createdComponents_ = [];\n\n  var componentConfigProperty_ = 'mdlComponentConfigInternal_';\n\n  /**\n   * Searches registered components for a class we are interested in using.\n   * Optionally replaces a match with passed object if specified.\n   *\n   * @param {string} name The name of a class we want to use.\n   * @param {componentHandler.ComponentConfig=} optReplace Optional object to replace match with.\n   * @return {!Object|boolean}\n   * @private\n   */\n  function findRegisteredClass_(name, optReplace) {\n    for (var i = 0; i < registeredComponents_.length; i++) {\n      if (registeredComponents_[i].className === name) {\n        if (typeof optReplace !== 'undefined') {\n          registeredComponents_[i] = optReplace;\n        }\n        return registeredComponents_[i];\n      }\n    }\n    return false;\n  }\n\n  /**\n   * Returns an array of the classNames of the upgraded classes on the element.\n   *\n   * @param {!Element} element The element to fetch data from.\n   * @return {!Array<string>}\n   * @private\n   */\n  function getUpgradedListOfElement_(element) {\n    var dataUpgraded = element.getAttribute('data-upgraded');\n    // Use `['']` as default value to conform the `,name,name...` style.\n    return dataUpgraded === null ? [''] : dataUpgraded.split(',');\n  }\n\n  /**\n   * Returns true if the given element has already been upgraded for the given\n   * class.\n   *\n   * @param {!Element} element The element we want to check.\n   * @param {string} jsClass The class to check for.\n   * @returns {boolean}\n   * @private\n   */\n  function isElementUpgraded_(element, jsClass) {\n    var upgradedList = getUpgradedListOfElement_(element);\n    return upgradedList.indexOf(jsClass) !== -1;\n  }\n\n  /**\n   * Create an event object.\n   *\n   * @param {string} eventType The type name of the event.\n   * @param {boolean} bubbles Whether the event should bubble up the DOM.\n   * @param {boolean} cancelable Whether the event can be canceled.\n   * @returns {!Event}\n   */\n  function createEvent_(eventType, bubbles, cancelable) {\n    if ('CustomEvent' in window && typeof window.CustomEvent === 'function') {\n      return new CustomEvent(eventType, {\n        bubbles: bubbles,\n        cancelable: cancelable\n      });\n    } else {\n      var ev = document.createEvent('Events');\n      ev.initEvent(eventType, bubbles, cancelable);\n      return ev;\n    }\n  }\n\n  /**\n   * Searches existing DOM for elements of our component type and upgrades them\n   * if they have not already been upgraded.\n   *\n   * @param {string=} optJsClass the programatic name of the element class we\n   * need to create a new instance of.\n   * @param {string=} optCssClass the name of the CSS class elements of this\n   * type will have.\n   */\n  function upgradeDomInternal(optJsClass, optCssClass) {\n    if (typeof optJsClass === 'undefined' &&\n        typeof optCssClass === 'undefined') {\n      for (var i = 0; i < registeredComponents_.length; i++) {\n        upgradeDomInternal(registeredComponents_[i].className,\n            registeredComponents_[i].cssClass);\n      }\n    } else {\n      var jsClass = /** @type {string} */ (optJsClass);\n      if (typeof optCssClass === 'undefined') {\n        var registeredClass = findRegisteredClass_(jsClass);\n        if (registeredClass) {\n          optCssClass = registeredClass.cssClass;\n        }\n      }\n\n      var elements = document.querySelectorAll('.' + optCssClass);\n      for (var n = 0; n < elements.length; n++) {\n        upgradeElementInternal(elements[n], jsClass);\n      }\n    }\n  }\n\n  /**\n   * Upgrades a specific element rather than all in the DOM.\n   *\n   * @param {!Element} element The element we wish to upgrade.\n   * @param {string=} optJsClass Optional name of the class we want to upgrade\n   * the element to.\n   */\n  function upgradeElementInternal(element, optJsClass) {\n    // Verify argument type.\n    if (!(typeof element === 'object' && element instanceof Element)) {\n      throw new Error('Invalid argument provided to upgrade MDL element.');\n    }\n    // Allow upgrade to be canceled by canceling emitted event.\n    var upgradingEv = createEvent_('mdl-componentupgrading', true, true);\n    element.dispatchEvent(upgradingEv);\n    if (upgradingEv.defaultPrevented) {\n      return;\n    }\n\n    var upgradedList = getUpgradedListOfElement_(element);\n    var classesToUpgrade = [];\n    // If jsClass is not provided scan the registered components to find the\n    // ones matching the element's CSS classList.\n    if (!optJsClass) {\n      var classList = element.classList;\n      registeredComponents_.forEach(function(component) {\n        // Match CSS & Not to be upgraded & Not upgraded.\n        if (classList.contains(component.cssClass) &&\n            classesToUpgrade.indexOf(component) === -1 &&\n            !isElementUpgraded_(element, component.className)) {\n          classesToUpgrade.push(component);\n        }\n      });\n    } else if (!isElementUpgraded_(element, optJsClass)) {\n      classesToUpgrade.push(findRegisteredClass_(optJsClass));\n    }\n\n    // Upgrade the element for each classes.\n    for (var i = 0, n = classesToUpgrade.length, registeredClass; i < n; i++) {\n      registeredClass = classesToUpgrade[i];\n      if (registeredClass) {\n        // Mark element as upgraded.\n        upgradedList.push(registeredClass.className);\n        element.setAttribute('data-upgraded', upgradedList.join(','));\n        var instance = new registeredClass.classConstructor(element);\n        instance[componentConfigProperty_] = registeredClass;\n        createdComponents_.push(instance);\n        // Call any callbacks the user has registered with this component type.\n        for (var j = 0, m = registeredClass.callbacks.length; j < m; j++) {\n          registeredClass.callbacks[j](element);\n        }\n\n        if (registeredClass.widget) {\n          // Assign per element instance for control over API\n          element[registeredClass.className] = instance;\n        }\n      } else {\n        throw new Error(\n          'Unable to find a registered component for the given class.');\n      }\n\n      var upgradedEv = createEvent_('mdl-componentupgraded', true, false);\n      element.dispatchEvent(upgradedEv);\n    }\n  }\n\n  /**\n   * Upgrades a specific list of elements rather than all in the DOM.\n   *\n   * @param {!Element|!Array<!Element>|!NodeList|!HTMLCollection} elements\n   * The elements we wish to upgrade.\n   */\n  function upgradeElementsInternal(elements) {\n    if (!Array.isArray(elements)) {\n      if (elements instanceof Element) {\n        elements = [elements];\n      } else {\n        elements = Array.prototype.slice.call(elements);\n      }\n    }\n    for (var i = 0, n = elements.length, element; i < n; i++) {\n      element = elements[i];\n      if (element instanceof HTMLElement) {\n        upgradeElementInternal(element);\n        if (element.children.length > 0) {\n          upgradeElementsInternal(element.children);\n        }\n      }\n    }\n  }\n\n  /**\n   * Registers a class for future use and attempts to upgrade existing DOM.\n   *\n   * @param {componentHandler.ComponentConfigPublic} config\n   */\n  function registerInternal(config) {\n    // In order to support both Closure-compiled and uncompiled code accessing\n    // this method, we need to allow for both the dot and array syntax for\n    // property access. You'll therefore see the `foo.bar || foo['bar']`\n    // pattern repeated across this method.\n    var widgetMissing = (typeof config.widget === 'undefined' &&\n        typeof config['widget'] === 'undefined');\n    var widget = true;\n\n    if (!widgetMissing) {\n      widget = config.widget || config['widget'];\n    }\n\n    var newConfig = /** @type {componentHandler.ComponentConfig} */ ({\n      classConstructor: config.constructor || config['constructor'],\n      className: config.classAsString || config['classAsString'],\n      cssClass: config.cssClass || config['cssClass'],\n      widget: widget,\n      callbacks: []\n    });\n\n    registeredComponents_.forEach(function(item) {\n      if (item.cssClass === newConfig.cssClass) {\n        throw new Error('The provided cssClass has already been registered: ' + item.cssClass);\n      }\n      if (item.className === newConfig.className) {\n        throw new Error('The provided className has already been registered');\n      }\n    });\n\n    if (config.constructor.prototype\n        .hasOwnProperty(componentConfigProperty_)) {\n      throw new Error(\n          'MDL component classes must not have ' + componentConfigProperty_ +\n          ' defined as a property.');\n    }\n\n    var found = findRegisteredClass_(config.classAsString, newConfig);\n\n    if (!found) {\n      registeredComponents_.push(newConfig);\n    }\n  }\n\n  /**\n   * Allows user to be alerted to any upgrades that are performed for a given\n   * component type\n   *\n   * @param {string} jsClass The class name of the MDL component we wish\n   * to hook into for any upgrades performed.\n   * @param {function(!HTMLElement)} callback The function to call upon an\n   * upgrade. This function should expect 1 parameter - the HTMLElement which\n   * got upgraded.\n   */\n  function registerUpgradedCallbackInternal(jsClass, callback) {\n    var regClass = findRegisteredClass_(jsClass);\n    if (regClass) {\n      regClass.callbacks.push(callback);\n    }\n  }\n\n  /**\n   * Upgrades all registered components found in the current DOM. This is\n   * automatically called on window load.\n   */\n  function upgradeAllRegisteredInternal() {\n    for (var n = 0; n < registeredComponents_.length; n++) {\n      upgradeDomInternal(registeredComponents_[n].className);\n    }\n  }\n\n  /**\n   * Check the component for the downgrade method.\n   * Execute if found.\n   * Remove component from createdComponents list.\n   *\n   * @param {?componentHandler.Component} component\n   */\n  function deconstructComponentInternal(component) {\n    if (component) {\n      var componentIndex = createdComponents_.indexOf(component);\n      createdComponents_.splice(componentIndex, 1);\n\n      var upgrades = component.element_.getAttribute('data-upgraded').split(',');\n      var componentPlace = upgrades.indexOf(component[componentConfigProperty_].classAsString);\n      upgrades.splice(componentPlace, 1);\n      component.element_.setAttribute('data-upgraded', upgrades.join(','));\n\n      var ev = createEvent_('mdl-componentdowngraded', true, false);\n      component.element_.dispatchEvent(ev);\n    }\n  }\n\n  /**\n   * Downgrade either a given node, an array of nodes, or a NodeList.\n   *\n   * @param {!Node|!Array<!Node>|!NodeList} nodes\n   */\n  function downgradeNodesInternal(nodes) {\n    /**\n     * Auxiliary function to downgrade a single node.\n     * @param  {!Node} node the node to be downgraded\n     */\n    var downgradeNode = function(node) {\n      createdComponents_.filter(function(item) {\n        return item.element_ === node;\n      }).forEach(deconstructComponentInternal);\n    };\n    if (nodes instanceof Array || nodes instanceof NodeList) {\n      for (var n = 0; n < nodes.length; n++) {\n        downgradeNode(nodes[n]);\n      }\n    } else if (nodes instanceof Node) {\n      downgradeNode(nodes);\n    } else {\n      throw new Error('Invalid argument provided to downgrade MDL nodes.');\n    }\n  }\n\n  // Now return the functions that should be made public with their publicly\n  // facing names...\n  return {\n    upgradeDom: upgradeDomInternal,\n    upgradeElement: upgradeElementInternal,\n    upgradeElements: upgradeElementsInternal,\n    upgradeAllRegistered: upgradeAllRegisteredInternal,\n    registerUpgradedCallback: registerUpgradedCallbackInternal,\n    register: registerInternal,\n    downgradeElements: downgradeNodesInternal\n  };\n})();\n\n/**\n * Describes the type of a registered component type managed by\n * componentHandler. Provided for benefit of the Closure compiler.\n *\n * @typedef {{\n *   constructor: Function,\n *   classAsString: string,\n *   cssClass: string,\n *   widget: (string|boolean|undefined)\n * }}\n */\ncomponentHandler.ComponentConfigPublic;  // jshint ignore:line\n\n/**\n * Describes the type of a registered component type managed by\n * componentHandler. Provided for benefit of the Closure compiler.\n *\n * @typedef {{\n *   constructor: !Function,\n *   className: string,\n *   cssClass: string,\n *   widget: (string|boolean),\n *   callbacks: !Array<function(!HTMLElement)>\n * }}\n */\ncomponentHandler.ComponentConfig;  // jshint ignore:line\n\n/**\n * Created component (i.e., upgraded element) type as managed by\n * componentHandler. Provided for benefit of the Closure compiler.\n *\n * @typedef {{\n *   element_: !HTMLElement,\n *   className: string,\n *   classAsString: string,\n *   cssClass: string,\n *   widget: string\n * }}\n */\ncomponentHandler.Component;  // jshint ignore:line\n\n// Export all symbols, for the benefit of Closure compiler.\n// No effect on uncompiled code.\ncomponentHandler['upgradeDom'] = componentHandler.upgradeDom;\ncomponentHandler['upgradeElement'] = componentHandler.upgradeElement;\ncomponentHandler['upgradeElements'] = componentHandler.upgradeElements;\ncomponentHandler['upgradeAllRegistered'] =\n    componentHandler.upgradeAllRegistered;\ncomponentHandler['registerUpgradedCallback'] =\n    componentHandler.registerUpgradedCallback;\ncomponentHandler['register'] = componentHandler.register;\ncomponentHandler['downgradeElements'] = componentHandler.downgradeElements;\nwindow.componentHandler = componentHandler;\nwindow['componentHandler'] = componentHandler;\n\nwindow.addEventListener('load', function() {\n  'use strict';\n\n  /**\n   * Performs a \"Cutting the mustard\" test. If the browser supports the features\n   * tested, adds a mdl-js class to the <html> element. It then upgrades all MDL\n   * components requiring JavaScript.\n   */\n  if ('classList' in document.createElement('div') &&\n      'querySelector' in document &&\n      'addEventListener' in window && Array.prototype.forEach) {\n    document.documentElement.classList.add('mdl-js');\n    componentHandler.upgradeAllRegistered();\n  } else {\n    /**\n     * Dummy function to avoid JS errors.\n     */\n    componentHandler.upgradeElement = function() {};\n    /**\n     * Dummy function to avoid JS errors.\n     */\n    componentHandler.register = function() {};\n  }\n});\n", "// Source: https://github.com/darius/requestAnimationFrame/blob/master/requestAnimationFrame.js\n// Adapted from https://gist.github.com/paulirish/1579671 which derived from\n// http://paulirish.com/2011/requestanimationframe-for-smart-animating/\n// http://my.opera.com/emoller/blog/2011/12/20/requestanimationframe-for-smart-er-animating\n// requestAnimationFrame polyfill by <PERSON>.\n// Fixes from Paul <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>\n// MIT license\nif (!Date.now) {\n    /**\n     * Date.now polyfill.\n     * @return {number} the current Date\n     */\n    Date.now = function () {\n        return new Date().getTime();\n    };\n    Date['now'] = Date.now;\n}\nvar vendors = [\n    'webkit',\n    'moz'\n];\nfor (var i = 0; i < vendors.length && !window.requestAnimationFrame; ++i) {\n    var vp = vendors[i];\n    window.requestAnimationFrame = window[vp + 'RequestAnimationFrame'];\n    window.cancelAnimationFrame = window[vp + 'CancelAnimationFrame'] || window[vp + 'CancelRequestAnimationFrame'];\n    window['requestAnimationFrame'] = window.requestAnimationFrame;\n    window['cancelAnimationFrame'] = window.cancelAnimationFrame;\n}\nif (/iP(ad|hone|od).*OS 6/.test(window.navigator.userAgent) || !window.requestAnimationFrame || !window.cancelAnimationFrame) {\n    var lastTime = 0;\n    /**\n     * requestAnimationFrame polyfill.\n     * @param  {!Function} callback the callback function.\n     */\n    window.requestAnimationFrame = function (callback) {\n        var now = Date.now();\n        var nextTime = Math.max(lastTime + 16, now);\n        return setTimeout(function () {\n            callback(lastTime = nextTime);\n        }, nextTime - now);\n    };\n    window.cancelAnimationFrame = clearTimeout;\n    window['requestAnimationFrame'] = window.requestAnimationFrame;\n    window['cancelAnimationFrame'] = window.cancelAnimationFrame;\n}", "/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for Button MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @param {HTMLElement} element The element that will be upgraded.\n   */\nvar MaterialButton = function MaterialButton(element) {\n    this.element_ = element;\n    // Initialize instance.\n    this.init();\n};\nwindow['MaterialButton'] = MaterialButton;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string | number}\n   * @private\n   */\nMaterialButton.prototype.Constant_ = {};\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialButton.prototype.CssClasses_ = {\n    RIPPLE_EFFECT: 'mdl-js-ripple-effect',\n    RIPPLE_CONTAINER: 'mdl-button__ripple-container',\n    RIPPLE: 'mdl-ripple'\n};\n/**\n   * Handle blur of element.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialButton.prototype.blurHandler_ = function (event) {\n    if (event) {\n        this.element_.blur();\n    }\n};\n// Public methods.\n/**\n   * Disable button.\n   *\n   * @public\n   */\nMaterialButton.prototype.disable = function () {\n    this.element_.disabled = true;\n};\nMaterialButton.prototype['disable'] = MaterialButton.prototype.disable;\n/**\n   * Enable button.\n   *\n   * @public\n   */\nMaterialButton.prototype.enable = function () {\n    this.element_.disabled = false;\n};\nMaterialButton.prototype['enable'] = MaterialButton.prototype.enable;\n/**\n   * Initialize element.\n   */\nMaterialButton.prototype.init = function () {\n    if (this.element_) {\n        if (this.element_.classList.contains(this.CssClasses_.RIPPLE_EFFECT)) {\n            var rippleContainer = document.createElement('span');\n            rippleContainer.classList.add(this.CssClasses_.RIPPLE_CONTAINER);\n            this.rippleElement_ = document.createElement('span');\n            this.rippleElement_.classList.add(this.CssClasses_.RIPPLE);\n            rippleContainer.appendChild(this.rippleElement_);\n            this.boundRippleBlurHandler = this.blurHandler_.bind(this);\n            this.rippleElement_.addEventListener('mouseup', this.boundRippleBlurHandler);\n            this.element_.appendChild(rippleContainer);\n        }\n        this.boundButtonBlurHandler = this.blurHandler_.bind(this);\n        this.element_.addEventListener('mouseup', this.boundButtonBlurHandler);\n        this.element_.addEventListener('mouseleave', this.boundButtonBlurHandler);\n    }\n};\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialButton,\n    classAsString: 'MaterialButton',\n    cssClass: 'mdl-js-button',\n    widget: true\n});", "/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for Checkbox MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @constructor\n   * @param {HTMLElement} element The element that will be upgraded.\n   */\nvar MaterialCheckbox = function MaterialCheckbox(element) {\n    this.element_ = element;\n    // Initialize instance.\n    this.init();\n};\nwindow['MaterialCheckbox'] = MaterialCheckbox;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string | number}\n   * @private\n   */\nMaterialCheckbox.prototype.Constant_ = { TINY_TIMEOUT: 0.001 };\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialCheckbox.prototype.CssClasses_ = {\n    INPUT: 'mdl-checkbox__input',\n    BOX_OUTLINE: 'mdl-checkbox__box-outline',\n    FOCUS_HELPER: 'mdl-checkbox__focus-helper',\n    TICK_OUTLINE: 'mdl-checkbox__tick-outline',\n    RIPPLE_EFFECT: 'mdl-js-ripple-effect',\n    RIPPLE_IGNORE_EVENTS: 'mdl-js-ripple-effect--ignore-events',\n    RIPPLE_CONTAINER: 'mdl-checkbox__ripple-container',\n    RIPPLE_CENTER: 'mdl-ripple--center',\n    RIPPLE: 'mdl-ripple',\n    IS_FOCUSED: 'is-focused',\n    IS_DISABLED: 'is-disabled',\n    IS_CHECKED: 'is-checked',\n    IS_UPGRADED: 'is-upgraded'\n};\n/**\n   * Handle change of state.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialCheckbox.prototype.onChange_ = function (event) {\n    this.updateClasses_();\n};\n/**\n   * Handle focus of element.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialCheckbox.prototype.onFocus_ = function (event) {\n    this.element_.classList.add(this.CssClasses_.IS_FOCUSED);\n};\n/**\n   * Handle lost focus of element.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialCheckbox.prototype.onBlur_ = function (event) {\n    this.element_.classList.remove(this.CssClasses_.IS_FOCUSED);\n};\n/**\n   * Handle mouseup.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialCheckbox.prototype.onMouseUp_ = function (event) {\n    this.blur_();\n};\n/**\n   * Handle class updates.\n   *\n   * @private\n   */\nMaterialCheckbox.prototype.updateClasses_ = function () {\n    this.checkDisabled();\n    this.checkToggleState();\n};\n/**\n   * Add blur.\n   *\n   * @private\n   */\nMaterialCheckbox.prototype.blur_ = function () {\n    // TODO: figure out why there's a focus event being fired after our blur,\n    // so that we can avoid this hack.\n    window.setTimeout(function () {\n        this.inputElement_.blur();\n    }.bind(this), this.Constant_.TINY_TIMEOUT);\n};\n// Public methods.\n/**\n   * Check the inputs toggle state and update display.\n   *\n   * @public\n   */\nMaterialCheckbox.prototype.checkToggleState = function () {\n    if (this.inputElement_.checked) {\n        this.element_.classList.add(this.CssClasses_.IS_CHECKED);\n    } else {\n        this.element_.classList.remove(this.CssClasses_.IS_CHECKED);\n    }\n};\nMaterialCheckbox.prototype['checkToggleState'] = MaterialCheckbox.prototype.checkToggleState;\n/**\n   * Check the inputs disabled state and update display.\n   *\n   * @public\n   */\nMaterialCheckbox.prototype.checkDisabled = function () {\n    if (this.inputElement_.disabled) {\n        this.element_.classList.add(this.CssClasses_.IS_DISABLED);\n    } else {\n        this.element_.classList.remove(this.CssClasses_.IS_DISABLED);\n    }\n};\nMaterialCheckbox.prototype['checkDisabled'] = MaterialCheckbox.prototype.checkDisabled;\n/**\n   * Disable checkbox.\n   *\n   * @public\n   */\nMaterialCheckbox.prototype.disable = function () {\n    this.inputElement_.disabled = true;\n    this.updateClasses_();\n};\nMaterialCheckbox.prototype['disable'] = MaterialCheckbox.prototype.disable;\n/**\n   * Enable checkbox.\n   *\n   * @public\n   */\nMaterialCheckbox.prototype.enable = function () {\n    this.inputElement_.disabled = false;\n    this.updateClasses_();\n};\nMaterialCheckbox.prototype['enable'] = MaterialCheckbox.prototype.enable;\n/**\n   * Check checkbox.\n   *\n   * @public\n   */\nMaterialCheckbox.prototype.check = function () {\n    this.inputElement_.checked = true;\n    this.updateClasses_();\n};\nMaterialCheckbox.prototype['check'] = MaterialCheckbox.prototype.check;\n/**\n   * Uncheck checkbox.\n   *\n   * @public\n   */\nMaterialCheckbox.prototype.uncheck = function () {\n    this.inputElement_.checked = false;\n    this.updateClasses_();\n};\nMaterialCheckbox.prototype['uncheck'] = MaterialCheckbox.prototype.uncheck;\n/**\n   * Initialize element.\n   */\nMaterialCheckbox.prototype.init = function () {\n    if (this.element_) {\n        this.inputElement_ = this.element_.querySelector('.' + this.CssClasses_.INPUT);\n        var boxOutline = document.createElement('span');\n        boxOutline.classList.add(this.CssClasses_.BOX_OUTLINE);\n        var tickContainer = document.createElement('span');\n        tickContainer.classList.add(this.CssClasses_.FOCUS_HELPER);\n        var tickOutline = document.createElement('span');\n        tickOutline.classList.add(this.CssClasses_.TICK_OUTLINE);\n        boxOutline.appendChild(tickOutline);\n        this.element_.appendChild(tickContainer);\n        this.element_.appendChild(boxOutline);\n        if (this.element_.classList.contains(this.CssClasses_.RIPPLE_EFFECT)) {\n            this.element_.classList.add(this.CssClasses_.RIPPLE_IGNORE_EVENTS);\n            this.rippleContainerElement_ = document.createElement('span');\n            this.rippleContainerElement_.classList.add(this.CssClasses_.RIPPLE_CONTAINER);\n            this.rippleContainerElement_.classList.add(this.CssClasses_.RIPPLE_EFFECT);\n            this.rippleContainerElement_.classList.add(this.CssClasses_.RIPPLE_CENTER);\n            this.boundRippleMouseUp = this.onMouseUp_.bind(this);\n            this.rippleContainerElement_.addEventListener('mouseup', this.boundRippleMouseUp);\n            var ripple = document.createElement('span');\n            ripple.classList.add(this.CssClasses_.RIPPLE);\n            this.rippleContainerElement_.appendChild(ripple);\n            this.element_.appendChild(this.rippleContainerElement_);\n        }\n        this.boundInputOnChange = this.onChange_.bind(this);\n        this.boundInputOnFocus = this.onFocus_.bind(this);\n        this.boundInputOnBlur = this.onBlur_.bind(this);\n        this.boundElementMouseUp = this.onMouseUp_.bind(this);\n        this.inputElement_.addEventListener('change', this.boundInputOnChange);\n        this.inputElement_.addEventListener('focus', this.boundInputOnFocus);\n        this.inputElement_.addEventListener('blur', this.boundInputOnBlur);\n        this.element_.addEventListener('mouseup', this.boundElementMouseUp);\n        this.updateClasses_();\n        this.element_.classList.add(this.CssClasses_.IS_UPGRADED);\n    }\n};\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialCheckbox,\n    classAsString: 'MaterialCheckbox',\n    cssClass: 'mdl-js-checkbox',\n    widget: true\n});", "/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for icon toggle MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @constructor\n   * @param {HTMLElement} element The element that will be upgraded.\n   */\nvar MaterialIconToggle = function MaterialIconToggle(element) {\n    this.element_ = element;\n    // Initialize instance.\n    this.init();\n};\nwindow['MaterialIconToggle'] = MaterialIconToggle;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string | number}\n   * @private\n   */\nMaterialIconToggle.prototype.Constant_ = { TINY_TIMEOUT: 0.001 };\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialIconToggle.prototype.CssClasses_ = {\n    INPUT: 'mdl-icon-toggle__input',\n    JS_RIPPLE_EFFECT: 'mdl-js-ripple-effect',\n    RIPPLE_IGNORE_EVENTS: 'mdl-js-ripple-effect--ignore-events',\n    RIPPLE_CONTAINER: 'mdl-icon-toggle__ripple-container',\n    RIPPLE_CENTER: 'mdl-ripple--center',\n    RIPPLE: 'mdl-ripple',\n    IS_FOCUSED: 'is-focused',\n    IS_DISABLED: 'is-disabled',\n    IS_CHECKED: 'is-checked'\n};\n/**\n   * Handle change of state.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialIconToggle.prototype.onChange_ = function (event) {\n    this.updateClasses_();\n};\n/**\n   * Handle focus of element.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialIconToggle.prototype.onFocus_ = function (event) {\n    this.element_.classList.add(this.CssClasses_.IS_FOCUSED);\n};\n/**\n   * Handle lost focus of element.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialIconToggle.prototype.onBlur_ = function (event) {\n    this.element_.classList.remove(this.CssClasses_.IS_FOCUSED);\n};\n/**\n   * Handle mouseup.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialIconToggle.prototype.onMouseUp_ = function (event) {\n    this.blur_();\n};\n/**\n   * Handle class updates.\n   *\n   * @private\n   */\nMaterialIconToggle.prototype.updateClasses_ = function () {\n    this.checkDisabled();\n    this.checkToggleState();\n};\n/**\n   * Add blur.\n   *\n   * @private\n   */\nMaterialIconToggle.prototype.blur_ = function () {\n    // TODO: figure out why there's a focus event being fired after our blur,\n    // so that we can avoid this hack.\n    window.setTimeout(function () {\n        this.inputElement_.blur();\n    }.bind(this), this.Constant_.TINY_TIMEOUT);\n};\n// Public methods.\n/**\n   * Check the inputs toggle state and update display.\n   *\n   * @public\n   */\nMaterialIconToggle.prototype.checkToggleState = function () {\n    if (this.inputElement_.checked) {\n        this.element_.classList.add(this.CssClasses_.IS_CHECKED);\n    } else {\n        this.element_.classList.remove(this.CssClasses_.IS_CHECKED);\n    }\n};\nMaterialIconToggle.prototype['checkToggleState'] = MaterialIconToggle.prototype.checkToggleState;\n/**\n   * Check the inputs disabled state and update display.\n   *\n   * @public\n   */\nMaterialIconToggle.prototype.checkDisabled = function () {\n    if (this.inputElement_.disabled) {\n        this.element_.classList.add(this.CssClasses_.IS_DISABLED);\n    } else {\n        this.element_.classList.remove(this.CssClasses_.IS_DISABLED);\n    }\n};\nMaterialIconToggle.prototype['checkDisabled'] = MaterialIconToggle.prototype.checkDisabled;\n/**\n   * Disable icon toggle.\n   *\n   * @public\n   */\nMaterialIconToggle.prototype.disable = function () {\n    this.inputElement_.disabled = true;\n    this.updateClasses_();\n};\nMaterialIconToggle.prototype['disable'] = MaterialIconToggle.prototype.disable;\n/**\n   * Enable icon toggle.\n   *\n   * @public\n   */\nMaterialIconToggle.prototype.enable = function () {\n    this.inputElement_.disabled = false;\n    this.updateClasses_();\n};\nMaterialIconToggle.prototype['enable'] = MaterialIconToggle.prototype.enable;\n/**\n   * Check icon toggle.\n   *\n   * @public\n   */\nMaterialIconToggle.prototype.check = function () {\n    this.inputElement_.checked = true;\n    this.updateClasses_();\n};\nMaterialIconToggle.prototype['check'] = MaterialIconToggle.prototype.check;\n/**\n   * Uncheck icon toggle.\n   *\n   * @public\n   */\nMaterialIconToggle.prototype.uncheck = function () {\n    this.inputElement_.checked = false;\n    this.updateClasses_();\n};\nMaterialIconToggle.prototype['uncheck'] = MaterialIconToggle.prototype.uncheck;\n/**\n   * Initialize element.\n   */\nMaterialIconToggle.prototype.init = function () {\n    if (this.element_) {\n        this.inputElement_ = this.element_.querySelector('.' + this.CssClasses_.INPUT);\n        if (this.element_.classList.contains(this.CssClasses_.JS_RIPPLE_EFFECT)) {\n            this.element_.classList.add(this.CssClasses_.RIPPLE_IGNORE_EVENTS);\n            this.rippleContainerElement_ = document.createElement('span');\n            this.rippleContainerElement_.classList.add(this.CssClasses_.RIPPLE_CONTAINER);\n            this.rippleContainerElement_.classList.add(this.CssClasses_.JS_RIPPLE_EFFECT);\n            this.rippleContainerElement_.classList.add(this.CssClasses_.RIPPLE_CENTER);\n            this.boundRippleMouseUp = this.onMouseUp_.bind(this);\n            this.rippleContainerElement_.addEventListener('mouseup', this.boundRippleMouseUp);\n            var ripple = document.createElement('span');\n            ripple.classList.add(this.CssClasses_.RIPPLE);\n            this.rippleContainerElement_.appendChild(ripple);\n            this.element_.appendChild(this.rippleContainerElement_);\n        }\n        this.boundInputOnChange = this.onChange_.bind(this);\n        this.boundInputOnFocus = this.onFocus_.bind(this);\n        this.boundInputOnBlur = this.onBlur_.bind(this);\n        this.boundElementOnMouseUp = this.onMouseUp_.bind(this);\n        this.inputElement_.addEventListener('change', this.boundInputOnChange);\n        this.inputElement_.addEventListener('focus', this.boundInputOnFocus);\n        this.inputElement_.addEventListener('blur', this.boundInputOnBlur);\n        this.element_.addEventListener('mouseup', this.boundElementOnMouseUp);\n        this.updateClasses_();\n        this.element_.classList.add('is-upgraded');\n    }\n};\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialIconToggle,\n    classAsString: 'MaterialIconToggle',\n    cssClass: 'mdl-js-icon-toggle',\n    widget: true\n});", "/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for dropdown MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @constructor\n   * @param {HTMLElement} element The element that will be upgraded.\n   */\nvar MaterialMenu = function MaterialMenu(element) {\n    this.element_ = element;\n    // Initialize instance.\n    this.init();\n};\nwindow['MaterialMenu'] = MaterialMenu;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string | number}\n   * @private\n   */\nMaterialMenu.prototype.Constant_ = {\n    // Total duration of the menu animation.\n    TRANSITION_DURATION_SECONDS: 0.3,\n    // The fraction of the total duration we want to use for menu item animations.\n    TRANSITION_DURATION_FRACTION: 0.8,\n    // How long the menu stays open after choosing an option (so the user can see\n    // the ripple).\n    CLOSE_TIMEOUT: 150\n};\n/**\n   * Keycodes, for code readability.\n   *\n   * @enum {number}\n   * @private\n   */\nMaterialMenu.prototype.Keycodes_ = {\n    ENTER: 13,\n    ESCAPE: 27,\n    SPACE: 32,\n    UP_ARROW: 38,\n    DOWN_ARROW: 40\n};\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialMenu.prototype.CssClasses_ = {\n    CONTAINER: 'mdl-menu__container',\n    OUTLINE: 'mdl-menu__outline',\n    ITEM: 'mdl-menu__item',\n    ITEM_RIPPLE_CONTAINER: 'mdl-menu__item-ripple-container',\n    RIPPLE_EFFECT: 'mdl-js-ripple-effect',\n    RIPPLE_IGNORE_EVENTS: 'mdl-js-ripple-effect--ignore-events',\n    RIPPLE: 'mdl-ripple',\n    // Statuses\n    IS_UPGRADED: 'is-upgraded',\n    IS_VISIBLE: 'is-visible',\n    IS_ANIMATING: 'is-animating',\n    // Alignment options\n    BOTTOM_LEFT: 'mdl-menu--bottom-left',\n    // This is the default.\n    BOTTOM_RIGHT: 'mdl-menu--bottom-right',\n    TOP_LEFT: 'mdl-menu--top-left',\n    TOP_RIGHT: 'mdl-menu--top-right',\n    UNALIGNED: 'mdl-menu--unaligned'\n};\n/**\n   * Initialize element.\n   */\nMaterialMenu.prototype.init = function () {\n    if (this.element_) {\n        // Create container for the menu.\n        var container = document.createElement('div');\n        container.classList.add(this.CssClasses_.CONTAINER);\n        this.element_.parentElement.insertBefore(container, this.element_);\n        this.element_.parentElement.removeChild(this.element_);\n        container.appendChild(this.element_);\n        this.container_ = container;\n        // Create outline for the menu (shadow and background).\n        var outline = document.createElement('div');\n        outline.classList.add(this.CssClasses_.OUTLINE);\n        this.outline_ = outline;\n        container.insertBefore(outline, this.element_);\n        // Find the \"for\" element and bind events to it.\n        var forElId = this.element_.getAttribute('for') || this.element_.getAttribute('data-mdl-for');\n        var forEl = null;\n        if (forElId) {\n            forEl = document.getElementById(forElId);\n            if (forEl) {\n                this.forElement_ = forEl;\n                forEl.addEventListener('click', this.handleForClick_.bind(this));\n                forEl.addEventListener('keydown', this.handleForKeyboardEvent_.bind(this));\n            }\n        }\n        var items = this.element_.querySelectorAll('.' + this.CssClasses_.ITEM);\n        this.boundItemKeydown_ = this.handleItemKeyboardEvent_.bind(this);\n        this.boundItemClick_ = this.handleItemClick_.bind(this);\n        for (var i = 0; i < items.length; i++) {\n            // Add a listener to each menu item.\n            items[i].addEventListener('click', this.boundItemClick_);\n            // Add a tab index to each menu item.\n            items[i].tabIndex = '-1';\n            // Add a keyboard listener to each menu item.\n            items[i].addEventListener('keydown', this.boundItemKeydown_);\n        }\n        // Add ripple classes to each item, if the user has enabled ripples.\n        if (this.element_.classList.contains(this.CssClasses_.RIPPLE_EFFECT)) {\n            this.element_.classList.add(this.CssClasses_.RIPPLE_IGNORE_EVENTS);\n            for (i = 0; i < items.length; i++) {\n                var item = items[i];\n                var rippleContainer = document.createElement('span');\n                rippleContainer.classList.add(this.CssClasses_.ITEM_RIPPLE_CONTAINER);\n                var ripple = document.createElement('span');\n                ripple.classList.add(this.CssClasses_.RIPPLE);\n                rippleContainer.appendChild(ripple);\n                item.appendChild(rippleContainer);\n                item.classList.add(this.CssClasses_.RIPPLE_EFFECT);\n            }\n        }\n        // Copy alignment classes to the container, so the outline can use them.\n        if (this.element_.classList.contains(this.CssClasses_.BOTTOM_LEFT)) {\n            this.outline_.classList.add(this.CssClasses_.BOTTOM_LEFT);\n        }\n        if (this.element_.classList.contains(this.CssClasses_.BOTTOM_RIGHT)) {\n            this.outline_.classList.add(this.CssClasses_.BOTTOM_RIGHT);\n        }\n        if (this.element_.classList.contains(this.CssClasses_.TOP_LEFT)) {\n            this.outline_.classList.add(this.CssClasses_.TOP_LEFT);\n        }\n        if (this.element_.classList.contains(this.CssClasses_.TOP_RIGHT)) {\n            this.outline_.classList.add(this.CssClasses_.TOP_RIGHT);\n        }\n        if (this.element_.classList.contains(this.CssClasses_.UNALIGNED)) {\n            this.outline_.classList.add(this.CssClasses_.UNALIGNED);\n        }\n        container.classList.add(this.CssClasses_.IS_UPGRADED);\n    }\n};\n/**\n   * Handles a click on the \"for\" element, by positioning the menu and then\n   * toggling it.\n   *\n   * @param {Event} evt The event that fired.\n   * @private\n   */\nMaterialMenu.prototype.handleForClick_ = function (evt) {\n    if (this.element_ && this.forElement_) {\n        var rect = this.forElement_.getBoundingClientRect();\n        var forRect = this.forElement_.parentElement.getBoundingClientRect();\n        if (this.element_.classList.contains(this.CssClasses_.UNALIGNED)) {\n        } else if (this.element_.classList.contains(this.CssClasses_.BOTTOM_RIGHT)) {\n            // Position below the \"for\" element, aligned to its right.\n            this.container_.style.right = forRect.right - rect.right + 'px';\n            this.container_.style.top = this.forElement_.offsetTop + this.forElement_.offsetHeight + 'px';\n        } else if (this.element_.classList.contains(this.CssClasses_.TOP_LEFT)) {\n            // Position above the \"for\" element, aligned to its left.\n            this.container_.style.left = this.forElement_.offsetLeft + 'px';\n            this.container_.style.bottom = forRect.bottom - rect.top + 'px';\n        } else if (this.element_.classList.contains(this.CssClasses_.TOP_RIGHT)) {\n            // Position above the \"for\" element, aligned to its right.\n            this.container_.style.right = forRect.right - rect.right + 'px';\n            this.container_.style.bottom = forRect.bottom - rect.top + 'px';\n        } else {\n            // Default: position below the \"for\" element, aligned to its left.\n            this.container_.style.left = this.forElement_.offsetLeft + 'px';\n            this.container_.style.top = this.forElement_.offsetTop + this.forElement_.offsetHeight + 'px';\n        }\n    }\n    this.toggle(evt);\n};\n/**\n   * Handles a keyboard event on the \"for\" element.\n   *\n   * @param {Event} evt The event that fired.\n   * @private\n   */\nMaterialMenu.prototype.handleForKeyboardEvent_ = function (evt) {\n    if (this.element_ && this.container_ && this.forElement_) {\n        var items = this.element_.querySelectorAll('.' + this.CssClasses_.ITEM + ':not([disabled])');\n        if (items && items.length > 0 && this.container_.classList.contains(this.CssClasses_.IS_VISIBLE)) {\n            if (evt.keyCode === this.Keycodes_.UP_ARROW) {\n                evt.preventDefault();\n                items[items.length - 1].focus();\n            } else if (evt.keyCode === this.Keycodes_.DOWN_ARROW) {\n                evt.preventDefault();\n                items[0].focus();\n            }\n        }\n    }\n};\n/**\n   * Handles a keyboard event on an item.\n   *\n   * @param {Event} evt The event that fired.\n   * @private\n   */\nMaterialMenu.prototype.handleItemKeyboardEvent_ = function (evt) {\n    if (this.element_ && this.container_) {\n        var items = this.element_.querySelectorAll('.' + this.CssClasses_.ITEM + ':not([disabled])');\n        if (items && items.length > 0 && this.container_.classList.contains(this.CssClasses_.IS_VISIBLE)) {\n            var currentIndex = Array.prototype.slice.call(items).indexOf(evt.target);\n            if (evt.keyCode === this.Keycodes_.UP_ARROW) {\n                evt.preventDefault();\n                if (currentIndex > 0) {\n                    items[currentIndex - 1].focus();\n                } else {\n                    items[items.length - 1].focus();\n                }\n            } else if (evt.keyCode === this.Keycodes_.DOWN_ARROW) {\n                evt.preventDefault();\n                if (items.length > currentIndex + 1) {\n                    items[currentIndex + 1].focus();\n                } else {\n                    items[0].focus();\n                }\n            } else if (evt.keyCode === this.Keycodes_.SPACE || evt.keyCode === this.Keycodes_.ENTER) {\n                evt.preventDefault();\n                // Send mousedown and mouseup to trigger ripple.\n                var e = new MouseEvent('mousedown');\n                evt.target.dispatchEvent(e);\n                e = new MouseEvent('mouseup');\n                evt.target.dispatchEvent(e);\n                // Send click.\n                evt.target.click();\n            } else if (evt.keyCode === this.Keycodes_.ESCAPE) {\n                evt.preventDefault();\n                this.hide();\n            }\n        }\n    }\n};\n/**\n   * Handles a click event on an item.\n   *\n   * @param {Event} evt The event that fired.\n   * @private\n   */\nMaterialMenu.prototype.handleItemClick_ = function (evt) {\n    if (evt.target.hasAttribute('disabled')) {\n        evt.stopPropagation();\n    } else {\n        // Wait some time before closing menu, so the user can see the ripple.\n        this.closing_ = true;\n        window.setTimeout(function (evt) {\n            this.hide();\n            this.closing_ = false;\n        }.bind(this), this.Constant_.CLOSE_TIMEOUT);\n    }\n};\n/**\n   * Calculates the initial clip (for opening the menu) or final clip (for closing\n   * it), and applies it. This allows us to animate from or to the correct point,\n   * that is, the point it's aligned to in the \"for\" element.\n   *\n   * @param {number} height Height of the clip rectangle\n   * @param {number} width Width of the clip rectangle\n   * @private\n   */\nMaterialMenu.prototype.applyClip_ = function (height, width) {\n    if (this.element_.classList.contains(this.CssClasses_.UNALIGNED)) {\n        // Do not clip.\n        this.element_.style.clip = '';\n    } else if (this.element_.classList.contains(this.CssClasses_.BOTTOM_RIGHT)) {\n        // Clip to the top right corner of the menu.\n        this.element_.style.clip = 'rect(0 ' + width + 'px ' + '0 ' + width + 'px)';\n    } else if (this.element_.classList.contains(this.CssClasses_.TOP_LEFT)) {\n        // Clip to the bottom left corner of the menu.\n        this.element_.style.clip = 'rect(' + height + 'px 0 ' + height + 'px 0)';\n    } else if (this.element_.classList.contains(this.CssClasses_.TOP_RIGHT)) {\n        // Clip to the bottom right corner of the menu.\n        this.element_.style.clip = 'rect(' + height + 'px ' + width + 'px ' + height + 'px ' + width + 'px)';\n    } else {\n        // Default: do not clip (same as clipping to the top left corner).\n        this.element_.style.clip = '';\n    }\n};\n/**\n   * Cleanup function to remove animation listeners.\n   *\n   * @param {Event} evt\n   * @private\n   */\nMaterialMenu.prototype.removeAnimationEndListener_ = function (evt) {\n    evt.target.classList.remove(MaterialMenu.prototype.CssClasses_.IS_ANIMATING);\n};\n/**\n   * Adds an event listener to clean up after the animation ends.\n   *\n   * @private\n   */\nMaterialMenu.prototype.addAnimationEndListener_ = function () {\n    this.element_.addEventListener('transitionend', this.removeAnimationEndListener_);\n    this.element_.addEventListener('webkitTransitionEnd', this.removeAnimationEndListener_);\n};\n/**\n   * Displays the menu.\n   *\n   * @public\n   */\nMaterialMenu.prototype.show = function (evt) {\n    if (this.element_ && this.container_ && this.outline_) {\n        // Measure the inner element.\n        var height = this.element_.getBoundingClientRect().height;\n        var width = this.element_.getBoundingClientRect().width;\n        // Apply the inner element's size to the container and outline.\n        this.container_.style.width = width + 'px';\n        this.container_.style.height = height + 'px';\n        this.outline_.style.width = width + 'px';\n        this.outline_.style.height = height + 'px';\n        var transitionDuration = this.Constant_.TRANSITION_DURATION_SECONDS * this.Constant_.TRANSITION_DURATION_FRACTION;\n        // Calculate transition delays for individual menu items, so that they fade\n        // in one at a time.\n        var items = this.element_.querySelectorAll('.' + this.CssClasses_.ITEM);\n        for (var i = 0; i < items.length; i++) {\n            var itemDelay = null;\n            if (this.element_.classList.contains(this.CssClasses_.TOP_LEFT) || this.element_.classList.contains(this.CssClasses_.TOP_RIGHT)) {\n                itemDelay = (height - items[i].offsetTop - items[i].offsetHeight) / height * transitionDuration + 's';\n            } else {\n                itemDelay = items[i].offsetTop / height * transitionDuration + 's';\n            }\n            items[i].style.transitionDelay = itemDelay;\n        }\n        // Apply the initial clip to the text before we start animating.\n        this.applyClip_(height, width);\n        // Wait for the next frame, turn on animation, and apply the final clip.\n        // Also make it visible. This triggers the transitions.\n        window.requestAnimationFrame(function () {\n            this.element_.classList.add(this.CssClasses_.IS_ANIMATING);\n            this.element_.style.clip = 'rect(0 ' + width + 'px ' + height + 'px 0)';\n            this.container_.classList.add(this.CssClasses_.IS_VISIBLE);\n        }.bind(this));\n        // Clean up after the animation is complete.\n        this.addAnimationEndListener_();\n        // Add a click listener to the document, to close the menu.\n        var callback = function (e) {\n            // Check to see if the document is processing the same event that\n            // displayed the menu in the first place. If so, do nothing.\n            // Also check to see if the menu is in the process of closing itself, and\n            // do nothing in that case.\n            // Also check if the clicked element is a menu item\n            // if so, do nothing.\n            if (e !== evt && !this.closing_ && e.target.parentNode !== this.element_) {\n                document.removeEventListener('click', callback);\n                this.hide();\n            }\n        }.bind(this);\n        document.addEventListener('click', callback);\n    }\n};\nMaterialMenu.prototype['show'] = MaterialMenu.prototype.show;\n/**\n   * Hides the menu.\n   *\n   * @public\n   */\nMaterialMenu.prototype.hide = function () {\n    if (this.element_ && this.container_ && this.outline_) {\n        var items = this.element_.querySelectorAll('.' + this.CssClasses_.ITEM);\n        // Remove all transition delays; menu items fade out concurrently.\n        for (var i = 0; i < items.length; i++) {\n            items[i].style.removeProperty('transition-delay');\n        }\n        // Measure the inner element.\n        var rect = this.element_.getBoundingClientRect();\n        var height = rect.height;\n        var width = rect.width;\n        // Turn on animation, and apply the final clip. Also make invisible.\n        // This triggers the transitions.\n        this.element_.classList.add(this.CssClasses_.IS_ANIMATING);\n        this.applyClip_(height, width);\n        this.container_.classList.remove(this.CssClasses_.IS_VISIBLE);\n        // Clean up after the animation is complete.\n        this.addAnimationEndListener_();\n    }\n};\nMaterialMenu.prototype['hide'] = MaterialMenu.prototype.hide;\n/**\n   * Displays or hides the menu, depending on current state.\n   *\n   * @public\n   */\nMaterialMenu.prototype.toggle = function (evt) {\n    if (this.container_.classList.contains(this.CssClasses_.IS_VISIBLE)) {\n        this.hide();\n    } else {\n        this.show(evt);\n    }\n};\nMaterialMenu.prototype['toggle'] = MaterialMenu.prototype.toggle;\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialMenu,\n    classAsString: 'MaterialMenu',\n    cssClass: 'mdl-js-menu',\n    widget: true\n});", "/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for Progress MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @constructor\n   * @param {HTMLElement} element The element that will be upgraded.\n   */\nvar MaterialProgress = function MaterialProgress(element) {\n    this.element_ = element;\n    // Initialize instance.\n    this.init();\n};\nwindow['MaterialProgress'] = MaterialProgress;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string | number}\n   * @private\n   */\nMaterialProgress.prototype.Constant_ = {};\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialProgress.prototype.CssClasses_ = { INDETERMINATE_CLASS: 'mdl-progress__indeterminate' };\n/**\n   * Set the current progress of the progressbar.\n   *\n   * @param {number} p Percentage of the progress (0-100)\n   * @public\n   */\nMaterialProgress.prototype.setProgress = function (p) {\n    if (this.element_.classList.contains(this.CssClasses_.INDETERMINATE_CLASS)) {\n        return;\n    }\n    this.progressbar_.style.width = p + '%';\n};\nMaterialProgress.prototype['setProgress'] = MaterialProgress.prototype.setProgress;\n/**\n   * Set the current progress of the buffer.\n   *\n   * @param {number} p Percentage of the buffer (0-100)\n   * @public\n   */\nMaterialProgress.prototype.setBuffer = function (p) {\n    this.bufferbar_.style.width = p + '%';\n    this.auxbar_.style.width = 100 - p + '%';\n};\nMaterialProgress.prototype['setBuffer'] = MaterialProgress.prototype.setBuffer;\n/**\n   * Initialize element.\n   */\nMaterialProgress.prototype.init = function () {\n    if (this.element_) {\n        var el = document.createElement('div');\n        el.className = 'progressbar bar bar1';\n        this.element_.appendChild(el);\n        this.progressbar_ = el;\n        el = document.createElement('div');\n        el.className = 'bufferbar bar bar2';\n        this.element_.appendChild(el);\n        this.bufferbar_ = el;\n        el = document.createElement('div');\n        el.className = 'auxbar bar bar3';\n        this.element_.appendChild(el);\n        this.auxbar_ = el;\n        this.progressbar_.style.width = '0%';\n        this.bufferbar_.style.width = '100%';\n        this.auxbar_.style.width = '0%';\n        this.element_.classList.add('is-upgraded');\n    }\n};\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialProgress,\n    classAsString: 'MaterialProgress',\n    cssClass: 'mdl-js-progress',\n    widget: true\n});", "/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for Radio MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @constructor\n   * @param {HTMLElement} element The element that will be upgraded.\n   */\nvar MaterialRadio = function MaterialRadio(element) {\n    this.element_ = element;\n    // Initialize instance.\n    this.init();\n};\nwindow['MaterialRadio'] = MaterialRadio;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string | number}\n   * @private\n   */\nMaterialRadio.prototype.Constant_ = { TINY_TIMEOUT: 0.001 };\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialRadio.prototype.CssClasses_ = {\n    IS_FOCUSED: 'is-focused',\n    IS_DISABLED: 'is-disabled',\n    IS_CHECKED: 'is-checked',\n    IS_UPGRADED: 'is-upgraded',\n    JS_RADIO: 'mdl-js-radio',\n    RADIO_BTN: 'mdl-radio__button',\n    RADIO_OUTER_CIRCLE: 'mdl-radio__outer-circle',\n    RADIO_INNER_CIRCLE: 'mdl-radio__inner-circle',\n    RIPPLE_EFFECT: 'mdl-js-ripple-effect',\n    RIPPLE_IGNORE_EVENTS: 'mdl-js-ripple-effect--ignore-events',\n    RIPPLE_CONTAINER: 'mdl-radio__ripple-container',\n    RIPPLE_CENTER: 'mdl-ripple--center',\n    RIPPLE: 'mdl-ripple'\n};\n/**\n   * Handle change of state.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialRadio.prototype.onChange_ = function (event) {\n    // Since other radio buttons don't get change events, we need to look for\n    // them to update their classes.\n    var radios = document.getElementsByClassName(this.CssClasses_.JS_RADIO);\n    for (var i = 0; i < radios.length; i++) {\n        var button = radios[i].querySelector('.' + this.CssClasses_.RADIO_BTN);\n        // Different name == different group, so no point updating those.\n        if (button.getAttribute('name') === this.btnElement_.getAttribute('name')) {\n            if (typeof radios[i]['MaterialRadio'] !== 'undefined') {\n                radios[i]['MaterialRadio'].updateClasses_();\n            }\n        }\n    }\n};\n/**\n   * Handle focus.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialRadio.prototype.onFocus_ = function (event) {\n    this.element_.classList.add(this.CssClasses_.IS_FOCUSED);\n};\n/**\n   * Handle lost focus.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialRadio.prototype.onBlur_ = function (event) {\n    this.element_.classList.remove(this.CssClasses_.IS_FOCUSED);\n};\n/**\n   * Handle mouseup.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialRadio.prototype.onMouseup_ = function (event) {\n    this.blur_();\n};\n/**\n   * Update classes.\n   *\n   * @private\n   */\nMaterialRadio.prototype.updateClasses_ = function () {\n    this.checkDisabled();\n    this.checkToggleState();\n};\n/**\n   * Add blur.\n   *\n   * @private\n   */\nMaterialRadio.prototype.blur_ = function () {\n    // TODO: figure out why there's a focus event being fired after our blur,\n    // so that we can avoid this hack.\n    window.setTimeout(function () {\n        this.btnElement_.blur();\n    }.bind(this), this.Constant_.TINY_TIMEOUT);\n};\n// Public methods.\n/**\n   * Check the components disabled state.\n   *\n   * @public\n   */\nMaterialRadio.prototype.checkDisabled = function () {\n    if (this.btnElement_.disabled) {\n        this.element_.classList.add(this.CssClasses_.IS_DISABLED);\n    } else {\n        this.element_.classList.remove(this.CssClasses_.IS_DISABLED);\n    }\n};\nMaterialRadio.prototype['checkDisabled'] = MaterialRadio.prototype.checkDisabled;\n/**\n   * Check the components toggled state.\n   *\n   * @public\n   */\nMaterialRadio.prototype.checkToggleState = function () {\n    if (this.btnElement_.checked) {\n        this.element_.classList.add(this.CssClasses_.IS_CHECKED);\n    } else {\n        this.element_.classList.remove(this.CssClasses_.IS_CHECKED);\n    }\n};\nMaterialRadio.prototype['checkToggleState'] = MaterialRadio.prototype.checkToggleState;\n/**\n   * Disable radio.\n   *\n   * @public\n   */\nMaterialRadio.prototype.disable = function () {\n    this.btnElement_.disabled = true;\n    this.updateClasses_();\n};\nMaterialRadio.prototype['disable'] = MaterialRadio.prototype.disable;\n/**\n   * Enable radio.\n   *\n   * @public\n   */\nMaterialRadio.prototype.enable = function () {\n    this.btnElement_.disabled = false;\n    this.updateClasses_();\n};\nMaterialRadio.prototype['enable'] = MaterialRadio.prototype.enable;\n/**\n   * Check radio.\n   *\n   * @public\n   */\nMaterialRadio.prototype.check = function () {\n    this.btnElement_.checked = true;\n    this.onChange_(null);\n};\nMaterialRadio.prototype['check'] = MaterialRadio.prototype.check;\n/**\n   * Uncheck radio.\n   *\n   * @public\n   */\nMaterialRadio.prototype.uncheck = function () {\n    this.btnElement_.checked = false;\n    this.onChange_(null);\n};\nMaterialRadio.prototype['uncheck'] = MaterialRadio.prototype.uncheck;\n/**\n   * Initialize element.\n   */\nMaterialRadio.prototype.init = function () {\n    if (this.element_) {\n        this.btnElement_ = this.element_.querySelector('.' + this.CssClasses_.RADIO_BTN);\n        this.boundChangeHandler_ = this.onChange_.bind(this);\n        this.boundFocusHandler_ = this.onChange_.bind(this);\n        this.boundBlurHandler_ = this.onBlur_.bind(this);\n        this.boundMouseUpHandler_ = this.onMouseup_.bind(this);\n        var outerCircle = document.createElement('span');\n        outerCircle.classList.add(this.CssClasses_.RADIO_OUTER_CIRCLE);\n        var innerCircle = document.createElement('span');\n        innerCircle.classList.add(this.CssClasses_.RADIO_INNER_CIRCLE);\n        this.element_.appendChild(outerCircle);\n        this.element_.appendChild(innerCircle);\n        var rippleContainer;\n        if (this.element_.classList.contains(this.CssClasses_.RIPPLE_EFFECT)) {\n            this.element_.classList.add(this.CssClasses_.RIPPLE_IGNORE_EVENTS);\n            rippleContainer = document.createElement('span');\n            rippleContainer.classList.add(this.CssClasses_.RIPPLE_CONTAINER);\n            rippleContainer.classList.add(this.CssClasses_.RIPPLE_EFFECT);\n            rippleContainer.classList.add(this.CssClasses_.RIPPLE_CENTER);\n            rippleContainer.addEventListener('mouseup', this.boundMouseUpHandler_);\n            var ripple = document.createElement('span');\n            ripple.classList.add(this.CssClasses_.RIPPLE);\n            rippleContainer.appendChild(ripple);\n            this.element_.appendChild(rippleContainer);\n        }\n        this.btnElement_.addEventListener('change', this.boundChangeHandler_);\n        this.btnElement_.addEventListener('focus', this.boundFocusHandler_);\n        this.btnElement_.addEventListener('blur', this.boundBlurHandler_);\n        this.element_.addEventListener('mouseup', this.boundMouseUpHandler_);\n        this.updateClasses_();\n        this.element_.classList.add(this.CssClasses_.IS_UPGRADED);\n    }\n};\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialRadio,\n    classAsString: 'MaterialRadio',\n    cssClass: 'mdl-js-radio',\n    widget: true\n});", "/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for Slider MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @constructor\n   * @param {HTMLElement} element The element that will be upgraded.\n   */\nvar MaterialSlider = function MaterialSlider(element) {\n    this.element_ = element;\n    // Browser feature detection.\n    this.isIE_ = window.navigator.msPointerEnabled;\n    // Initialize instance.\n    this.init();\n};\nwindow['MaterialSlider'] = MaterialSlider;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string | number}\n   * @private\n   */\nMaterialSlider.prototype.Constant_ = {};\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialSlider.prototype.CssClasses_ = {\n    IE_CONTAINER: 'mdl-slider__ie-container',\n    SLIDER_CONTAINER: 'mdl-slider__container',\n    BACKGROUND_FLEX: 'mdl-slider__background-flex',\n    BACKGROUND_LOWER: 'mdl-slider__background-lower',\n    BACKGROUND_UPPER: 'mdl-slider__background-upper',\n    IS_LOWEST_VALUE: 'is-lowest-value',\n    IS_UPGRADED: 'is-upgraded'\n};\n/**\n   * Handle input on element.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialSlider.prototype.onInput_ = function (event) {\n    this.updateValueStyles_();\n};\n/**\n   * Handle change on element.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialSlider.prototype.onChange_ = function (event) {\n    this.updateValueStyles_();\n};\n/**\n   * Handle mouseup on element.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialSlider.prototype.onMouseUp_ = function (event) {\n    event.target.blur();\n};\n/**\n   * Handle mousedown on container element.\n   * This handler is purpose is to not require the use to click\n   * exactly on the 2px slider element, as FireFox seems to be very\n   * strict about this.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   * @suppress {missingProperties}\n   */\nMaterialSlider.prototype.onContainerMouseDown_ = function (event) {\n    // If this click is not on the parent element (but rather some child)\n    // ignore. It may still bubble up.\n    if (event.target !== this.element_.parentElement) {\n        return;\n    }\n    // Discard the original event and create a new event that\n    // is on the slider element.\n    event.preventDefault();\n    var newEvent = new MouseEvent('mousedown', {\n        target: event.target,\n        buttons: event.buttons,\n        clientX: event.clientX,\n        clientY: this.element_.getBoundingClientRect().y\n    });\n    this.element_.dispatchEvent(newEvent);\n};\n/**\n   * Handle updating of values.\n   *\n   * @private\n   */\nMaterialSlider.prototype.updateValueStyles_ = function () {\n    // Calculate and apply percentages to div structure behind slider.\n    var fraction = (this.element_.value - this.element_.min) / (this.element_.max - this.element_.min);\n    if (fraction === 0) {\n        this.element_.classList.add(this.CssClasses_.IS_LOWEST_VALUE);\n    } else {\n        this.element_.classList.remove(this.CssClasses_.IS_LOWEST_VALUE);\n    }\n    if (!this.isIE_) {\n        this.backgroundLower_.style.flex = fraction;\n        this.backgroundLower_.style.webkitFlex = fraction;\n        this.backgroundUpper_.style.flex = 1 - fraction;\n        this.backgroundUpper_.style.webkitFlex = 1 - fraction;\n    }\n};\n// Public methods.\n/**\n   * Disable slider.\n   *\n   * @public\n   */\nMaterialSlider.prototype.disable = function () {\n    this.element_.disabled = true;\n};\nMaterialSlider.prototype['disable'] = MaterialSlider.prototype.disable;\n/**\n   * Enable slider.\n   *\n   * @public\n   */\nMaterialSlider.prototype.enable = function () {\n    this.element_.disabled = false;\n};\nMaterialSlider.prototype['enable'] = MaterialSlider.prototype.enable;\n/**\n   * Update slider value.\n   *\n   * @param {number} value The value to which to set the control (optional).\n   * @public\n   */\nMaterialSlider.prototype.change = function (value) {\n    if (typeof value !== 'undefined') {\n        this.element_.value = value;\n    }\n    this.updateValueStyles_();\n};\nMaterialSlider.prototype['change'] = MaterialSlider.prototype.change;\n/**\n   * Initialize element.\n   */\nMaterialSlider.prototype.init = function () {\n    if (this.element_) {\n        if (this.isIE_) {\n            // Since we need to specify a very large height in IE due to\n            // implementation limitations, we add a parent here that trims it down to\n            // a reasonable size.\n            var containerIE = document.createElement('div');\n            containerIE.classList.add(this.CssClasses_.IE_CONTAINER);\n            this.element_.parentElement.insertBefore(containerIE, this.element_);\n            this.element_.parentElement.removeChild(this.element_);\n            containerIE.appendChild(this.element_);\n        } else {\n            // For non-IE browsers, we need a div structure that sits behind the\n            // slider and allows us to style the left and right sides of it with\n            // different colors.\n            var container = document.createElement('div');\n            container.classList.add(this.CssClasses_.SLIDER_CONTAINER);\n            this.element_.parentElement.insertBefore(container, this.element_);\n            this.element_.parentElement.removeChild(this.element_);\n            container.appendChild(this.element_);\n            var backgroundFlex = document.createElement('div');\n            backgroundFlex.classList.add(this.CssClasses_.BACKGROUND_FLEX);\n            container.appendChild(backgroundFlex);\n            this.backgroundLower_ = document.createElement('div');\n            this.backgroundLower_.classList.add(this.CssClasses_.BACKGROUND_LOWER);\n            backgroundFlex.appendChild(this.backgroundLower_);\n            this.backgroundUpper_ = document.createElement('div');\n            this.backgroundUpper_.classList.add(this.CssClasses_.BACKGROUND_UPPER);\n            backgroundFlex.appendChild(this.backgroundUpper_);\n        }\n        this.boundInputHandler = this.onInput_.bind(this);\n        this.boundChangeHandler = this.onChange_.bind(this);\n        this.boundMouseUpHandler = this.onMouseUp_.bind(this);\n        this.boundContainerMouseDownHandler = this.onContainerMouseDown_.bind(this);\n        this.element_.addEventListener('input', this.boundInputHandler);\n        this.element_.addEventListener('change', this.boundChangeHandler);\n        this.element_.addEventListener('mouseup', this.boundMouseUpHandler);\n        this.element_.parentElement.addEventListener('mousedown', this.boundContainerMouseDownHandler);\n        this.updateValueStyles_();\n        this.element_.classList.add(this.CssClasses_.IS_UPGRADED);\n    }\n};\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialSlider,\n    classAsString: 'MaterialSlider',\n    cssClass: 'mdl-js-slider',\n    widget: true\n});", "/**\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for Snackbar MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @constructor\n   * @param {HTMLElement} element The element that will be upgraded.\n   */\nvar MaterialSnackbar = function MaterialSnackbar(element) {\n    this.element_ = element;\n    this.textElement_ = this.element_.querySelector('.' + this.cssClasses_.MESSAGE);\n    this.actionElement_ = this.element_.querySelector('.' + this.cssClasses_.ACTION);\n    if (!this.textElement_) {\n        throw new Error('There must be a message element for a snackbar.');\n    }\n    if (!this.actionElement_) {\n        throw new Error('There must be an action element for a snackbar.');\n    }\n    this.active = false;\n    this.actionHandler_ = undefined;\n    this.message_ = undefined;\n    this.actionText_ = undefined;\n    this.queuedNotifications_ = [];\n    this.setActionHidden_(true);\n};\nwindow['MaterialSnackbar'] = MaterialSnackbar;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string | number}\n   * @private\n   */\nMaterialSnackbar.prototype.Constant_ = {\n    // The duration of the snackbar show/hide animation, in ms.\n    ANIMATION_LENGTH: 250\n};\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialSnackbar.prototype.cssClasses_ = {\n    SNACKBAR: 'mdl-snackbar',\n    MESSAGE: 'mdl-snackbar__text',\n    ACTION: 'mdl-snackbar__action',\n    ACTIVE: 'mdl-snackbar--active'\n};\n/**\n   * Display the snackbar.\n   *\n   * @private\n   */\nMaterialSnackbar.prototype.displaySnackbar_ = function () {\n    this.element_.setAttribute('aria-hidden', 'true');\n    if (this.actionHandler_) {\n        this.actionElement_.textContent = this.actionText_;\n        this.actionElement_.addEventListener('click', this.actionHandler_);\n        this.setActionHidden_(false);\n    }\n    this.textElement_.textContent = this.message_;\n    this.element_.classList.add(this.cssClasses_.ACTIVE);\n    this.element_.setAttribute('aria-hidden', 'false');\n    setTimeout(this.cleanup_.bind(this), this.timeout_);\n};\n/**\n   * Show the snackbar.\n   *\n   * @param {Object} data The data for the notification.\n   * @public\n   */\nMaterialSnackbar.prototype.showSnackbar = function (data) {\n    if (data === undefined) {\n        throw new Error('Please provide a data object with at least a message to display.');\n    }\n    if (data['message'] === undefined) {\n        throw new Error('Please provide a message to be displayed.');\n    }\n    if (data['actionHandler'] && !data['actionText']) {\n        throw new Error('Please provide action text with the handler.');\n    }\n    if (this.active) {\n        this.queuedNotifications_.push(data);\n    } else {\n        this.active = true;\n        this.message_ = data['message'];\n        if (data['timeout']) {\n            this.timeout_ = data['timeout'];\n        } else {\n            this.timeout_ = 2750;\n        }\n        if (data['actionHandler']) {\n            this.actionHandler_ = data['actionHandler'];\n        }\n        if (data['actionText']) {\n            this.actionText_ = data['actionText'];\n        }\n        this.displaySnackbar_();\n    }\n};\nMaterialSnackbar.prototype['showSnackbar'] = MaterialSnackbar.prototype.showSnackbar;\n/**\n   * Check if the queue has items within it.\n   * If it does, display the next entry.\n   *\n   * @private\n   */\nMaterialSnackbar.prototype.checkQueue_ = function () {\n    if (this.queuedNotifications_.length > 0) {\n        this.showSnackbar(this.queuedNotifications_.shift());\n    }\n};\n/**\n   * Cleanup the snackbar event listeners and accessiblity attributes.\n   *\n   * @private\n   */\nMaterialSnackbar.prototype.cleanup_ = function () {\n    this.element_.classList.remove(this.cssClasses_.ACTIVE);\n    setTimeout(function () {\n        this.element_.setAttribute('aria-hidden', 'true');\n        this.textElement_.textContent = '';\n        if (!Boolean(this.actionElement_.getAttribute('aria-hidden'))) {\n            this.setActionHidden_(true);\n            this.actionElement_.textContent = '';\n            this.actionElement_.removeEventListener('click', this.actionHandler_);\n        }\n        this.actionHandler_ = undefined;\n        this.message_ = undefined;\n        this.actionText_ = undefined;\n        this.active = false;\n        this.checkQueue_();\n    }.bind(this), this.Constant_.ANIMATION_LENGTH);\n};\n/**\n   * Set the action handler hidden state.\n   *\n   * @param {boolean} value\n   * @private\n   */\nMaterialSnackbar.prototype.setActionHidden_ = function (value) {\n    if (value) {\n        this.actionElement_.setAttribute('aria-hidden', 'true');\n    } else {\n        this.actionElement_.removeAttribute('aria-hidden');\n    }\n};\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialSnackbar,\n    classAsString: 'MaterialSnackbar',\n    cssClass: 'mdl-js-snackbar',\n    widget: true\n});", "/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for Spinner MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @param {HTMLElement} element The element that will be upgraded.\n   * @constructor\n   */\nvar MaterialSpinner = function MaterialSpinner(element) {\n    this.element_ = element;\n    // Initialize instance.\n    this.init();\n};\nwindow['MaterialSpinner'] = MaterialSpinner;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string | number}\n   * @private\n   */\nMaterialSpinner.prototype.Constant_ = { MDL_SPINNER_LAYER_COUNT: 4 };\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialSpinner.prototype.CssClasses_ = {\n    MDL_SPINNER_LAYER: 'mdl-spinner__layer',\n    MDL_SPINNER_CIRCLE_CLIPPER: 'mdl-spinner__circle-clipper',\n    MDL_SPINNER_CIRCLE: 'mdl-spinner__circle',\n    MDL_SPINNER_GAP_PATCH: 'mdl-spinner__gap-patch',\n    MDL_SPINNER_LEFT: 'mdl-spinner__left',\n    MDL_SPINNER_RIGHT: 'mdl-spinner__right'\n};\n/**\n   * Auxiliary method to create a spinner layer.\n   *\n   * @param {number} index Index of the layer to be created.\n   * @public\n   */\nMaterialSpinner.prototype.createLayer = function (index) {\n    var layer = document.createElement('div');\n    layer.classList.add(this.CssClasses_.MDL_SPINNER_LAYER);\n    layer.classList.add(this.CssClasses_.MDL_SPINNER_LAYER + '-' + index);\n    var leftClipper = document.createElement('div');\n    leftClipper.classList.add(this.CssClasses_.MDL_SPINNER_CIRCLE_CLIPPER);\n    leftClipper.classList.add(this.CssClasses_.MDL_SPINNER_LEFT);\n    var gapPatch = document.createElement('div');\n    gapPatch.classList.add(this.CssClasses_.MDL_SPINNER_GAP_PATCH);\n    var rightClipper = document.createElement('div');\n    rightClipper.classList.add(this.CssClasses_.MDL_SPINNER_CIRCLE_CLIPPER);\n    rightClipper.classList.add(this.CssClasses_.MDL_SPINNER_RIGHT);\n    var circleOwners = [\n        leftClipper,\n        gapPatch,\n        rightClipper\n    ];\n    for (var i = 0; i < circleOwners.length; i++) {\n        var circle = document.createElement('div');\n        circle.classList.add(this.CssClasses_.MDL_SPINNER_CIRCLE);\n        circleOwners[i].appendChild(circle);\n    }\n    layer.appendChild(leftClipper);\n    layer.appendChild(gapPatch);\n    layer.appendChild(rightClipper);\n    this.element_.appendChild(layer);\n};\nMaterialSpinner.prototype['createLayer'] = MaterialSpinner.prototype.createLayer;\n/**\n   * Stops the spinner animation.\n   * Public method for users who need to stop the spinner for any reason.\n   *\n   * @public\n   */\nMaterialSpinner.prototype.stop = function () {\n    this.element_.classList.remove('is-active');\n};\nMaterialSpinner.prototype['stop'] = MaterialSpinner.prototype.stop;\n/**\n   * Starts the spinner animation.\n   * Public method for users who need to manually start the spinner for any reason\n   * (instead of just adding the 'is-active' class to their markup).\n   *\n   * @public\n   */\nMaterialSpinner.prototype.start = function () {\n    this.element_.classList.add('is-active');\n};\nMaterialSpinner.prototype['start'] = MaterialSpinner.prototype.start;\n/**\n   * Initialize element.\n   */\nMaterialSpinner.prototype.init = function () {\n    if (this.element_) {\n        for (var i = 1; i <= this.Constant_.MDL_SPINNER_LAYER_COUNT; i++) {\n            this.createLayer(i);\n        }\n        this.element_.classList.add('is-upgraded');\n    }\n};\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialSpinner,\n    classAsString: 'MaterialSpinner',\n    cssClass: 'mdl-js-spinner',\n    widget: true\n});", "/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for Checkbox MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @constructor\n   * @param {HTMLElement} element The element that will be upgraded.\n   */\nvar MaterialSwitch = function MaterialSwitch(element) {\n    this.element_ = element;\n    // Initialize instance.\n    this.init();\n};\nwindow['MaterialSwitch'] = MaterialSwitch;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string | number}\n   * @private\n   */\nMaterialSwitch.prototype.Constant_ = { TINY_TIMEOUT: 0.001 };\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialSwitch.prototype.CssClasses_ = {\n    INPUT: 'mdl-switch__input',\n    TRACK: 'mdl-switch__track',\n    THUMB: 'mdl-switch__thumb',\n    FOCUS_HELPER: 'mdl-switch__focus-helper',\n    RIPPLE_EFFECT: 'mdl-js-ripple-effect',\n    RIPPLE_IGNORE_EVENTS: 'mdl-js-ripple-effect--ignore-events',\n    RIPPLE_CONTAINER: 'mdl-switch__ripple-container',\n    RIPPLE_CENTER: 'mdl-ripple--center',\n    RIPPLE: 'mdl-ripple',\n    IS_FOCUSED: 'is-focused',\n    IS_DISABLED: 'is-disabled',\n    IS_CHECKED: 'is-checked'\n};\n/**\n   * Handle change of state.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialSwitch.prototype.onChange_ = function (event) {\n    this.updateClasses_();\n};\n/**\n   * Handle focus of element.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialSwitch.prototype.onFocus_ = function (event) {\n    this.element_.classList.add(this.CssClasses_.IS_FOCUSED);\n};\n/**\n   * Handle lost focus of element.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialSwitch.prototype.onBlur_ = function (event) {\n    this.element_.classList.remove(this.CssClasses_.IS_FOCUSED);\n};\n/**\n   * Handle mouseup.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialSwitch.prototype.onMouseUp_ = function (event) {\n    this.blur_();\n};\n/**\n   * Handle class updates.\n   *\n   * @private\n   */\nMaterialSwitch.prototype.updateClasses_ = function () {\n    this.checkDisabled();\n    this.checkToggleState();\n};\n/**\n   * Add blur.\n   *\n   * @private\n   */\nMaterialSwitch.prototype.blur_ = function () {\n    // TODO: figure out why there's a focus event being fired after our blur,\n    // so that we can avoid this hack.\n    window.setTimeout(function () {\n        this.inputElement_.blur();\n    }.bind(this), this.Constant_.TINY_TIMEOUT);\n};\n// Public methods.\n/**\n   * Check the components disabled state.\n   *\n   * @public\n   */\nMaterialSwitch.prototype.checkDisabled = function () {\n    if (this.inputElement_.disabled) {\n        this.element_.classList.add(this.CssClasses_.IS_DISABLED);\n    } else {\n        this.element_.classList.remove(this.CssClasses_.IS_DISABLED);\n    }\n};\nMaterialSwitch.prototype['checkDisabled'] = MaterialSwitch.prototype.checkDisabled;\n/**\n   * Check the components toggled state.\n   *\n   * @public\n   */\nMaterialSwitch.prototype.checkToggleState = function () {\n    if (this.inputElement_.checked) {\n        this.element_.classList.add(this.CssClasses_.IS_CHECKED);\n    } else {\n        this.element_.classList.remove(this.CssClasses_.IS_CHECKED);\n    }\n};\nMaterialSwitch.prototype['checkToggleState'] = MaterialSwitch.prototype.checkToggleState;\n/**\n   * Disable switch.\n   *\n   * @public\n   */\nMaterialSwitch.prototype.disable = function () {\n    this.inputElement_.disabled = true;\n    this.updateClasses_();\n};\nMaterialSwitch.prototype['disable'] = MaterialSwitch.prototype.disable;\n/**\n   * Enable switch.\n   *\n   * @public\n   */\nMaterialSwitch.prototype.enable = function () {\n    this.inputElement_.disabled = false;\n    this.updateClasses_();\n};\nMaterialSwitch.prototype['enable'] = MaterialSwitch.prototype.enable;\n/**\n   * Activate switch.\n   *\n   * @public\n   */\nMaterialSwitch.prototype.on = function () {\n    this.inputElement_.checked = true;\n    this.updateClasses_();\n};\nMaterialSwitch.prototype['on'] = MaterialSwitch.prototype.on;\n/**\n   * Deactivate switch.\n   *\n   * @public\n   */\nMaterialSwitch.prototype.off = function () {\n    this.inputElement_.checked = false;\n    this.updateClasses_();\n};\nMaterialSwitch.prototype['off'] = MaterialSwitch.prototype.off;\n/**\n   * Initialize element.\n   */\nMaterialSwitch.prototype.init = function () {\n    if (this.element_) {\n        this.inputElement_ = this.element_.querySelector('.' + this.CssClasses_.INPUT);\n        var track = document.createElement('div');\n        track.classList.add(this.CssClasses_.TRACK);\n        var thumb = document.createElement('div');\n        thumb.classList.add(this.CssClasses_.THUMB);\n        var focusHelper = document.createElement('span');\n        focusHelper.classList.add(this.CssClasses_.FOCUS_HELPER);\n        thumb.appendChild(focusHelper);\n        this.element_.appendChild(track);\n        this.element_.appendChild(thumb);\n        this.boundMouseUpHandler = this.onMouseUp_.bind(this);\n        if (this.element_.classList.contains(this.CssClasses_.RIPPLE_EFFECT)) {\n            this.element_.classList.add(this.CssClasses_.RIPPLE_IGNORE_EVENTS);\n            this.rippleContainerElement_ = document.createElement('span');\n            this.rippleContainerElement_.classList.add(this.CssClasses_.RIPPLE_CONTAINER);\n            this.rippleContainerElement_.classList.add(this.CssClasses_.RIPPLE_EFFECT);\n            this.rippleContainerElement_.classList.add(this.CssClasses_.RIPPLE_CENTER);\n            this.rippleContainerElement_.addEventListener('mouseup', this.boundMouseUpHandler);\n            var ripple = document.createElement('span');\n            ripple.classList.add(this.CssClasses_.RIPPLE);\n            this.rippleContainerElement_.appendChild(ripple);\n            this.element_.appendChild(this.rippleContainerElement_);\n        }\n        this.boundChangeHandler = this.onChange_.bind(this);\n        this.boundFocusHandler = this.onFocus_.bind(this);\n        this.boundBlurHandler = this.onBlur_.bind(this);\n        this.inputElement_.addEventListener('change', this.boundChangeHandler);\n        this.inputElement_.addEventListener('focus', this.boundFocusHandler);\n        this.inputElement_.addEventListener('blur', this.boundBlurHandler);\n        this.element_.addEventListener('mouseup', this.boundMouseUpHandler);\n        this.updateClasses_();\n        this.element_.classList.add('is-upgraded');\n    }\n};\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialSwitch,\n    classAsString: 'MaterialSwitch',\n    cssClass: 'mdl-js-switch',\n    widget: true\n});", "/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for Textfield MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @constructor\n   * @param {HTMLElement} element The element that will be upgraded.\n   */\nvar MaterialTextfield = function MaterialTextfield(element) {\n    this.element_ = element;\n    this.maxRows = this.Constant_.NO_MAX_ROWS;\n    // Initialize instance.\n    this.init();\n};\nwindow['MaterialTextfield'] = MaterialTextfield;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string | number}\n   * @private\n   */\nMaterialTextfield.prototype.Constant_ = {\n    NO_MAX_ROWS: -1,\n    MAX_ROWS_ATTRIBUTE: 'maxrows'\n};\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialTextfield.prototype.CssClasses_ = {\n    LABEL: 'mdl-textfield__label',\n    INPUT: 'mdl-textfield__input',\n    IS_DIRTY: 'is-dirty',\n    IS_FOCUSED: 'is-focused',\n    IS_DISABLED: 'is-disabled',\n    IS_INVALID: 'is-invalid',\n    IS_UPGRADED: 'is-upgraded',\n    HAS_PLACEHOLDER: 'has-placeholder'\n};\n/**\n   * Handle input being entered.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialTextfield.prototype.onKeyDown_ = function (event) {\n    var currentRowCount = event.target.value.split('\\n').length;\n    if (event.keyCode === 13) {\n        if (currentRowCount >= this.maxRows) {\n            event.preventDefault();\n        }\n    }\n};\n/**\n   * Handle focus.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialTextfield.prototype.onFocus_ = function (event) {\n    this.element_.classList.add(this.CssClasses_.IS_FOCUSED);\n};\n/**\n   * Handle lost focus.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialTextfield.prototype.onBlur_ = function (event) {\n    this.element_.classList.remove(this.CssClasses_.IS_FOCUSED);\n};\n/**\n   * Handle reset event from out side.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialTextfield.prototype.onReset_ = function (event) {\n    this.updateClasses_();\n};\n/**\n   * Handle class updates.\n   *\n   * @private\n   */\nMaterialTextfield.prototype.updateClasses_ = function () {\n    this.checkDisabled();\n    this.checkValidity();\n    this.checkDirty();\n    this.checkFocus();\n};\n// Public methods.\n/**\n   * Check the disabled state and update field accordingly.\n   *\n   * @public\n   */\nMaterialTextfield.prototype.checkDisabled = function () {\n    if (this.input_.disabled) {\n        this.element_.classList.add(this.CssClasses_.IS_DISABLED);\n    } else {\n        this.element_.classList.remove(this.CssClasses_.IS_DISABLED);\n    }\n};\nMaterialTextfield.prototype['checkDisabled'] = MaterialTextfield.prototype.checkDisabled;\n/**\n  * Check the focus state and update field accordingly.\n  *\n  * @public\n  */\nMaterialTextfield.prototype.checkFocus = function () {\n    if (Boolean(this.element_.querySelector(':focus'))) {\n        this.element_.classList.add(this.CssClasses_.IS_FOCUSED);\n    } else {\n        this.element_.classList.remove(this.CssClasses_.IS_FOCUSED);\n    }\n};\nMaterialTextfield.prototype['checkFocus'] = MaterialTextfield.prototype.checkFocus;\n/**\n   * Check the validity state and update field accordingly.\n   *\n   * @public\n   */\nMaterialTextfield.prototype.checkValidity = function () {\n    if (this.input_.validity) {\n        if (this.input_.validity.valid) {\n            this.element_.classList.remove(this.CssClasses_.IS_INVALID);\n        } else {\n            this.element_.classList.add(this.CssClasses_.IS_INVALID);\n        }\n    }\n};\nMaterialTextfield.prototype['checkValidity'] = MaterialTextfield.prototype.checkValidity;\n/**\n   * Check the dirty state and update field accordingly.\n   *\n   * @public\n   */\nMaterialTextfield.prototype.checkDirty = function () {\n    if (this.input_.value && this.input_.value.length > 0) {\n        this.element_.classList.add(this.CssClasses_.IS_DIRTY);\n    } else {\n        this.element_.classList.remove(this.CssClasses_.IS_DIRTY);\n    }\n};\nMaterialTextfield.prototype['checkDirty'] = MaterialTextfield.prototype.checkDirty;\n/**\n   * Disable text field.\n   *\n   * @public\n   */\nMaterialTextfield.prototype.disable = function () {\n    this.input_.disabled = true;\n    this.updateClasses_();\n};\nMaterialTextfield.prototype['disable'] = MaterialTextfield.prototype.disable;\n/**\n   * Enable text field.\n   *\n   * @public\n   */\nMaterialTextfield.prototype.enable = function () {\n    this.input_.disabled = false;\n    this.updateClasses_();\n};\nMaterialTextfield.prototype['enable'] = MaterialTextfield.prototype.enable;\n/**\n   * Update text field value.\n   *\n   * @param {string} value The value to which to set the control (optional).\n   * @public\n   */\nMaterialTextfield.prototype.change = function (value) {\n    this.input_.value = value || '';\n    this.updateClasses_();\n};\nMaterialTextfield.prototype['change'] = MaterialTextfield.prototype.change;\n/**\n   * Initialize element.\n   */\nMaterialTextfield.prototype.init = function () {\n    if (this.element_) {\n        this.label_ = this.element_.querySelector('.' + this.CssClasses_.LABEL);\n        this.input_ = this.element_.querySelector('.' + this.CssClasses_.INPUT);\n        if (this.input_) {\n            if (this.input_.hasAttribute(this.Constant_.MAX_ROWS_ATTRIBUTE)) {\n                this.maxRows = parseInt(this.input_.getAttribute(this.Constant_.MAX_ROWS_ATTRIBUTE), 10);\n                if (isNaN(this.maxRows)) {\n                    this.maxRows = this.Constant_.NO_MAX_ROWS;\n                }\n            }\n            if (this.input_.hasAttribute('placeholder')) {\n                this.element_.classList.add(this.CssClasses_.HAS_PLACEHOLDER);\n            }\n            this.boundUpdateClassesHandler = this.updateClasses_.bind(this);\n            this.boundFocusHandler = this.onFocus_.bind(this);\n            this.boundBlurHandler = this.onBlur_.bind(this);\n            this.boundResetHandler = this.onReset_.bind(this);\n            this.input_.addEventListener('input', this.boundUpdateClassesHandler);\n            this.input_.addEventListener('focus', this.boundFocusHandler);\n            this.input_.addEventListener('blur', this.boundBlurHandler);\n            this.input_.addEventListener('reset', this.boundResetHandler);\n            if (this.maxRows !== this.Constant_.NO_MAX_ROWS) {\n                // TODO: This should handle pasting multi line text.\n                // Currently doesn't.\n                this.boundKeyDownHandler = this.onKeyDown_.bind(this);\n                this.input_.addEventListener('keydown', this.boundKeyDownHandler);\n            }\n            var invalid = this.element_.classList.contains(this.CssClasses_.IS_INVALID);\n            this.updateClasses_();\n            this.element_.classList.add(this.CssClasses_.IS_UPGRADED);\n            if (invalid) {\n                this.element_.classList.add(this.CssClasses_.IS_INVALID);\n            }\n            if (this.input_.hasAttribute('autofocus')) {\n                this.element_.focus();\n                this.checkFocus();\n            }\n        }\n    }\n};\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialTextfield,\n    classAsString: 'MaterialTextfield',\n    cssClass: 'mdl-js-textfield',\n    widget: true\n});", "/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for Tooltip MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @constructor\n   * @param {HTMLElement} element The element that will be upgraded.\n   */\nvar MaterialTooltip = function MaterialTooltip(element) {\n    this.element_ = element;\n    // Initialize instance.\n    this.init();\n};\nwindow['MaterialTooltip'] = MaterialTooltip;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string | number}\n   * @private\n   */\nMaterialTooltip.prototype.Constant_ = {};\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialTooltip.prototype.CssClasses_ = {\n    IS_ACTIVE: 'is-active',\n    BOTTOM: 'mdl-tooltip--bottom',\n    LEFT: 'mdl-tooltip--left',\n    RIGHT: 'mdl-tooltip--right',\n    TOP: 'mdl-tooltip--top'\n};\n/**\n   * Handle mouseenter for tooltip.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialTooltip.prototype.handleMouseEnter_ = function (event) {\n    var props = event.target.getBoundingClientRect();\n    var left = props.left + props.width / 2;\n    var top = props.top + props.height / 2;\n    var marginLeft = -1 * (this.element_.offsetWidth / 2);\n    var marginTop = -1 * (this.element_.offsetHeight / 2);\n    if (this.element_.classList.contains(this.CssClasses_.LEFT) || this.element_.classList.contains(this.CssClasses_.RIGHT)) {\n        left = props.width / 2;\n        if (top + marginTop < 0) {\n            this.element_.style.top = '0';\n            this.element_.style.marginTop = '0';\n        } else {\n            this.element_.style.top = top + 'px';\n            this.element_.style.marginTop = marginTop + 'px';\n        }\n    } else {\n        if (left + marginLeft < 0) {\n            this.element_.style.left = '0';\n            this.element_.style.marginLeft = '0';\n        } else {\n            this.element_.style.left = left + 'px';\n            this.element_.style.marginLeft = marginLeft + 'px';\n        }\n    }\n    if (this.element_.classList.contains(this.CssClasses_.TOP)) {\n        this.element_.style.top = props.top - this.element_.offsetHeight - 10 + 'px';\n    } else if (this.element_.classList.contains(this.CssClasses_.RIGHT)) {\n        this.element_.style.left = props.left + props.width + 10 + 'px';\n    } else if (this.element_.classList.contains(this.CssClasses_.LEFT)) {\n        this.element_.style.left = props.left - this.element_.offsetWidth - 10 + 'px';\n    } else {\n        this.element_.style.top = props.top + props.height + 10 + 'px';\n    }\n    this.element_.classList.add(this.CssClasses_.IS_ACTIVE);\n};\n/**\n   * Hide tooltip on mouseleave or scroll\n   *\n   * @private\n   */\nMaterialTooltip.prototype.hideTooltip_ = function () {\n    this.element_.classList.remove(this.CssClasses_.IS_ACTIVE);\n};\n/**\n   * Initialize element.\n   */\nMaterialTooltip.prototype.init = function () {\n    if (this.element_) {\n        var forElId = this.element_.getAttribute('for') || this.element_.getAttribute('data-mdl-for');\n        if (forElId) {\n            this.forElement_ = document.getElementById(forElId);\n        }\n        if (this.forElement_) {\n            // It's left here because it prevents accidental text selection on Android\n            if (!this.forElement_.hasAttribute('tabindex')) {\n                this.forElement_.setAttribute('tabindex', '0');\n            }\n            this.boundMouseEnterHandler = this.handleMouseEnter_.bind(this);\n            this.boundMouseLeaveAndScrollHandler = this.hideTooltip_.bind(this);\n            this.forElement_.addEventListener('mouseenter', this.boundMouseEnterHandler, false);\n            this.forElement_.addEventListener('touchend', this.boundMouseEnterHandler, false);\n            this.forElement_.addEventListener('mouseleave', this.boundMouseLeaveAndScrollHandler, false);\n            window.addEventListener('scroll', this.boundMouseLeaveAndScrollHandler, true);\n            window.addEventListener('touchstart', this.boundMouseLeaveAndScrollHandler);\n        }\n    }\n};\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialTooltip,\n    classAsString: 'MaterialTooltip',\n    cssClass: 'mdl-tooltip'\n});", "/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for Data Table Card MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @constructor\n   * @param {Element} element The element that will be upgraded.\n   */\nvar MaterialDataTable = function MaterialDataTable(element) {\n    this.element_ = element;\n    // Initialize instance.\n    this.init();\n};\nwindow['MaterialDataTable'] = MaterialDataTable;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string | number}\n   * @private\n   */\nMaterialDataTable.prototype.Constant_ = {};\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialDataTable.prototype.CssClasses_ = {\n    DATA_TABLE: 'mdl-data-table',\n    SELECTABLE: 'mdl-data-table--selectable',\n    SELECT_ELEMENT: 'mdl-data-table__select',\n    IS_SELECTED: 'is-selected',\n    IS_UPGRADED: 'is-upgraded'\n};\n/**\n   * Generates and returns a function that toggles the selection state of a\n   * single row (or multiple rows).\n   *\n   * @param {Element} checkbox Checkbox that toggles the selection state.\n   * @param {Element} row Row to toggle when checkbox changes.\n   * @param {(Array<Object>|NodeList)=} opt_rows Rows to toggle when checkbox changes.\n   * @private\n   */\nMaterialDataTable.prototype.selectRow_ = function (checkbox, row, opt_rows) {\n    if (row) {\n        return function () {\n            if (checkbox.checked) {\n                row.classList.add(this.CssClasses_.IS_SELECTED);\n            } else {\n                row.classList.remove(this.CssClasses_.IS_SELECTED);\n            }\n        }.bind(this);\n    }\n    if (opt_rows) {\n        return function () {\n            var i;\n            var el;\n            if (checkbox.checked) {\n                for (i = 0; i < opt_rows.length; i++) {\n                    el = opt_rows[i].querySelector('td').querySelector('.mdl-checkbox');\n                    el['MaterialCheckbox'].check();\n                    opt_rows[i].classList.add(this.CssClasses_.IS_SELECTED);\n                }\n            } else {\n                for (i = 0; i < opt_rows.length; i++) {\n                    el = opt_rows[i].querySelector('td').querySelector('.mdl-checkbox');\n                    el['MaterialCheckbox'].uncheck();\n                    opt_rows[i].classList.remove(this.CssClasses_.IS_SELECTED);\n                }\n            }\n        }.bind(this);\n    }\n};\n/**\n   * Creates a checkbox for a single or or multiple rows and hooks up the\n   * event handling.\n   *\n   * @param {Element} row Row to toggle when checkbox changes.\n   * @param {(Array<Object>|NodeList)=} opt_rows Rows to toggle when checkbox changes.\n   * @private\n   */\nMaterialDataTable.prototype.createCheckbox_ = function (row, opt_rows) {\n    var label = document.createElement('label');\n    var labelClasses = [\n        'mdl-checkbox',\n        'mdl-js-checkbox',\n        'mdl-js-ripple-effect',\n        this.CssClasses_.SELECT_ELEMENT\n    ];\n    label.className = labelClasses.join(' ');\n    var checkbox = document.createElement('input');\n    checkbox.type = 'checkbox';\n    checkbox.classList.add('mdl-checkbox__input');\n    if (row) {\n        checkbox.checked = row.classList.contains(this.CssClasses_.IS_SELECTED);\n        checkbox.addEventListener('change', this.selectRow_(checkbox, row));\n    } else if (opt_rows) {\n        checkbox.addEventListener('change', this.selectRow_(checkbox, null, opt_rows));\n    }\n    label.appendChild(checkbox);\n    componentHandler.upgradeElement(label, 'MaterialCheckbox');\n    return label;\n};\n/**\n   * Initialize element.\n   */\nMaterialDataTable.prototype.init = function () {\n    if (this.element_) {\n        var firstHeader = this.element_.querySelector('th');\n        var bodyRows = Array.prototype.slice.call(this.element_.querySelectorAll('tbody tr'));\n        var footRows = Array.prototype.slice.call(this.element_.querySelectorAll('tfoot tr'));\n        var rows = bodyRows.concat(footRows);\n        if (this.element_.classList.contains(this.CssClasses_.SELECTABLE)) {\n            var th = document.createElement('th');\n            var headerCheckbox = this.createCheckbox_(null, rows);\n            th.appendChild(headerCheckbox);\n            firstHeader.parentElement.insertBefore(th, firstHeader);\n            for (var i = 0; i < rows.length; i++) {\n                var firstCell = rows[i].querySelector('td');\n                if (firstCell) {\n                    var td = document.createElement('td');\n                    if (rows[i].parentNode.nodeName.toUpperCase() === 'TBODY') {\n                        var rowCheckbox = this.createCheckbox_(rows[i]);\n                        td.appendChild(rowCheckbox);\n                    }\n                    rows[i].insertBefore(td, firstCell);\n                }\n            }\n            this.element_.classList.add(this.CssClasses_.IS_UPGRADED);\n        }\n    }\n};\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialDataTable,\n    classAsString: 'MaterialDataTable',\n    cssClass: 'mdl-js-data-table'\n});", "/**\n * @license\n * Copyright 2015 Google Inc. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n   * Class constructor for Ripple MDL component.\n   * Implements MDL component design pattern defined at:\n   * https://github.com/jasonmayes/mdl-component-design-pattern\n   *\n   * @constructor\n   * @param {HTMLElement} element The element that will be upgraded.\n   */\nvar MaterialRipple = function MaterialRipple(element) {\n    this.element_ = element;\n    // Initialize instance.\n    this.init();\n};\nwindow['MaterialRipple'] = MaterialRipple;\n/**\n   * Store constants in one place so they can be updated easily.\n   *\n   * @enum {string | number}\n   * @private\n   */\nMaterialRipple.prototype.Constant_ = {\n    INITIAL_SCALE: 'scale(0.0001, 0.0001)',\n    INITIAL_SIZE: '1px',\n    INITIAL_OPACITY: '0.4',\n    FINAL_OPACITY: '0',\n    FINAL_SCALE: ''\n};\n/**\n   * Store strings for class names defined by this component that are used in\n   * JavaScript. This allows us to simply change it in one place should we\n   * decide to modify at a later date.\n   *\n   * @enum {string}\n   * @private\n   */\nMaterialRipple.prototype.CssClasses_ = {\n    RIPPLE_CENTER: 'mdl-ripple--center',\n    RIPPLE_EFFECT_IGNORE_EVENTS: 'mdl-js-ripple-effect--ignore-events',\n    RIPPLE: 'mdl-ripple',\n    IS_ANIMATING: 'is-animating',\n    IS_VISIBLE: 'is-visible'\n};\n/**\n   * Handle mouse / finger down on element.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialRipple.prototype.downHandler_ = function (event) {\n    if (!this.rippleElement_.style.width && !this.rippleElement_.style.height) {\n        var rect = this.element_.getBoundingClientRect();\n        this.boundHeight = rect.height;\n        this.boundWidth = rect.width;\n        this.rippleSize_ = Math.sqrt(rect.width * rect.width + rect.height * rect.height) * 2 + 2;\n        this.rippleElement_.style.width = this.rippleSize_ + 'px';\n        this.rippleElement_.style.height = this.rippleSize_ + 'px';\n    }\n    this.rippleElement_.classList.add(this.CssClasses_.IS_VISIBLE);\n    if (event.type === 'mousedown' && this.ignoringMouseDown_) {\n        this.ignoringMouseDown_ = false;\n    } else {\n        if (event.type === 'touchstart') {\n            this.ignoringMouseDown_ = true;\n        }\n        var frameCount = this.getFrameCount();\n        if (frameCount > 0) {\n            return;\n        }\n        this.setFrameCount(1);\n        var bound = event.currentTarget.getBoundingClientRect();\n        var x;\n        var y;\n        // Check if we are handling a keyboard click.\n        if (event.clientX === 0 && event.clientY === 0) {\n            x = Math.round(bound.width / 2);\n            y = Math.round(bound.height / 2);\n        } else {\n            var clientX = event.clientX !== undefined ? event.clientX : event.touches[0].clientX;\n            var clientY = event.clientY !== undefined ? event.clientY : event.touches[0].clientY;\n            x = Math.round(clientX - bound.left);\n            y = Math.round(clientY - bound.top);\n        }\n        this.setRippleXY(x, y);\n        this.setRippleStyles(true);\n        window.requestAnimationFrame(this.animFrameHandler.bind(this));\n    }\n};\n/**\n   * Handle mouse / finger up on element.\n   *\n   * @param {Event} event The event that fired.\n   * @private\n   */\nMaterialRipple.prototype.upHandler_ = function (event) {\n    // Don't fire for the artificial \"mouseup\" generated by a double-click.\n    if (event && event.detail !== 2) {\n        // Allow a repaint to occur before removing this class, so the animation\n        // shows for tap events, which seem to trigger a mouseup too soon after\n        // mousedown.\n        window.setTimeout(function () {\n            this.rippleElement_.classList.remove(this.CssClasses_.IS_VISIBLE);\n        }.bind(this), 0);\n    }\n};\n/**\n   * Initialize element.\n   */\nMaterialRipple.prototype.init = function () {\n    if (this.element_) {\n        var recentering = this.element_.classList.contains(this.CssClasses_.RIPPLE_CENTER);\n        if (!this.element_.classList.contains(this.CssClasses_.RIPPLE_EFFECT_IGNORE_EVENTS)) {\n            this.rippleElement_ = this.element_.querySelector('.' + this.CssClasses_.RIPPLE);\n            this.frameCount_ = 0;\n            this.rippleSize_ = 0;\n            this.x_ = 0;\n            this.y_ = 0;\n            // Touch start produces a compat mouse down event, which would cause a\n            // second ripples. To avoid that, we use this property to ignore the first\n            // mouse down after a touch start.\n            this.ignoringMouseDown_ = false;\n            this.boundDownHandler = this.downHandler_.bind(this);\n            this.element_.addEventListener('mousedown', this.boundDownHandler);\n            this.element_.addEventListener('touchstart', this.boundDownHandler);\n            this.boundUpHandler = this.upHandler_.bind(this);\n            this.element_.addEventListener('mouseup', this.boundUpHandler);\n            this.element_.addEventListener('mouseleave', this.boundUpHandler);\n            this.element_.addEventListener('touchend', this.boundUpHandler);\n            this.element_.addEventListener('blur', this.boundUpHandler);\n            /**\n         * Getter for frameCount_.\n         * @return {number} the frame count.\n         */\n            this.getFrameCount = function () {\n                return this.frameCount_;\n            };\n            /**\n         * Setter for frameCount_.\n         * @param {number} fC the frame count.\n         */\n            this.setFrameCount = function (fC) {\n                this.frameCount_ = fC;\n            };\n            /**\n         * Getter for rippleElement_.\n         * @return {Element} the ripple element.\n         */\n            this.getRippleElement = function () {\n                return this.rippleElement_;\n            };\n            /**\n         * Sets the ripple X and Y coordinates.\n         * @param  {number} newX the new X coordinate\n         * @param  {number} newY the new Y coordinate\n         */\n            this.setRippleXY = function (newX, newY) {\n                this.x_ = newX;\n                this.y_ = newY;\n            };\n            /**\n         * Sets the ripple styles.\n         * @param  {boolean} start whether or not this is the start frame.\n         */\n            this.setRippleStyles = function (start) {\n                if (this.rippleElement_ !== null) {\n                    var transformString;\n                    var scale;\n                    var size;\n                    var offset = 'translate(' + this.x_ + 'px, ' + this.y_ + 'px)';\n                    if (start) {\n                        scale = this.Constant_.INITIAL_SCALE;\n                        size = this.Constant_.INITIAL_SIZE;\n                    } else {\n                        scale = this.Constant_.FINAL_SCALE;\n                        size = this.rippleSize_ + 'px';\n                        if (recentering) {\n                            offset = 'translate(' + this.boundWidth / 2 + 'px, ' + this.boundHeight / 2 + 'px)';\n                        }\n                    }\n                    transformString = 'translate(-50%, -50%) ' + offset + scale;\n                    this.rippleElement_.style.webkitTransform = transformString;\n                    this.rippleElement_.style.msTransform = transformString;\n                    this.rippleElement_.style.transform = transformString;\n                    if (start) {\n                        this.rippleElement_.classList.remove(this.CssClasses_.IS_ANIMATING);\n                    } else {\n                        this.rippleElement_.classList.add(this.CssClasses_.IS_ANIMATING);\n                    }\n                }\n            };\n            /**\n         * Handles an animation frame.\n         */\n            this.animFrameHandler = function () {\n                if (this.frameCount_-- > 0) {\n                    window.requestAnimationFrame(this.animFrameHandler.bind(this));\n                } else {\n                    this.setRippleStyles(false);\n                }\n            };\n        }\n    }\n};\n// The component registers itself. It can assume componentHandler is available\n// in the global scope.\ncomponentHandler.register({\n    constructor: MaterialRipple,\n    classAsString: 'MaterialRipple',\n    cssClass: 'mdl-js-ripple-effect',\n    widget: false\n});"]}