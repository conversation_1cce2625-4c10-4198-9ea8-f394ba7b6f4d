/*! For license information please see main.js.LICENSE.txt */
(()=>{var __webpack_modules__={556:function(module){var factory;"undefined"!=typeof self&&self,factory=function(){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s="./src/web/index.js")}({"./node_modules/base64-js/index.js":function(e,t,r){"use strict";t.byteLength=function(e){var t=f(e),r=t[0],n=t[1];return 3*(r+n)/4-n},t.toByteArray=function(e){for(var t,r=f(e),n=r[0],s=r[1],a=new o(function(e,t,r){return 3*(t+r)/4-r}(0,n,s)),u=0,c=s>0?n-4:n,h=0;h<c;h+=4)t=i[e.charCodeAt(h)]<<18|i[e.charCodeAt(h+1)]<<12|i[e.charCodeAt(h+2)]<<6|i[e.charCodeAt(h+3)],a[u++]=t>>16&255,a[u++]=t>>8&255,a[u++]=255&t;return 2===s&&(t=i[e.charCodeAt(h)]<<2|i[e.charCodeAt(h+1)]>>4,a[u++]=255&t),1===s&&(t=i[e.charCodeAt(h)]<<10|i[e.charCodeAt(h+1)]<<4|i[e.charCodeAt(h+2)]>>2,a[u++]=t>>8&255,a[u++]=255&t),a},t.fromByteArray=function(e){for(var t,r=e.length,i=r%3,o=[],s=16383,a=0,u=r-i;a<u;a+=s)o.push(c(e,a,a+s>u?u:a+s));return 1===i?(t=e[r-1],o.push(n[t>>2]+n[t<<4&63]+"==")):2===i&&(t=(e[r-2]<<8)+e[r-1],o.push(n[t>>10]+n[t>>4&63]+n[t<<2&63]+"=")),o.join("")};for(var n=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,u=s.length;a<u;++a)n[a]=s[a],i[s.charCodeAt(a)]=a;function f(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function c(e,t,r){for(var i,o,s=[],a=t;a<r;a+=3)i=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),s.push(n[(o=i)>>18&63]+n[o>>12&63]+n[o>>6&63]+n[63&o]);return s.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},"./node_modules/heap/index.js":function(e,t,r){e.exports=r("./node_modules/heap/lib/heap.js")},"./node_modules/heap/lib/heap.js":function(e,t,r){var n,i,o;(function(){var r,s,a,u,f,c,h,l,d,p,_,g,m,y,w;a=Math.floor,p=Math.min,s=function(e,t){return e<t?-1:e>t?1:0},d=function(e,t,r,n,i){var o;if(null==r&&(r=0),null==i&&(i=s),r<0)throw new Error("lo must be non-negative");for(null==n&&(n=e.length);r<n;)i(t,e[o=a((r+n)/2)])<0?n=o:r=o+1;return[].splice.apply(e,[r,r-r].concat(t)),t},c=function(e,t,r){return null==r&&(r=s),e.push(t),y(e,0,e.length-1,r)},f=function(e,t){var r,n;return null==t&&(t=s),r=e.pop(),e.length?(n=e[0],e[0]=r,w(e,0,t)):n=r,n},l=function(e,t,r){var n;return null==r&&(r=s),n=e[0],e[0]=t,w(e,0,r),n},h=function(e,t,r){var n;return null==r&&(r=s),e.length&&r(e[0],t)<0&&(t=(n=[e[0],t])[0],e[0]=n[1],w(e,0,r)),t},u=function(e,t){var r,n,i,o,u,f;for(null==t&&(t=s),u=[],n=0,i=(o=function(){f=[];for(var t=0,r=a(e.length/2);0<=r?t<r:t>r;0<=r?t++:t--)f.push(t);return f}.apply(this).reverse()).length;n<i;n++)r=o[n],u.push(w(e,r,t));return u},m=function(e,t,r){var n;if(null==r&&(r=s),-1!==(n=e.indexOf(t)))return y(e,0,n,r),w(e,n,r)},_=function(e,t,r){var n,i,o,a,f;if(null==r&&(r=s),!(i=e.slice(0,t)).length)return i;for(u(i,r),o=0,a=(f=e.slice(t)).length;o<a;o++)n=f[o],h(i,n,r);return i.sort(r).reverse()},g=function(e,t,r){var n,i,o,a,c,h,l,_,g;if(null==r&&(r=s),10*t<=e.length){if(!(o=e.slice(0,t).sort(r)).length)return o;for(i=o[o.length-1],a=0,h=(l=e.slice(t)).length;a<h;a++)r(n=l[a],i)<0&&(d(o,n,0,null,r),o.pop(),i=o[o.length-1]);return o}for(u(e,r),g=[],c=0,_=p(t,e.length);0<=_?c<_:c>_;0<=_?++c:--c)g.push(f(e,r));return g},y=function(e,t,r,n){var i,o,a;for(null==n&&(n=s),i=e[r];r>t&&n(i,o=e[a=r-1>>1])<0;)e[r]=o,r=a;return e[r]=i},w=function(e,t,r){var n,i,o,a,u;for(null==r&&(r=s),i=e.length,u=t,o=e[t],n=2*t+1;n<i;)(a=n+1)<i&&!(r(e[n],e[a])<0)&&(n=a),e[t]=e[n],n=2*(t=n)+1;return e[t]=o,y(e,u,t,r)},r=function(){function e(e){this.cmp=null!=e?e:s,this.nodes=[]}return e.push=c,e.pop=f,e.replace=l,e.pushpop=h,e.heapify=u,e.updateItem=m,e.nlargest=_,e.nsmallest=g,e.prototype.push=function(e){return c(this.nodes,e,this.cmp)},e.prototype.pop=function(){return f(this.nodes,this.cmp)},e.prototype.peek=function(){return this.nodes[0]},e.prototype.contains=function(e){return-1!==this.nodes.indexOf(e)},e.prototype.replace=function(e){return l(this.nodes,e,this.cmp)},e.prototype.pushpop=function(e){return h(this.nodes,e,this.cmp)},e.prototype.heapify=function(){return u(this.nodes,this.cmp)},e.prototype.updateItem=function(e){return m(this.nodes,e,this.cmp)},e.prototype.clear=function(){return this.nodes=[]},e.prototype.empty=function(){return 0===this.nodes.length},e.prototype.size=function(){return this.nodes.length},e.prototype.clone=function(){var t;return(t=new e).nodes=this.nodes.slice(0),t},e.prototype.toArray=function(){return this.nodes.slice(0)},e.prototype.insert=e.prototype.push,e.prototype.top=e.prototype.peek,e.prototype.front=e.prototype.peek,e.prototype.has=e.prototype.contains,e.prototype.copy=e.prototype.clone,e}(),i=[],void 0===(o="function"==typeof(n=function(){return r})?n.apply(t,i):n)||(e.exports=o)}).call(this)},"./node_modules/ieee754/index.js":function(e,t){t.read=function(e,t,r,n,i){var o,s,a=8*i-n-1,u=(1<<a)-1,f=u>>1,c=-7,h=r?i-1:0,l=r?-1:1,d=e[t+h];for(h+=l,o=d&(1<<-c)-1,d>>=-c,c+=a;c>0;o=256*o+e[t+h],h+=l,c-=8);for(s=o&(1<<-c)-1,o>>=-c,c+=n;c>0;s=256*s+e[t+h],h+=l,c-=8);if(0===o)o=1-f;else{if(o===u)return s?NaN:1/0*(d?-1:1);s+=Math.pow(2,n),o-=f}return(d?-1:1)*s*Math.pow(2,o-n)},t.write=function(e,t,r,n,i,o){var s,a,u,f=8*o-i-1,c=(1<<f)-1,h=c>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=n?0:o-1,p=n?1:-1,_=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=c):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),(t+=s+h>=1?l/u:l*Math.pow(2,1-h))*u>=2&&(s++,u/=2),s+h>=c?(a=0,s=c):s+h>=1?(a=(t*u-1)*Math.pow(2,i),s+=h):(a=t*Math.pow(2,h-1)*Math.pow(2,i),s=0));i>=8;e[r+d]=255&a,d+=p,a/=256,i-=8);for(s=s<<i|a,f+=i;f>0;e[r+d]=255&s,d+=p,s/=256,f-=8);e[r+d-p]|=128*_}},"./node_modules/int53/index.js":function(e,t){var r={},n=4294967295;function i(e,t){if(!e)throw new Error(t)}function o(e){return(e=~e)<0&&(e=2147483648+(2147483647&e)),e}function s(e){i(e>-1&&e<=9007199254740991,"number out of range"),i(Math.floor(e)===e,"number must be an integer");var t=0,r=4294967295&e,o=r<0?2147483648+(2147483647&e):r;return e>n&&(t=(e-o)/4294967296),[t,o]}function a(e){if(e>-1)return s(e);var t=s(-e),r=o(t[0]),i=o(t[1]);return i===n?(r+=1,i=0):i+=1,[r,i]}function u(e,t,r){return r&&0!=(2147483648&e)?(e=o(e),t=o(t),i(e<2097152,"number too small"),-(4294967296*e+t+1)):(i(e<2097152,"number too large"),4294967296*e+t)}r.readInt64BE=function(e,t){return t=t||0,u(e.readUInt32BE(t),e.readUInt32BE(t+4),!0)},r.readInt64LE=function(e,t){t=t||0;var r=e.readUInt32LE(t);return u(e.readUInt32LE(t+4),r,!0)},r.readUInt64BE=function(e,t){return t=t||0,u(e.readUInt32BE(t),e.readUInt32BE(t+4),!1)},r.readUInt64LE=function(e,t){t=t||0;var r=e.readUInt32LE(t);return u(e.readUInt32LE(t+4),r,!1)},r.writeInt64BE=function(e,t,r){r=r||0;var n=a(e);t.writeUInt32BE(n[0],r),t.writeUInt32BE(n[1],r+4)},r.writeInt64LE=function(e,t,r){r=r||0;var n=a(e);t.writeUInt32LE(n[1],r),t.writeUInt32LE(n[0],r+4)},r.writeUInt64BE=function(e,t,r){r=r||0;var n=s(e);t.writeUInt32BE(n[0],r),t.writeUInt32BE(n[1],r+4)},r.writeUInt64LE=function(e,t,r){r=r||0;var n=s(e);t.writeUInt32LE(n[1],r),t.writeUInt32LE(n[0],r+4)},e.exports=r},"./node_modules/isarray/index.js":function(e,t){var r={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==r.call(e)}},"./node_modules/node-libs-browser/node_modules/buffer/index.js":function(e,t,r){"use strict";(function(e){var n=r("./node_modules/base64-js/index.js"),i=r("./node_modules/ieee754/index.js"),o=r("./node_modules/isarray/index.js");function s(){return u.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function a(e,t){if(s()<t)throw new RangeError("Invalid typed array length");return u.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=u.prototype:(null===e&&(e=new u(t)),e.length=t),e}function u(e,t,r){if(!(u.TYPED_ARRAY_SUPPORT||this instanceof u))return new u(e,t,r);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return h(this,e)}return f(this,e,t,r)}function f(e,t,r,n){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");return t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n),u.TYPED_ARRAY_SUPPORT?(e=t).__proto__=u.prototype:e=l(e,t),e}(e,t,r,n):"string"==typeof t?function(e,t,r){if("string"==typeof r&&""!==r||(r="utf8"),!u.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|p(t,r),i=(e=a(e,n)).write(t,r);return i!==n&&(e=e.slice(0,i)),e}(e,t,r):function(e,t){if(u.isBuffer(t)){var r=0|d(t.length);return 0===(e=a(e,r)).length||t.copy(e,0,0,r),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(n=t.length)!=n?a(e,0):l(e,t);if("Buffer"===t.type&&o(t.data))return l(e,t.data)}var n;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function c(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function h(e,t){if(c(t),e=a(e,t<0?0:0|d(t)),!u.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function l(e,t){var r=t.length<0?0:0|d(t.length);e=a(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function d(e){if(e>=s())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+s().toString(16)+" bytes");return 0|e}function p(e,t){if(u.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return z(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return N(e).length;default:if(n)return z(e).length;t=(""+t).toLowerCase(),n=!0}}function _(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return C(this,t,r);case"utf8":case"utf-8":return R(this,t,r);case"ascii":return M(this,t,r);case"latin1":case"binary":return B(this,t,r);case"base64":return I(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return x(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function g(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function m(e,t,r,n,i){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof t&&(t=u.from(t,n)),u.isBuffer(t))return 0===t.length?-1:y(e,t,r,n,i);if("number"==typeof t)return t&=255,u.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):y(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function y(e,t,r,n,i){var o,s=1,a=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;s=2,a/=2,u/=2,r/=2}function f(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(i){var c=-1;for(o=r;o<a;o++)if(f(e,o)===f(t,-1===c?0:o-c)){if(-1===c&&(c=o),o-c+1===u)return c*s}else-1!==c&&(o-=o-c),c=-1}else for(r+u>a&&(r=a-u),o=r;o>=0;o--){for(var h=!0,l=0;l<u;l++)if(f(e,o+l)!==f(t,l)){h=!1;break}if(h)return o}return-1}function w(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;if(o%2!=0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a=parseInt(t.substr(2*s,2),16);if(isNaN(a))return s;e[r+s]=a}return s}function v(e,t,r,n){return Y(z(t,e.length-r),e,r,n)}function b(e,t,r,n){return Y(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function E(e,t,r,n){return b(e,t,r,n)}function T(e,t,r,n){return Y(N(t),e,r,n)}function A(e,t,r,n){return Y(function(e,t){for(var r,n,i,o=[],s=0;s<e.length&&!((t-=2)<0);++s)n=(r=e.charCodeAt(s))>>8,i=r%256,o.push(i),o.push(n);return o}(t,e.length-r),e,r,n)}function I(e,t,r){return 0===t&&r===e.length?n.fromByteArray(e):n.fromByteArray(e.slice(t,r))}function R(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o,s,a,u,f=e[i],c=null,h=f>239?4:f>223?3:f>191?2:1;if(i+h<=r)switch(h){case 1:f<128&&(c=f);break;case 2:128==(192&(o=e[i+1]))&&(u=(31&f)<<6|63&o)>127&&(c=u);break;case 3:o=e[i+1],s=e[i+2],128==(192&o)&&128==(192&s)&&(u=(15&f)<<12|(63&o)<<6|63&s)>2047&&(u<55296||u>57343)&&(c=u);break;case 4:o=e[i+1],s=e[i+2],a=e[i+3],128==(192&o)&&128==(192&s)&&128==(192&a)&&(u=(15&f)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&u<1114112&&(c=u)}null===c?(c=65533,h=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=h}return function(e){var t=e.length;if(t<=P)return String.fromCharCode.apply(String,e);for(var r="",n=0;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=P));return r}(n)}t.Buffer=u,t.SlowBuffer=function(e){return+e!=e&&(e=0),u.alloc(+e)},t.INSPECT_MAX_BYTES=50,u.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=s(),u.poolSize=8192,u._augment=function(e){return e.__proto__=u.prototype,e},u.from=function(e,t,r){return f(null,e,t,r)},u.TYPED_ARRAY_SUPPORT&&(u.prototype.__proto__=Uint8Array.prototype,u.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&u[Symbol.species]===u&&Object.defineProperty(u,Symbol.species,{value:null,configurable:!0})),u.alloc=function(e,t,r){return function(e,t,r,n){return c(t),t<=0?a(e,t):void 0!==r?"string"==typeof n?a(e,t).fill(r,n):a(e,t).fill(r):a(e,t)}(null,e,t,r)},u.allocUnsafe=function(e){return h(null,e)},u.allocUnsafeSlow=function(e){return h(null,e)},u.isBuffer=function(e){return!(null==e||!e._isBuffer)},u.compare=function(e,t){if(!u.isBuffer(e)||!u.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},u.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(e,t){if(!o(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return u.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=u.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var s=e[r];if(!u.isBuffer(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(n,i),i+=s.length}return n},u.byteLength=p,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)g(this,t,t+1);return this},u.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},u.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},u.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?R(this,0,e):_.apply(this,arguments)},u.prototype.equals=function(e){if(!u.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===u.compare(this,e)},u.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(e+=" ... ")),"<Buffer "+e+">"},u.prototype.compare=function(e,t,r,n,i){if(!u.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(this===e)return 0;for(var o=(i>>>=0)-(n>>>=0),s=(r>>>=0)-(t>>>=0),a=Math.min(o,s),f=this.slice(n,i),c=e.slice(t,r),h=0;h<a;++h)if(f[h]!==c[h]){o=f[h],s=c[h];break}return o<s?-1:s<o?1:0},u.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},u.prototype.indexOf=function(e,t,r){return m(this,e,t,r,!0)},u.prototype.lastIndexOf=function(e,t,r){return m(this,e,t,r,!1)},u.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return w(this,e,t,r);case"utf8":case"utf-8":return v(this,e,t,r);case"ascii":return b(this,e,t,r);case"latin1":case"binary":return E(this,e,t,r);case"base64":return T(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return A(this,e,t,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var P=4096;function M(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function B(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function C(e,t,r){var n,i=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>i)&&(r=i);for(var o="",s=t;s<r;++s)o+=(n=e[s])<16?"0"+n.toString(16):n.toString(16);return o}function x(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function U(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function O(e,t,r,n,i,o){if(!u.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function j(e,t,r,n){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-r,2);i<o;++i)e[r+i]=(t&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function k(e,t,r,n){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-r,4);i<o;++i)e[r+i]=t>>>8*(n?i:3-i)&255}function S(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function L(e,t,r,n,o){return o||S(e,0,r,4),i.write(e,t,r,n,23,4),r+4}function D(e,t,r,n,o){return o||S(e,0,r,8),i.write(e,t,r,n,52,8),r+8}u.prototype.slice=function(e,t){var r,n=this.length;if((e=~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),(t=void 0===t?n:~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e),u.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=u.prototype;else{var i=t-e;r=new u(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+e]}return r},u.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||U(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},u.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||U(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},u.prototype.readUInt8=function(e,t){return t||U(e,1,this.length),this[e]},u.prototype.readUInt16LE=function(e,t){return t||U(e,2,this.length),this[e]|this[e+1]<<8},u.prototype.readUInt16BE=function(e,t){return t||U(e,2,this.length),this[e]<<8|this[e+1]},u.prototype.readUInt32LE=function(e,t){return t||U(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},u.prototype.readUInt32BE=function(e,t){return t||U(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},u.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||U(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},u.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||U(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},u.prototype.readInt8=function(e,t){return t||U(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},u.prototype.readInt16LE=function(e,t){t||U(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt16BE=function(e,t){t||U(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt32LE=function(e,t){return t||U(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},u.prototype.readInt32BE=function(e,t){return t||U(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},u.prototype.readFloatLE=function(e,t){return t||U(e,4,this.length),i.read(this,e,!0,23,4)},u.prototype.readFloatBE=function(e,t){return t||U(e,4,this.length),i.read(this,e,!1,23,4)},u.prototype.readDoubleLE=function(e,t){return t||U(e,8,this.length),i.read(this,e,!0,52,8)},u.prototype.readDoubleBE=function(e,t){return t||U(e,8,this.length),i.read(this,e,!1,52,8)},u.prototype.writeUIntLE=function(e,t,r,n){e=+e,t|=0,r|=0,n||O(this,e,t,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[t]=255&e;++o<r&&(i*=256);)this[t+o]=e/i&255;return t+r},u.prototype.writeUIntBE=function(e,t,r,n){e=+e,t|=0,r|=0,n||O(this,e,t,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+r},u.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,1,255,0),u.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},u.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):j(this,e,t,!0),t+2},u.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):j(this,e,t,!1),t+2},u.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):k(this,e,t,!0),t+4},u.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):k(this,e,t,!1),t+4},u.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);O(this,e,t,r,i-1,-i)}var o=0,s=1,a=0;for(this[t]=255&e;++o<r&&(s*=256);)e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+r},u.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);O(this,e,t,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[t+o]=255&e;--o>=0&&(s*=256);)e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+r},u.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,1,127,-128),u.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},u.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):j(this,e,t,!0),t+2},u.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):j(this,e,t,!1),t+2},u.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,2147483647,-2147483648),u.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):k(this,e,t,!0),t+4},u.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||O(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),u.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):k(this,e,t,!1),t+4},u.prototype.writeFloatLE=function(e,t,r){return L(this,e,t,!0,r)},u.prototype.writeFloatBE=function(e,t,r){return L(this,e,t,!1,r)},u.prototype.writeDoubleLE=function(e,t,r){return D(this,e,t,!0,r)},u.prototype.writeDoubleBE=function(e,t,r){return D(this,e,t,!1,r)},u.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,o=n-r;if(this===e&&r<t&&t<n)for(i=o-1;i>=0;--i)e[i+t]=this[i+r];else if(o<1e3||!u.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+o),t);return o},u.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!u.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var o;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(o=t;o<r;++o)this[o]=e;else{var s=u.isBuffer(e)?e:z(new u(e,n).toString()),a=s.length;for(o=0;o<r-t;++o)this[o+t]=s[o%a]}return this};var $=/[^+\/0-9A-Za-z-_]/g;function z(e,t){var r;t=t||1/0;for(var n=e.length,i=null,o=[],s=0;s<n;++s){if((r=e.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(s+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function N(e){return n.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace($,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function Y(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}}).call(this,r("./node_modules/webpack/buildin/global.js"))},"./node_modules/process/browser.js":function(e,t){var r,n,i=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function a(e){if(r===setTimeout)return setTimeout(e,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(e){r=o}try{n="function"==typeof clearTimeout?clearTimeout:s}catch(e){n=s}}();var u,f=[],c=!1,h=-1;function l(){c&&u&&(c=!1,u.length?f=u.concat(f):h=-1,f.length&&d())}function d(){if(!c){var e=a(l);c=!0;for(var t=f.length;t;){for(u=f,f=[];++h<t;)u&&u[h].run();h=-1,t=f.length}u=null,c=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===s||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function _(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];f.push(new p(e,t)),1!==f.length||c||a(d)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=_,i.addListener=_,i.once=_,i.off=_,i.removeListener=_,i.removeAllListeners=_,i.emit=_,i.prependListener=_,i.prependOnceListener=_,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},"./node_modules/setimmediate/setImmediate.js":function(e,t,r){(function(e,t){!function(e,r){"use strict";if(!e.setImmediate){var n,i,o,s,a,u=1,f={},c=!1,h=e.document,l=Object.getPrototypeOf&&Object.getPrototypeOf(e);l=l&&l.setTimeout?l:e,"[object process]"==={}.toString.call(e.process)?n=function(e){t.nextTick((function(){p(e)}))}:function(){if(e.postMessage&&!e.importScripts){var t=!0,r=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=r,t}}()?(s="setImmediate$"+Math.random()+"$",a=function(t){t.source===e&&"string"==typeof t.data&&0===t.data.indexOf(s)&&p(+t.data.slice(s.length))},e.addEventListener?e.addEventListener("message",a,!1):e.attachEvent("onmessage",a),n=function(t){e.postMessage(s+t,"*")}):e.MessageChannel?((o=new MessageChannel).port1.onmessage=function(e){p(e.data)},n=function(e){o.port2.postMessage(e)}):h&&"onreadystatechange"in h.createElement("script")?(i=h.documentElement,n=function(e){var t=h.createElement("script");t.onreadystatechange=function(){p(e),t.onreadystatechange=null,i.removeChild(t),t=null},i.appendChild(t)}):n=function(e){setTimeout(p,0,e)},l.setImmediate=function(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),r=0;r<t.length;r++)t[r]=arguments[r+1];var i={callback:e,args:t};return f[u]=i,n(u),u++},l.clearImmediate=d}function d(e){delete f[e]}function p(e){if(c)setTimeout(p,0,e);else{var t=f[e];if(t){c=!0;try{!function(e){var t=e.callback,r=e.args;switch(r.length){case 0:t();break;case 1:t(r[0]);break;case 2:t(r[0],r[1]);break;case 3:t(r[0],r[1],r[2]);break;default:t.apply(void 0,r)}}(t)}finally{d(e),c=!1}}}}}("undefined"==typeof self?void 0===e?this:e:self)}).call(this,r("./node_modules/webpack/buildin/global.js"),r("./node_modules/process/browser.js"))},"./node_modules/timers-browserify/main.js":function(e,t,r){(function(e){var n=void 0!==e&&e||"undefined"!=typeof self&&self||window,i=Function.prototype.apply;function o(e,t){this._id=e,this._clearFn=t}t.setTimeout=function(){return new o(i.call(setTimeout,n,arguments),clearTimeout)},t.setInterval=function(){return new o(i.call(setInterval,n,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},o.prototype.unref=o.prototype.ref=function(){},o.prototype.close=function(){this._clearFn.call(n,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout((function(){e._onTimeout&&e._onTimeout()}),t))},r("./node_modules/setimmediate/setImmediate.js"),t.setImmediate="undefined"!=typeof self&&self.setImmediate||void 0!==e&&e.setImmediate||this&&this.setImmediate,t.clearImmediate="undefined"!=typeof self&&self.clearImmediate||void 0!==e&&e.clearImmediate||this&&this.clearImmediate}).call(this,r("./node_modules/webpack/buildin/global.js"))},"./node_modules/webpack/buildin/global.js":function(e,t){var r;r=function(){return this}();try{r=r||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(r=window)}e.exports=r},"./src/BagReader.js":function(e,t,r){"use strict";r.r(t),function(e){r.d(t,"default",(function(){return u}));var n=r("./src/header.js"),i=r("./src/nmerge.js"),o=r("./src/record.js"),s=r("./src/TimeUtil.js");function a(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class u{constructor(e){a(this,"_lastReadResult",void 0),a(this,"_file",void 0),a(this,"_lastChunkInfo",void 0),this._file=e,this._lastChunkInfo=void 0}verifyBagHeader(e,t){this._file.read(0,13,((r,n)=>r||!n?e(r||new Error("Missing both error and buffer")):this._file.size()<13?e(new Error("Missing file header.")):"#ROSBAG V2.0\n"!==n.toString()?e(new Error("Cannot identify bag format.")):void t()))}readHeader(e){this.verifyBagHeader(e,(()=>this._file.read(13,4096,((t,r)=>{if(t||!r)return e(t||new Error("Missing both error and buffer"));const n=r.length;if(n<8)return e(new Error("Record at position 13 is truncated."));const i=r.readInt32LE(0);if(n<i+8)return e(new Error(`Record at position 13 header too large: ${i}.`));try{const t=this.readRecordFromBuffer(r,13,o.BagHeader);return e(null,t)}catch(t){return e(new Error(`Could not read header from rosbag file buffer - ${t.message}`))}}))))}readHeaderAsync(){return new Promise(((e,t)=>this.readHeader(((r,n)=>r||!n?t(r):e(n)))))}readConnectionsAndChunkInfo(e,t,r,n){this._file.read(e,this._file.size()-e,((i,s)=>{if(i||!s)return n(i||new Error("Missing both error and buffer"));if(0===t)return n(null,{connections:[],chunkInfos:[]});const a=this.readRecordsFromBuffer(s,t,e,o.Connection),u=a[t-1].end-a[0].offset,f=this.readRecordsFromBuffer(s.slice(u),r,e+u,o.ChunkInfo);if(r>0){for(let e=0;e<r-1;e++)f[e].nextChunk=f[e+1];f[r-1].nextChunk=null}return n(null,{connections:a,chunkInfos:f})}))}readConnectionsAndChunkInfoAsync(e,t,r){return new Promise(((n,i)=>{this.readConnectionsAndChunkInfo(e,t,r,((e,t)=>e||!t?i(e):n(t)))}))}readChunkMessages(e,t,r,n,a,u){const f=r||{sec:0,nsec:0},c=n||{sec:Number.MAX_VALUE,nsec:Number.MAX_VALUE},h=t||e.connections.map((e=>e.conn));this.readChunk(e,a,((e,t)=>{if(e||!t)return u(e||new Error("Missing both error and result"));const r=t.chunk,n={};t.indices.forEach((e=>{n[e.conn]=e}));const a=h.filter((e=>void 0!==n[e])).map((e=>n[e].indices[Symbol.iterator]())),l=Object(i.default)(((e,t)=>s.compare(e.time,t.time)),...a),d=[];let p=l.next();for(;!p.done;){const{value:e}=p;if(p=l.next(),e&&!s.isGreaterThan(f,e.time)){if(s.isGreaterThan(e.time,c))break;d.push(e)}}const _=d.map((e=>this.readRecordFromBuffer(r.data.slice(e.offset),r.dataOffset,o.MessageData)));return u(null,_)}))}readChunkMessagesAsync(e,t,r,n,i){return new Promise(((o,s)=>{this.readChunkMessages(e,t,r,n,i,((e,t)=>e||!t?s(e):o(t)))}))}readChunk(t,r,n){if(t===this._lastChunkInfo&&this._lastReadResult){const t=this._lastReadResult;return e((()=>n(null,t)))}const{nextChunk:i}=t,s=i?i.chunkPosition-t.chunkPosition:this._file.size()-t.chunkPosition;this._file.read(t.chunkPosition,s,((e,i)=>{if(e||!i)return n(e||new Error("Missing both error and buffer"));const s=this.readRecordFromBuffer(i,t.chunkPosition,o.Chunk),{compression:a}=s;if("none"!==a){const e=r[a];if(!e)return n(new Error(`Unsupported compression type ${s.compression}`));const t=e(s.data,s.size);s.data=t}const u=this.readRecordsFromBuffer(i.slice(s.length),t.count,t.chunkPosition+s.length,o.IndexData);return this._lastChunkInfo=t,this._lastReadResult={chunk:s,indices:u},n(null,this._lastReadResult)}))}readRecordsFromBuffer(e,t,r,n){const i=[];let o=0;for(let s=0;s<t;s++){const t=this.readRecordFromBuffer(e.slice(o),r+o,n);o+=t.end-t.offset,i.push(t)}return i}readRecordFromBuffer(e,t,r){const i=e.readInt32LE(0),o=Object(n.parseHeader)(e.slice(4,4+i),r),s=4+i+4,a=e.readInt32LE(4+i),u=e.slice(s,s+a);return o.parseData(u),o.offset=t,o.dataOffset=o.offset+4+i+4,o.end=o.dataOffset+a,o.length=o.end-o.offset,o}}}.call(this,r("./node_modules/timers-browserify/main.js").setImmediate)},"./src/MessageReader.js":function(module,__webpack_exports__,__nested_webpack_require_100263__){"use strict";__nested_webpack_require_100263__.r(__webpack_exports__),function(global){__nested_webpack_require_100263__.d(__webpack_exports__,"MessageReader",(function(){return MessageReader}));var int53__WEBPACK_IMPORTED_MODULE_0__=__nested_webpack_require_100263__("./node_modules/int53/index.js"),int53__WEBPACK_IMPORTED_MODULE_0___default=__nested_webpack_require_100263__.n(int53__WEBPACK_IMPORTED_MODULE_0__),_fields__WEBPACK_IMPORTED_MODULE_1__=__nested_webpack_require_100263__("./src/fields.js"),_parseMessageDefinition__WEBPACK_IMPORTED_MODULE_2__=__nested_webpack_require_100263__("./src/parseMessageDefinition.js");function _defineProperty(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class StandardTypeReader{constructor(e){_defineProperty(this,"buffer",void 0),_defineProperty(this,"offset",void 0),_defineProperty(this,"view",void 0),_defineProperty(this,"_decoder",void 0),_defineProperty(this,"_decoderStatus","NOT_INITIALIZED"),this.buffer=e,this.offset=0,this.view=new DataView(e.buffer,e.byteOffset)}_intializeTextDecoder(){if(void 0!==global.TextDecoder)try{this._decoder=new global.TextDecoder("ascii"),this._decoderStatus="INITIALIZED"}catch(e){this._decoderStatus="NOT_AVAILABLE"}else this._decoderStatus="NOT_AVAILABLE"}json(){const e=this.string();try{return JSON.parse(e)}catch(t){return`Could not parse ${e}`}}string(){const e=this.int32(),t=new Uint8Array(this.buffer.buffer,this.buffer.byteOffset+this.offset,e);if(this.offset+=e,t.length<1e3)return String.fromCharCode.apply(null,t);if("NOT_INITIALIZED"===this._decoderStatus&&this._intializeTextDecoder(),this._decoder)return this._decoder.decode(t);let r="";for(let n=0;n<e;n++)r+=String.fromCharCode(t[n]);return r}bool(){return 0!==this.uint8()}int8(){return this.view.getInt8(this.offset++)}uint8(){return this.view.getUint8(this.offset++)}typedArray(e,t){const r=null==e?this.uint32():e,n=new t(this.view.buffer,this.offset+this.view.byteOffset,r);return this.offset+=r,n}int16(){const e=this.view.getInt16(this.offset,!0);return this.offset+=2,e}uint16(){const e=this.view.getUint16(this.offset,!0);return this.offset+=2,e}int32(){const e=this.view.getInt32(this.offset,!0);return this.offset+=4,e}uint32(){const e=this.view.getUint32(this.offset,!0);return this.offset+=4,e}float32(){const e=this.view.getFloat32(this.offset,!0);return this.offset+=4,e}float64(){const e=this.view.getFloat64(this.offset,!0);return this.offset+=8,e}int64(){const e=this.offset;return this.offset+=8,int53__WEBPACK_IMPORTED_MODULE_0___default.a.readInt64LE(this.buffer,e)}uint64(){const e=this.offset;return this.offset+=8,int53__WEBPACK_IMPORTED_MODULE_0___default.a.readUInt64LE(this.buffer,e)}time(){const e=this.offset;return this.offset+=8,Object(_fields__WEBPACK_IMPORTED_MODULE_1__.extractTime)(this.buffer,e)}duration(){const e=this.offset;return this.offset+=8,Object(_fields__WEBPACK_IMPORTED_MODULE_1__.extractTime)(this.buffer,e)}}const findTypeByName=(e,t="")=>{let r="";const n=e.filter((e=>{const n=e.name||"";if(!t)return!n;const i=t.indexOf("/")>-1?t:`/${t}`;return!!n.endsWith(i)&&(r=n,!0)}));if(1!==n.length)throw new Error(`Expected 1 top level type definition for '${t}' but found ${n.length}.`);return{...n[0],name:r}},friendlyName=e=>e.replace(/\//g,"_"),createParser=(types,freeze)=>{const unnamedTypes=types.filter((e=>!e.name));if(1!==unnamedTypes.length)throw new Error("multiple unnamed types");const[unnamedType]=unnamedTypes,namedTypes=types.filter((e=>!!e.name)),constructorBody=e=>{const t=[];return e.definitions.forEach((e=>{if(!e.isConstant)if(e.isArray){if("uint8"===e.type||"int8"===e.type){const r="uint8"===e.type?"Uint8Array":"Int8Array";return void t.push(`this.${e.name} = reader.typedArray(${String(e.arrayLength)}, ${r});`)}const r=`length_${e.name}`;t.push(`var ${r} = ${e.arrayLength?e.arrayLength:"reader.uint32();"}`);const n=`this.${e.name}`;if(t.push(`${n} = new Array(${r})`),t.push(`for (var i = 0; i < ${r}; i++) {`),e.isComplex){const r=findTypeByName(types,e.type);t.push(`  ${n}[i] = new Record.${friendlyName(r.name)}(reader);`)}else t.push(`  ${n}[i] = reader.${e.type}();`);t.push("}")}else if(e.isComplex){const r=findTypeByName(types,e.type);t.push(`this.${e.name} = new Record.${friendlyName(r.name)}(reader);`)}else t.push(`this.${e.name} = reader.${e.type}();`)})),freeze&&t.push("Object.freeze(this);"),t.join("\n    ")};let js=`\n  var Record = function (reader) {\n    ${constructorBody(unnamedType)}\n  };\n`,_read;namedTypes.forEach((e=>{js+=`\n  Record.${friendlyName(e.name)} = function(reader) {\n    ${constructorBody(e)}\n  };\n`})),js+="\n  return function read(reader) {\n    return new Record(reader);\n  };";try{_read=eval(`(function buildReader() { ${js} })()`)}catch(e){throw console.error("error building parser:",js),e}return function(e){const t=new StandardTypeReader(e);return _read(t)}};class MessageReader{constructor(e,t={}){_defineProperty(this,"reader",void 0);let r=e;"string"==typeof r&&(console.warn("Passing string message defintions to MessageReader is deprecated. Instead call `parseMessageDefinition` on it and pass in the resulting parsed message definition object."),r=Object(_parseMessageDefinition__WEBPACK_IMPORTED_MODULE_2__.parseMessageDefinition)(r)),this.reader=createParser(r,!!t.freeze)}readMessage(e){return this.reader(e)}}}.call(this,__nested_webpack_require_100263__("./node_modules/webpack/buildin/global.js"))},"./src/MessageWriter.js":function(module,__webpack_exports__,__nested_webpack_require_110408__){"use strict";__nested_webpack_require_110408__.r(__webpack_exports__),function(Buffer){__nested_webpack_require_110408__.d(__webpack_exports__,"MessageWriter",(function(){return MessageWriter}));var int53__WEBPACK_IMPORTED_MODULE_0__=__nested_webpack_require_110408__("./node_modules/int53/index.js"),int53__WEBPACK_IMPORTED_MODULE_0___default=__nested_webpack_require_110408__.n(int53__WEBPACK_IMPORTED_MODULE_0__);function _defineProperty(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function writeTime(e,t,r){t.writeUInt32LE(e.sec,r),t.writeUInt32LE(e.nsec,r+4)}class StandardTypeOffsetCalculator{constructor(){_defineProperty(this,"offset",0)}_incrementAndReturn(e){const t=this.offset;return this.offset+=e,t}json(e){return this.string(JSON.stringify(e))}string(e){const t=4+e.length;return this._incrementAndReturn(t)}bool(){return this.uint8()}int8(){return this._incrementAndReturn(1)}uint8(){return this._incrementAndReturn(1)}int16(){return this._incrementAndReturn(2)}uint16(){return this._incrementAndReturn(2)}int32(){return this._incrementAndReturn(4)}uint32(){return this._incrementAndReturn(4)}float32(){return this._incrementAndReturn(4)}float64(){return this._incrementAndReturn(8)}int64(){return this._incrementAndReturn(8)}uint64(){return this._incrementAndReturn(8)}time(){return this._incrementAndReturn(8)}duration(){return this._incrementAndReturn(8)}}class StandardTypeWriter{constructor(e){_defineProperty(this,"buffer",void 0),_defineProperty(this,"view",void 0),_defineProperty(this,"offsetCalculator",void 0),this.buffer=e,this.view=new DataView(e.buffer,e.byteOffset),this.offsetCalculator=new StandardTypeOffsetCalculator}json(e){this.string(JSON.stringify(e))}string(e){const t=this.offsetCalculator.string(e);this.view.setInt32(t,e.length,!0),this.buffer.write(e,t+4,e.length,"ascii")}bool(e){this.uint8(e?1:0)}int8(e){this.view.setInt8(this.offsetCalculator.int8(),e)}uint8(e){this.view.setUint8(this.offsetCalculator.uint8(),e)}int16(e){this.view.setInt16(this.offsetCalculator.int16(),e,!0)}uint16(e){this.view.setUint16(this.offsetCalculator.uint16(),e,!0)}int32(e){this.view.setInt32(this.offsetCalculator.int32(),e,!0)}uint32(e){this.view.setUint32(this.offsetCalculator.uint32(),e,!0)}float32(e){this.view.setFloat32(this.offsetCalculator.float32(),e,!0)}float64(e){this.view.setFloat64(this.offsetCalculator.float64(),e,!0)}int64(e){int53__WEBPACK_IMPORTED_MODULE_0___default.a.writeInt64LE(e,this.buffer,this.offsetCalculator.int64())}uint64(e){int53__WEBPACK_IMPORTED_MODULE_0___default.a.writeUInt64LE(e,this.buffer,this.offsetCalculator.uint64())}time(e){writeTime(e,this.buffer,this.offsetCalculator.time())}duration(e){writeTime(e,this.buffer,this.offsetCalculator.time())}}const findTypeByName=(e,t="")=>{let r="";const n=e.filter((e=>{const n=e.name||"";if(!t)return!n;const i=t.indexOf("/")>-1?t:`/${t}`;return!!n.endsWith(i)&&(r=n,!0)}));if(1!==n.length)throw new Error(`Expected 1 top level type definition for '${t}' but found ${n.length}.`);return{...n[0],name:r}},friendlyName=e=>e.replace(/\//g,"_");function createWriterAndSizeCalculator(types){const unnamedTypes=types.filter((e=>!e.name));if(1!==unnamedTypes.length)throw new Error("multiple unnamed types");const[unnamedType]=unnamedTypes,namedTypes=types.filter((e=>!!e.name)),constructorBody=(e,t)=>{const r=[];return e.definitions.forEach((e=>{if(e.isConstant)return;const n=`message["${e.name}"]`;if(e.isArray){const i=`length_${e.name}`;if(e.arrayLength?r.push(`var ${i} = ${e.arrayLength};`):(r.push(`var ${i} = ${n}.length;`),r.push(`${t}.uint32(${i});`)),r.push(`for (var i = 0; i < ${i}; i++) {`),e.isComplex){const i=findTypeByName(types,e.type);r.push(`  ${friendlyName(i.name)}(${t}, ${n}[i]);`)}else r.push(`  ${t}.${e.type}(${n}[i]);`);r.push("}")}else if(e.isComplex){const i=findTypeByName(types,e.type);r.push(`${friendlyName(i.name)}(${t}, ${n});`)}else r.push(`${t}.${e.type}(${n});`)})),r.join("\n    ")};let writerJs="",calculateSizeJs="",_write,_calculateSize;namedTypes.forEach((e=>{writerJs+=`\n  function ${friendlyName(e.name)}(writer, message) {\n    ${constructorBody(e,"writer")}\n  };\n`,calculateSizeJs+=`\n  function ${friendlyName(e.name)}(offsetCalculator, message) {\n    ${constructorBody(e,"offsetCalculator")}\n  };\n`})),writerJs+=`\n  return function write(writer, message) {\n    ${constructorBody(unnamedType,"writer")}\n    return writer.buffer;\n  };`,calculateSizeJs+=`\n  return function calculateSize(offsetCalculator, message) {\n    ${constructorBody(unnamedType,"offsetCalculator")}\n    return offsetCalculator.offset;\n  };`;try{_write=eval(`(function buildWriter() { ${writerJs} })()`)}catch(e){throw console.error("error building writer:",writerJs),e}try{_calculateSize=eval(`(function buildSizeCalculator() { ${calculateSizeJs} })()`)}catch(e){throw console.error("error building size calculator:",calculateSizeJs),e}return{writer:function(e,t){const r=new StandardTypeWriter(t);return _write(r,e)},bufferSizeCalculator(e){const t=new StandardTypeOffsetCalculator;return _calculateSize(t,e)}}}class MessageWriter{constructor(e){_defineProperty(this,"writer",void 0),_defineProperty(this,"bufferSizeCalculator",void 0);const{writer:t,bufferSizeCalculator:r}=createWriterAndSizeCalculator(e);this.writer=t,this.bufferSizeCalculator=r}calculateBufferSize(e){return this.bufferSizeCalculator(e)}writeMessage(e,t){let r=t;if(!r){const t=this.calculateBufferSize(e);r=Buffer.allocUnsafe(t)}return this.writer(e,r)}}}.call(this,__nested_webpack_require_110408__("./node_modules/node-libs-browser/node_modules/buffer/index.js").Buffer)},"./src/ReadResult.js":function(e,t,r){"use strict";function n(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}r.r(t),r.d(t,"default",(function(){return i}));class i{constructor(e,t,r,i,o,s,a){n(this,"topic",void 0),n(this,"message",void 0),n(this,"timestamp",void 0),n(this,"data",void 0),n(this,"chunkOffset",void 0),n(this,"totalChunks",void 0),this.topic=e,this.message=t,this.timestamp=r,this.data=i,this.chunkOffset=o,this.totalChunks=s,a&&(Object.freeze(r),Object.freeze(this))}}},"./src/TimeUtil.js":function(e,t,r){"use strict";function n(e){return{sec:Math.floor(e.getTime()/1e3),nsec:1e6*e.getMilliseconds()}}function i(e){return new Date(1e3*e.sec+e.nsec/1e6)}function o(e,t){return e.sec-t.sec||e.nsec-t.nsec}function s(e,t){return this.compare(e,t)<0}function a(e,t){return this.compare(e,t)>0}function u(e,t){return e.sec===t.sec&&e.nsec===t.nsec}function f(e){return`{${e.sec}, ${e.nsec}}`}function c(e,t){const r=e.nsec+t.nsec,n=Math.floor(r/1e9),i=r%1e9,o={sec:e.sec+t.sec+n,nsec:Math.abs(-1===Math.sign(i)?1e9+i:i)};if(o.sec<0||o.nsec<0)throw new Error(`Invalid time: ${f(o)} produced from TimeUtil.add(${f(e)}, ${f(t)}})`);return o}r.r(t),r.d(t,"fromDate",(function(){return n})),r.d(t,"toDate",(function(){return i})),r.d(t,"compare",(function(){return o})),r.d(t,"isLessThan",(function(){return s})),r.d(t,"isGreaterThan",(function(){return a})),r.d(t,"areSame",(function(){return u})),r.d(t,"add",(function(){return c}))},"./src/bag.js":function(e,t,r){"use strict";r.r(t),r.d(t,"default",(function(){return u})),r("./src/BagReader.js");var n=r("./src/MessageReader.js"),i=r("./src/ReadResult.js"),o=(r("./src/record.js"),r("./src/TimeUtil.js")),s=r("./src/parseMessageDefinition.js");function a(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class u{constructor(e){a(this,"reader",void 0),a(this,"header",void 0),a(this,"connections",void 0),a(this,"chunkInfos",void 0),a(this,"startTime",void 0),a(this,"endTime",void 0),this.reader=e}async open(){this.header=await this.reader.readHeaderAsync();const{connectionCount:e,chunkCount:t,indexPosition:r}=this.header,n=await this.reader.readConnectionsAndChunkInfoAsync(r,e,t);this.connections={},n.connections.forEach((e=>{this.connections[e.conn]=e})),this.chunkInfos=n.chunkInfos,t>0&&(this.startTime=this.chunkInfos[0].startTime,this.endTime=this.chunkInfos[t-1].endTime)}async readMessages(e,t){const r=this.connections,a=e.startTime||{sec:0,nsec:0},u=e.endTime||{sec:Number.MAX_VALUE,nsec:Number.MAX_VALUE},f=e.topics||Object.keys(r).map((e=>r[e].topic)),c=Object.keys(r).filter((e=>-1!==f.indexOf(r[e].topic))).map((e=>+e)),{decompress:h={}}=e,l=this.chunkInfos.filter((e=>o.compare(e.startTime,u)<=0&&o.compare(a,e.endTime)<=0));function d(t,o){const a=r[t.conn],{topic:u}=a,{data:f,time:c}=t;let h=null;return e.noParse||(a.reader=a.reader||new n.MessageReader(Object(s.parseMessageDefinition)(a.messageDefinition),{freeze:e.freeze}),h=a.reader.readMessage(f)),new i.default(u,h,c,f,o,l.length,e.freeze)}for(let e=0;e<l.length;e++){const r=l[e];(await this.reader.readChunkMessagesAsync(r,c,a,u,h)).forEach((r=>t(d(r,e))))}}}a(u,"open",(e=>{throw new Error("This method should have been overridden based on the environment. Make sure you are correctly importing the node or web version of Bag.")}))},"./src/fields.js":function(e,t,r){"use strict";r.r(t),r.d(t,"extractFields",(function(){return i})),r.d(t,"extractTime",(function(){return o}));const n="=".charCodeAt(0);function i(e){if(e.length<4)throw new Error("Header fields are truncated.");let t=0;const r={};for(;t<e.length;){const i=e.readInt32LE(t);if(t+=4,t+i>e.length)throw new Error("Header fields are corrupt.");const o=e.slice(t,t+i),s=o.indexOf(n);if(-1===s)throw new Error("Header field is missing equals sign.");r[o.slice(0,s).toString()]=o.slice(s+1),t+=i}return r}function o(e,t){return{sec:e.readUInt32LE(t),nsec:e.readUInt32LE(t+4)}}},"./src/header.js":function(e,t,r){"use strict";r.r(t),r.d(t,"parseHeader",(function(){return i}));var n=r("./src/fields.js");function i(e,t){const r=Object(n.extractFields)(e);if(void 0===r.op)throw new Error("Header is missing 'op' field.");const i=r.op.readUInt8(0);if(i!==t.opcode)throw new Error(`Expected ${t.name} (${t.opcode}) but found ${i}`);return new t(r)}r("./src/record.js")},"./src/index.js":function(e,t,r){"use strict";r.r(t);var n=r("./src/TimeUtil.js");r.d(t,"TimeUtil",(function(){return n})),r("./src/bag.js"),r("./src/BagReader.js");var i=r("./src/MessageReader.js");r.d(t,"MessageReader",(function(){return i.MessageReader}));var o=r("./src/MessageWriter.js");r.d(t,"MessageWriter",(function(){return o.MessageWriter}));var s=r("./src/parseMessageDefinition.js");r.d(t,"rosPrimitiveTypes",(function(){return s.rosPrimitiveTypes})),r.d(t,"parseMessageDefinition",(function(){return s.parseMessageDefinition}));var a=r("./src/types.js");for(var u in a)["TimeUtil","default","MessageReader","MessageWriter","rosPrimitiveTypes","parseMessageDefinition","default"].indexOf(u)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(u);var f=r("./src/fields.js");r.d(t,"extractFields",(function(){return f.extractFields})),r.d(t,"extractTime",(function(){return f.extractTime}))},"./src/nmerge.js":function(e,t,r){"use strict";r.r(t);var n=r("./node_modules/heap/index.js"),i=r.n(n);t.default=function(e,...t){const r=new i.a(((t,r)=>e(t.value,r.value)));for(let e=0;e<t.length;e++){const{value:n,done:i}=t[e].next();i||r.push({i:e,value:n})}return{next:()=>{if(r.empty())return{done:!0};const{i:e}=r.front(),n=t[e].next();return n.done?{value:r.pop().value,done:!1}:{value:r.replace({i:e,value:n.value}).value,done:!1}}}}},"./src/parseMessageDefinition.js":function(e,t,r){"use strict";r.r(t),r.d(t,"rosPrimitiveTypes",(function(){return n})),r.d(t,"parseMessageDefinition",(function(){return s}));const n=new Set(["string","bool","int8","uint8","int16","uint16","int32","uint32","float32","float64","int64","uint64","time","duration","json"]);function i(e){let t=e;return"char"===e&&(t="uint8"),"byte"===e&&(t="int8"),t}const o=e=>{const t=[];let r;return e.forEach((({isJson:e,line:o})=>{const s=o.replace(/#.*/gi,"").split(" ").filter((e=>e));if(!s[1])return;const a=s[0].trim(),u=s[1].trim();if("MSG:"===a)r=u;else if(u.indexOf("=")>-1||s.indexOf("=")>-1){const e=o.match(/(\S+)\s*=\s*(.*)\s*/);if(!e)throw new Error("Malformed line: "+o);let r=e[2];if("string"!==a){r=r.replace(/True/gi,"true"),r=r.replace(/False/gi,"false");try{r=JSON.parse(r.replace(/\s*#.*/g,""))}catch(e){throw console.warn(`Error in this constant definition: ${o}`),e}"bool"===a&&(r=Boolean(r))}(a.includes("int")&&r>Number.MAX_SAFE_INTEGER||r<Number.MIN_SAFE_INTEGER)&&console.warn(`Found integer constant outside safe integer range: ${o}`),t.push({type:i(a),name:e[1],isConstant:!0,value:r})}else if(a.indexOf("]")===a.length-1){const e=a.split("["),r=e[0],o=e[1].replace("]","");t.push(function(e,t,r){const o=i(e);return{type:o,name:t,isArray:!0,arrayLength:null===r?void 0:r,isComplex:!n.has(o)}}(r,u,o?parseInt(o,10):void 0))}else t.push(function(e,t){const r=i(e);return{type:r,name:t,isArray:!1,isComplex:!n.has(r)}}(e?"json":a,u))})),{name:r,definitions:t}};function s(e){const t=e.split("\n").map((e=>e.trim())).filter((e=>e));let r=[];const n=[];let i=!1;return t.forEach((e=>{e.startsWith("#")?e.startsWith("#pragma rosbag_parse_json")&&(i=!0):e.startsWith("==")?(i=!1,n.push(o(r)),r=[]):(r.push({isJson:i,line:e}),i=!1)})),n.push(o(r)),n.forEach((({definitions:e})=>{e.forEach((e=>{if(e.isComplex){const t=((e,t)=>{const r=e.filter((e=>{const r=e.name||"";if(!t)return!r;const n=t.indexOf("/")>-1?t:`/${t}`;return r.endsWith(n)}));if(1!==r.length)throw new Error(`Expected 1 top level type definition for '${t}' but found ${r.length}`);return r[0]})(n,e.type).name;if(void 0===t)throw new Error(`Missing type definition for ${e.type}`);e.type=t}}))})),n}},"./src/record.js":function(e,t,r){"use strict";r.r(t),r.d(t,"Record",(function(){return u})),r.d(t,"BagHeader",(function(){return f})),r.d(t,"Chunk",(function(){return c})),r.d(t,"Connection",(function(){return l})),r.d(t,"MessageData",(function(){return d})),r.d(t,"IndexData",(function(){return p})),r.d(t,"ChunkInfo",(function(){return _}));var n=r("./node_modules/int53/index.js"),i=r.n(n),o=r("./src/fields.js");function s(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}r("./src/MessageReader.js");const a=e=>i.a.readUInt64LE(e,0);class u{constructor(e){s(this,"offset",void 0),s(this,"dataOffset",void 0),s(this,"end",void 0),s(this,"length",void 0)}parseData(e){}}class f extends u{constructor(e){super(e),s(this,"indexPosition",void 0),s(this,"connectionCount",void 0),s(this,"chunkCount",void 0),this.indexPosition=a(e.index_pos),this.connectionCount=e.conn_count.readInt32LE(0),this.chunkCount=e.chunk_count.readInt32LE(0)}}s(f,"opcode",3);class c extends u{constructor(e){super(e),s(this,"compression",void 0),s(this,"size",void 0),s(this,"data",void 0),this.compression=e.compression.toString(),this.size=e.size.readUInt32LE(0)}parseData(e){this.data=e}}s(c,"opcode",5);const h=(e,t)=>{if(void 0===e[t])throw new Error(`Connection header is missing ${t}.`);return e[t].toString()};class l extends u{constructor(e){super(e),s(this,"conn",void 0),s(this,"topic",void 0),s(this,"type",void 0),s(this,"md5sum",void 0),s(this,"messageDefinition",void 0),s(this,"callerid",void 0),s(this,"latching",void 0),s(this,"reader",void 0),this.conn=e.conn.readUInt32LE(0),this.topic=e.topic.toString(),this.type=void 0,this.md5sum=void 0,this.messageDefinition=""}parseData(e){const t=Object(o.extractFields)(e);this.type=h(t,"type"),this.md5sum=h(t,"md5sum"),this.messageDefinition=h(t,"message_definition"),void 0!==t.callerid&&(this.callerid=t.callerid.toString()),void 0!==t.latching&&(this.latching="1"===t.latching.toString())}}s(l,"opcode",7);class d extends u{constructor(e){super(e),s(this,"conn",void 0),s(this,"time",void 0),s(this,"data",void 0),this.conn=e.conn.readUInt32LE(0),this.time=Object(o.extractTime)(e.time,0)}parseData(e){this.data=e}}s(d,"opcode",2);class p extends u{constructor(e){super(e),s(this,"ver",void 0),s(this,"conn",void 0),s(this,"count",void 0),s(this,"indices",void 0),this.ver=e.ver.readUInt32LE(0),this.conn=e.conn.readUInt32LE(0),this.count=e.count.readUInt32LE(0)}parseData(e){this.indices=[];for(let t=0;t<this.count;t++)this.indices.push({time:Object(o.extractTime)(e,12*t),offset:e.readUInt32LE(12*t+8)})}}s(p,"opcode",4);class _ extends u{constructor(e){super(e),s(this,"ver",void 0),s(this,"chunkPosition",void 0),s(this,"startTime",void 0),s(this,"endTime",void 0),s(this,"count",void 0),s(this,"connections",void 0),s(this,"nextChunk",void 0),this.ver=e.ver.readUInt32LE(0),this.chunkPosition=a(e.chunk_pos),this.startTime=Object(o.extractTime)(e.start_time,0),this.endTime=Object(o.extractTime)(e.end_time,0),this.count=e.count.readUInt32LE(0)}parseData(e){this.connections=[];for(let t=0;t<this.count;t++)this.connections.push({conn:e.readUInt32LE(8*t),count:e.readUInt32LE(8*t+4)})}}s(_,"opcode",6)},"./src/types.js":function(e,t){},"./src/web/index.js":function(e,t,r){"use strict";r.r(t),function(e){r.d(t,"Reader",(function(){return c})),r.d(t,"open",(function(){return h}));var n=r("./node_modules/node-libs-browser/node_modules/buffer/index.js"),i=r("./src/index.js");r.d(t,"TimeUtil",(function(){return i.TimeUtil})),r.d(t,"MessageReader",(function(){return i.MessageReader})),r.d(t,"MessageWriter",(function(){return i.MessageWriter})),r.d(t,"parseMessageDefinition",(function(){return i.parseMessageDefinition})),r.d(t,"rosPrimitiveTypes",(function(){return i.rosPrimitiveTypes})),r.d(t,"extractFields",(function(){return i.extractFields})),r.d(t,"extractTime",(function(){return i.extractTime}));var o=r("./src/bag.js"),s=r("./src/BagReader.js");r.d(t,"BagReader",(function(){return s.default}));var a=r("./src/types.js");for(var u in a)["Reader","TimeUtil","BagReader","MessageReader","MessageWriter","open","parseMessageDefinition","rosPrimitiveTypes","extractFields","extractTime","default"].indexOf(u)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(u);function f(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class c{constructor(e){f(this,"_blob",void 0),f(this,"_size",void 0),this._blob=e,this._size=e.size}read(t,r,i){const o=new FileReader;o.onload=function(){o.onload=null,o.onerror=null,e(i,null,n.Buffer.from(o.result))},o.onerror=function(){o.onload=null,o.onerror=null,e(i,new Error(o.error))},o.readAsArrayBuffer(this._blob.slice(t,t+r))}size(){return this._size}}const h=async e=>{if(!(e instanceof Blob))throw new Error("Expected file to be a File or Blob. Make sure you are correctly importing the node or web version of Bag.");const t=new o.default(new s.default(new c(e)));return await t.open(),t};o.default.open=h,t.default=o.default}.call(this,r("./node_modules/timers-browserify/main.js").setImmediate)}})},module.exports=factory()}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var r=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e].call(r.exports,r,r.exports,__webpack_require__),r.exports}var __webpack_exports__={};(()=>{const e=__webpack_require__(556);window.rosbag=e})()})();