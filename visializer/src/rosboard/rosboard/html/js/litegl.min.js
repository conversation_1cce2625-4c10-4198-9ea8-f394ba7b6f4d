'use strict';(function(t){function V(){return Array.prototype.slice.call(this)}function R(h,e){if(h.constructor===e)return h;if(h.constructor!==Array)return e=e||Float32Array,new e(h);e=e||Float32Array;for(var a=h[0].length,b=new e(h.length*a),c=0;c<h.length;++c)for(var d=0;d<a;++d)b[c*a+d]=h[c][d];return b}function K(h,e,a,b,c){h=h||1024;g.debug&&console.log("GL.Mesh created");null!==c&&(this.gl=c=c||t.gl);this._context_id=c.context_id;this.vertexBuffers={};this.indexBuffers={};this.info={groups:[]};
this._bounding=BBox.create();this.resize(h)}function B(h,e,a,b){this.gl=b=b||t.gl;this._context_id=b.context_id;if(h&&h.constructor!==Array)throw"FBO textures must be an Array";this.handler=null;this.height=this.width=-1;this.color_textures=[];this.depth_texture=null;this.stencil=!!a;this._stencil_enabled=!1;this._num_binded_textures=0;this.order=null;(h&&h.length||e)&&this.setTextures(h,e);this._old_fbo_handler=null;this._old_viewport=new Float32Array(4)}var g=t.GL=t.LiteGL={};if("undefined"==typeof glMatrix)throw"litegl.js requires gl-matrix to work. It must be included before litegl.";
if(!t.vec2)throw"litegl.js does not support gl-matrix 3.0, download 2.8 https://github.com/toji/gl-matrix/releases/tag/v2.8.1";t.requestAnimationFrame=t.requestAnimationFrame||t.mozRequestAnimationFrame||t.webkitRequestAnimationFrame||function(h){setTimeout(h,1E3/60)};g.blockable_keys={Up:!0,Down:!0,Left:!0,Right:!0};g.reverse=null;g.LEFT_MOUSE_BUTTON=0;g.MIDDLE_MOUSE_BUTTON=1;g.RIGHT_MOUSE_BUTTON=2;g.LEFT_MOUSE_BUTTON_MASK=1;g.RIGHT_MOUSE_BUTTON_MASK=2;g.MIDDLE_MOUSE_BUTTON_MASK=4;g.last_context_id=
0;g.COLOR_BUFFER_BIT=16384;g.DEPTH_BUFFER_BIT=256;g.STENCIL_BUFFER_BIT=1024;g.TEXTURE_2D=3553;g.TEXTURE_CUBE_MAP=34067;g.TEXTURE_3D=32879;g.TEXTURE_MAG_FILTER=10240;g.TEXTURE_MIN_FILTER=10241;g.TEXTURE_WRAP_S=10242;g.TEXTURE_WRAP_T=10243;g.BYTE=5120;g.UNSIGNED_BYTE=5121;g.SHORT=5122;g.UNSIGNED_SHORT=5123;g.INT=5124;g.UNSIGNED_INT=5125;g.FLOAT=5126;g.HALF_FLOAT_OES=36193;g.HALF_FLOAT=5131;g.DEPTH_COMPONENT16=33189;g.DEPTH_COMPONENT24=33190;g.DEPTH_COMPONENT32F=36012;g.FLOAT_VEC2=35664;g.FLOAT_VEC3=
35665;g.FLOAT_VEC4=35666;g.INT_VEC2=35667;g.INT_VEC3=35668;g.INT_VEC4=35669;g.BOOL=35670;g.BOOL_VEC2=35671;g.BOOL_VEC3=35672;g.BOOL_VEC4=35673;g.FLOAT_MAT2=35674;g.FLOAT_MAT3=35675;g.FLOAT_MAT4=35676;g.TYPE_LENGTH={};g.TYPE_LENGTH[g.FLOAT]=g.TYPE_LENGTH[g.INT]=g.TYPE_LENGTH[g.BYTE]=g.TYPE_LENGTH[g.BOOL]=1;g.TYPE_LENGTH[g.FLOAT_VEC2]=g.TYPE_LENGTH[g.INT_VEC2]=g.TYPE_LENGTH[g.BOOL_VEC2]=2;g.TYPE_LENGTH[g.FLOAT_VEC3]=g.TYPE_LENGTH[g.INT_VEC3]=g.TYPE_LENGTH[g.BOOL_VEC3]=3;g.TYPE_LENGTH[g.FLOAT_VEC4]=
g.TYPE_LENGTH[g.INT_VEC4]=g.TYPE_LENGTH[g.BOOL_VEC4]=4;g.TYPE_LENGTH[g.FLOAT_MAT3]=9;g.TYPE_LENGTH[g.FLOAT_MAT4]=16;g.SAMPLER_2D=35678;g.SAMPLER_3D=35679;g.SAMPLER_CUBE=35680;g.INT_SAMPLER_2D=36298;g.INT_SAMPLER_3D=36299;g.INT_SAMPLER_CUBE=36300;g.UNSIGNED_INT_SAMPLER_2D=36306;g.UNSIGNED_INT_SAMPLER_3D=36307;g.UNSIGNED_INT_SAMPLER_CUBE=36308;g.DEPTH_COMPONENT=6402;g.ALPHA=6406;g.RGB=6407;g.RGBA=6408;g.LUMINANCE=6409;g.LUMINANCE_ALPHA=6410;g.DEPTH_STENCIL=34041;g.UNSIGNED_INT_24_8_WEBGL=34042;g.R8=
33321;g.R16F=33325;g.R32F=33326;g.R8UI=33330;g.RG8=33323;g.RG16F=33327;g.RG32F=33328;g.RGB8=32849;g.SRGB8=35905;g.RGB565=36194;g.R11F_G11F_B10F=35898;g.RGB9_E5=35901;g.RGB16F=34843;g.RGB32F=34837;g.RGB8UI=36221;g.RGBA8=32856;g.RGB5_A1=32855;g.RGBA16F=34842;g.RGBA32F=34836;g.RGBA8UI=36220;g.RGBA16I=36232;g.RGBA16UI=36214;g.RGBA32I=36226;g.RGBA32UI=36208;g.NEAREST=9728;g.LINEAR=9729;g.NEAREST_MIPMAP_NEAREST=9984;g.LINEAR_MIPMAP_NEAREST=9985;g.NEAREST_MIPMAP_LINEAR=9986;g.LINEAR_MIPMAP_LINEAR=9987;g.REPEAT=
10497;g.CLAMP_TO_EDGE=33071;g.MIRRORED_REPEAT=33648;g.ZERO=0;g.ONE=1;g.SRC_COLOR=768;g.ONE_MINUS_SRC_COLOR=769;g.SRC_ALPHA=770;g.ONE_MINUS_SRC_ALPHA=771;g.DST_ALPHA=772;g.ONE_MINUS_DST_ALPHA=773;g.DST_COLOR=774;g.ONE_MINUS_DST_COLOR=775;g.SRC_ALPHA_SATURATE=776;g.CONSTANT_COLOR=32769;g.ONE_MINUS_CONSTANT_COLOR=32770;g.CONSTANT_ALPHA=32771;g.ONE_MINUS_CONSTANT_ALPHA=32772;g.VERTEX_SHADER=35633;g.FRAGMENT_SHADER=35632;g.FRONT=1028;g.BACK=1029;g.FRONT_AND_BACK=1032;g.NEVER=512;g.LESS=513;g.EQUAL=514;
g.LEQUAL=515;g.GREATER=516;g.NOTEQUAL=517;g.GEQUAL=518;g.ALWAYS=519;g.KEEP=7680;g.REPLACE=7681;g.INCR=7682;g.DECR=7683;g.INCR_WRAP=34055;g.DECR_WRAP=34056;g.INVERT=5386;g.STREAM_DRAW=35040;g.STATIC_DRAW=35044;g.DYNAMIC_DRAW=35048;g.ARRAY_BUFFER=34962;g.ELEMENT_ARRAY_BUFFER=34963;g.POINTS=0;g.LINES=1;g.LINE_LOOP=2;g.LINE_STRIP=3;g.TRIANGLES=4;g.TRIANGLE_STRIP=5;g.TRIANGLE_FAN=6;g.CW=2304;g.CCW=2305;g.CULL_FACE=2884;g.DEPTH_TEST=2929;g.BLEND=3042;g.temp_vec3=vec3.create();g.temp2_vec3=vec3.create();
g.temp_vec4=vec4.create();g.temp_quat=quat.create();g.temp_mat3=mat3.create();g.temp_mat4=mat4.create();t.DEG2RAD=0.0174532925;t.RAD2DEG=57.295779578552306;t.EPSILON=1E-6;t.isPowerOfTwo=g.isPowerOfTwo=function(h){return 0==Math.log(h)/Math.log(2)%1};t.nearestPowerOfTwo=g.nearestPowerOfTwo=function(h){return Math.pow(2,Math.round(Math.log(h)/Math.log(2)))};t.nextPowerOfTwo=g.nextPowerOfTwo=function(h){return Math.pow(2,Math.ceil(Math.log(h)/Math.log(2)))};[Uint8Array,Int8Array,Uint16Array,Int16Array,
Uint32Array,Int32Array,Float32Array,Float64Array].forEach(function(h){h.prototype.toJSON||Object.defineProperty(h.prototype,"toJSON",{value:V,enumerable:!1,configurable:!0,writable:!0})});t.getTime="undefined"!=typeof performance?performance.now.bind(performance):Date.now.bind(Date);g.getTime=t.getTime;t.isFunction=function(h){return!!(h&&h.constructor&&h.call&&h.apply)};t.isArray=function(h){return h&&h.constructor===Array};t.isNumber=function(h){return null!=h&&h.constructor===Number};t.getClassName=
function(h){if(h){if(h.name)return h.name;if(h.toString&&(h=h.toString().match(/function\s*(\w+)/))&&2==h.length)return h[1]}};t.cloneObject=g.cloneObject=function(h,e){if(h.constructor!==Object)throw"cloneObject only can clone pure javascript objects, not classes";e=e||{};for(var a in h){var b=h[a];if(null===b)e[a]=null;else switch(b.constructor){case Int8Array:case Uint8Array:case Int16Array:case Uint16Array:case Int32Array:case Uint32Array:case Float32Array:case Float64Array:e[a]=new b.constructor(b);
break;case Boolean:case Number:case String:e[a]=b;break;case Array:e[a]=b.concat();break;case Object:e[a]=g.cloneObject(b)}}return e};t.regexMap=function(h,e,a){for(var b;null!=(b=h.exec(e));)a(b)};t.createCanvas=g.createCanvas=function(h,e){var a=document.createElement("canvas");a.width=h;a.height=e;return a};t.cloneCanvas=g.cloneCanvas=function(h){var e=document.createElement("canvas");e.width=h.width;e.height=h.height;e.getContext("2d").drawImage(h,0,0);return e};"undefined"!=typeof Image&&(Image.prototype.getPixels=
function(){var h=document.createElement("canvas");h.width=this.width;h.height=this.height;h=h.getContext("2d");h.drawImage(this,0,0);return h.getImageData(0,0,this.width,this.height).data});String.prototype.hasOwnProperty("replaceAll")||Object.defineProperty(String.prototype,"replaceAll",{value:function(h){var e=this,a;for(a in h)e=e.split(a).join(h[a]);return e},enumerable:!1});String.prototype.hasOwnProperty("hashCode")||Object.defineProperty(String.prototype,"hashCode",{value:function(){var h=
0,e,a,b;if(0==this.length)return h;e=0;for(b=this.length;e<b;++e)a=this.charCodeAt(e),h=(h<<5)-h+a,h|=0;return h},enumerable:!1});Array.prototype.hasOwnProperty("clone")||Object.defineProperty(Array.prototype,"clone",{value:Array.prototype.concat,enumerable:!1});Float32Array.prototype.hasOwnProperty("clone")||Object.defineProperty(Float32Array.prototype,"clone",{value:function(){return new Float32Array(this)},enumerable:!1});t.wipeObject=function(h){for(var e in h)h.hasOwnProperty(e)&&delete h[e]};
t.extendClass=g.extendClass=function(h,e){for(var a in e)h.hasOwnProperty(a)||(h[a]=e[a]);if(e.prototype){var b=Object.getOwnPropertyNames(e.prototype);for(a=0;a<b.length;++a){var c=b[a];h.prototype.hasOwnProperty(c)||(e.prototype.__lookupGetter__(c)?h.prototype.__defineGetter__(c,e.prototype.__lookupGetter__(c)):h.prototype[c]=e.prototype[c],e.prototype.__lookupSetter__(c)&&h.prototype.__defineSetter__(c,e.prototype.__lookupSetter__(c)))}}h.hasOwnProperty("superclass")||Object.defineProperty(h,"superclass",
{get:function(){return e},enumerable:!1})};t.HttpRequest=g.request=function(h,e,a,b,c){var d=!0;c=c||{};void 0!==c.async&&(d=c.async);if(e){var f=null,f=[],k;for(k in e)f.push(k+"="+e[k]);f=f.join("&");h=h+"?"+f}var g=new XMLHttpRequest;g.open("GET",h,d);g.responseType=c.responseType||"text";g.onload=function(c){this.getResponseHeader("Content-Type");200!=this.status?(y.trigger(g,"fail",this.status),b&&b(this.status)):(y.trigger(g,"done",this.response),a&&a(this.response))};g.onerror=function(a){y.trigger(g,
"fail",a)};if(c){for(k in c)g[k]=c[k];c.binary&&(g.responseType="arraybuffer")}g.send();return g};t.XMLHttpRequest&&(XMLHttpRequest.prototype.hasOwnProperty("done")||Object.defineProperty(XMLHttpRequest.prototype,"done",{enumerable:!1,value:function(h){y.bind(this,"done",function(e,a){h(a)});return this}}),XMLHttpRequest.prototype.hasOwnProperty("fail")||Object.defineProperty(XMLHttpRequest.prototype,"fail",{enumerable:!1,value:function(h){y.bind(this,"fail",function(e,a){h(a)});return this}}));t.getFileExtension=
function(h){var e=h.indexOf("?");-1!=e&&(h=h.substr(0,e));e=h.lastIndexOf(".");return-1==e?"":h.substr(e+1).toLowerCase()};t.loadFileAtlas=g.loadFileAtlas=function(h,e,a){var b=null;HttpRequest(h,null,function(a){a=g.processFileAtlas(a);e&&e(a);b&&b(a)},alert,a);return{done:function(a){b=a}}};t.processFileAtlas=g.processFileAtlas=function(h,e){for(var a=h.split("\n"),b={},c=[],d="",f=0,k=a.length;f<k;f++){var g=e?a[f]:a[f].trim();g.length&&("\\"!=g[0]?c.push(g):(c.length&&(b[d]=c.join("\n")),c.length=
0,d=g.substr(1)))}c.length&&(b[d]=c.join("\n"));return b};t.typedArrayToArray=function(h){var e=[];e.length=h.length;for(var a=0;a<h.length;a++)e[a]=h[a];return e};t.RGBToHex=function(h,e,a){h=Math.min(255,255*h)|0;e=Math.min(255,255*e)|0;a=Math.min(255,255*a)|0;return"#"+(16777216+(h<<16)+(e<<8)+a).toString(16).slice(1)};t.HUEToRGB=function(h,e,a){0>a&&(a+=1);1<a&&(a-=1);return a<1/6?h+6*(e-h)*a:0.5>a?e:a<2/3?h+(e-h)*(2/3-a)*6:h};t.HSLToRGB=function(h,e,a,b){b=b||vec3.create();if(0==e)a=e=h=a;else{var c=
0.5>a?a*(1+e):a+e-a*e,d=2*a-c;a=HUEToRGB(d,c,h+1/3);e=HUEToRGB(d,c,h);h=HUEToRGB(d,c,h-1/3)}b[0]=a;b[1]=e;b[2]=h;return b};t.hexColorToRGBA=function(){var h={white:[1,1,1],black:[0,0,0],gray:[0.501960813999176,0.501960813999176,0.501960813999176],red:[1,0,0],orange:[1,0.6470588445663452,0],pink:[1,0.7529411911964417,0.7960784435272217],green:[0,0.501960813999176,0],lime:[0,1,0],blue:[0,0,1],violet:[0.9333333373069763,0.5098039507865906,0.9333333373069763],magenta:[1,0,1],cyan:[0,1,1],yellow:[1,1,
0],brown:[0.6470588445663452,0.16470588743686676,0.16470588743686676],silver:[0.7529411911964417,0.7529411911964417,0.7529411911964417],gold:[1,0.843137264251709,0],transparent:[0,0,0,0]};return function(e,a,b){b=void 0===b?1:b;a=a||new Float32Array(4);a[3]=b;if("string"!=typeof e)return a;var c=h[e];if(void 0!==c)return a.set(c),a[3]=3==a.length?b:a[3]*b,a;c=e.indexOf("rgba(");if(-1!=c)return e=e.substr(5,e.length-2),e=e.split(","),a[0]=parseInt(e[0])/255,a[1]=parseInt(e[1])/255,a[2]=parseInt(e[2])/
255,a[3]=parseFloat(e[3])*b,a;c=e.indexOf("hsla(");if(-1!=c)return e=e.substr(5,e.length-2),e=e.split(","),HSLToRGB(parseInt(e[0])/360,parseInt(e[1])/100,parseInt(e[2])/100,a),a[3]=parseFloat(e[3])*b,a;a[3]=b;c=e.indexOf("rgb(");if(-1!=c)return e=e.substr(4,e.length-2),e=e.split(","),a[0]=parseInt(e[0])/255,a[1]=parseInt(e[1])/255,a[2]=parseInt(e[2])/255,a;c=e.indexOf("hsl(");if(-1!=c)return e=e.substr(4,e.length-2),e=e.split(","),HSLToRGB(parseInt(e[0])/360,parseInt(e[1])/100,parseInt(e[2])/100,
a),a;e=e.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,function(a,b,c,e){return b+b+c+c+e+e});b=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);if(!b)return a;a[0]=parseInt(b[1],16)/255;a[1]=parseInt(b[2],16)/255;a[2]=parseInt(b[3],16)/255;return a}}();t.toHalfFloat=function(){var h=new Float32Array(1),e=new Int32Array(h.buffer);return function(a){h[0]=a;a=e[0];var b=a>>16&32768,c=(a&2147483647)+4096;if(1199570944<=c)return 1199570944<=(a&2147483647)?2139095040>c?b|31744:b|31744|(a&8388607)>>13:
b|31743;if(947912704<=c)return b|c-939524096>>13;if(855638016>c)return b;c=(a&2147483647)>>23;return b|(a&8388607|8388608)+(8388608>>>c-102)>>126-c}}();var S=function(){function h(a){return a.charCodeAt(0)+(a.charCodeAt(1)<<8)+(a.charCodeAt(2)<<16)+(a.charCodeAt(3)<<24)}function e(a,b,c,d){var e=new Uint16Array(4),f=new Uint16Array(c*d),h=0,k=0,l=0,g=k=h=0,q=0,m=0,n=0,u=c/4;d/=4;for(var p=0;p<d;p++)for(var r=0;r<u;r++)l=b+4*(p*u+r),e[0]=a[l],e[1]=a[l+1],h=e[0]&31,k=e[0]&2016,g=e[0]&63488,q=e[1]&31,
m=e[1]&2016,n=e[1]&63488,e[2]=5*h+3*q>>3|5*k+3*m>>3&2016|5*g+3*n>>3&63488,e[3]=5*q+3*h>>3|5*m+3*k>>3&2016|5*n+3*g>>3&63488,h=a[l+2],k=4*p*c+4*r,f[k]=e[h&3],f[k+1]=e[h>>2&3],f[k+2]=e[h>>4&3],f[k+3]=e[h>>6&3],k+=c,f[k]=e[h>>8&3],f[k+1]=e[h>>10&3],f[k+2]=e[h>>12&3],f[k+3]=e[h>>14],h=a[l+3],k+=c,f[k]=e[h&3],f[k+1]=e[h>>2&3],f[k+2]=e[h>>4&3],f[k+3]=e[h>>6&3],k+=c,f[k]=e[h>>8&3],f[k+1]=e[h>>10&3],f[k+2]=e[h>>12&3],f[k+3]=e[h>>14];return f}function a(a){for(var b=0,c=a.length,d=0;b<c;b+=4)d=a[b],a[b]=a[b+
2],a[b+2]=d}function b(b,c,d,h){var I=new Int32Array(d,0,r),A,D,E,H,G,L,N,O,y;if(I[v]!=f)return console.error("Invalid magic number in DDS header"),0;if(!I[z]&l)return console.error("Unsupported format, must contain a FourCC code"),0;A=I[C];switch(A){case m:D=8;E=c?c.COMPRESSED_RGB_S3TC_DXT1_EXT:null;break;case n:D=16;E=c?c.COMPRESSED_RGBA_S3TC_DXT3_EXT:null;break;case p:D=16;E=c?c.COMPRESSED_RGBA_S3TC_DXT5_EXT:null;break;default:D=4,A=null,E=b.RGBA}N=1;I[u]&k&&!1!==h&&(N=Math.max(1,I[M]));H=I[w];
G=I[x];L=I[s]+4;if(I[t+1]&g)for(y=0;6>y;++y)for(H=I[w],G=I[x],O=0;O<N;++O)A?(h=Math.max(4,H)/4*Math.max(4,G)/4*D,c=new Uint8Array(d,L,h),b.compressedTexImage2D(b.TEXTURE_CUBE_MAP_POSITIVE_X+y,O,E,H,G,0,c)):(b.pixelStorei(b.UNPACK_FLIP_Y_WEBGL,!1),h=H*G*D,c=new Uint8Array(d,L,h),a(c),b.texImage2D(b.TEXTURE_CUBE_MAP_POSITIVE_X+y,O,E,H,G,0,b.RGBA,b.UNSIGNED_BYTE,c)),L+=h,H*=0.5,G*=0.5;else if(c)for(b.pixelStorei(b.UNPACK_FLIP_Y_WEBGL,!0),O=0;O<N;++O)A?(h=Math.max(4,H)/4*Math.max(4,G)/4*D,c=new Uint8Array(d,
L,h),b.compressedTexImage2D(b.TEXTURE_2D,O,E,H,G,0,c)):(h=H*G*D,c=new Uint8Array(d,L,h),a(c),b.texImage2D(b.TEXTURE_2D,O,E,H,G,0,E,b.UNSIGNED_BYTE,c)),L+=h,H*=0.5,G*=0.5;else if(A==m)Math.max(4,H),Math.max(4,G),c=new Uint16Array(d),d=e(c,L/2,H,G),b.texImage2D(b.TEXTURE_2D,0,b.RGB,H,G,0,b.RGB,b.UNSIGNED_SHORT_5_6_5,d),h&&b.generateMipmap(b.TEXTURE_2D);else return console.error("No manual decoder for",String.fromCharCode(A&255,A>>8&255,A>>16&255,A>>24&255),"and no native support"),0;return N}function c(b,
c){var d=new Int32Array(b,0,r),h,I,A,D,E,H,G,L,N,y,B;if(d[v]!=f)return console.error("Invalid magic number in DDS header"),0;if(!d[z]&l)return console.error("Unsupported format, must contain a FourCC code"),0;h=d[C];switch(h){case m:I=8;A="COMPRESSED_RGB_S3TC_DXT1_EXT";break;case n:I=16;A="COMPRESSED_RGBA_S3TC_DXT3_EXT";break;case p:I=16;A="COMPRESSED_RGBA_S3TC_DXT5_EXT";break;default:I=4,A="RGBA"}N=1;d[u]&k&&!1!==loadMipmaps&&(N=Math.max(1,d[M]));D=d[w];E=d[x];G=d[s]+4;var F=[];if(d[t+1]&g)for(B=
0;6>B;++B)for(D=d[w],E=d[x],y=0;y<N;++y)h?(H=Math.max(4,D)/4*Math.max(4,E)/4*I,new Uint8Array(b,G,H),F.push({tex:"TEXTURE_CUBE_MAP",face:B,mipmap:y,internalFormat:A,width:D,height:E,offset:0,dataOffset:G,dataLength:H})):(H=D*E*I,L=new Uint8Array(b,G,H),a(L),F.push({tex:"TEXTURE_CUBE_MAP",face:B,mipmap:y,internalFormat:A,width:D,height:E,offset:0,type:"UNSIGNED_BYTE",dataOffset:G,dataLength:H})),G+=H,D*=0.5,E*=0.5;else if(c)if(h==m)Math.max(4,D),Math.max(4,E),L=new Uint16Array(b),d=e(L,G/2,D,E),F.push({tex:"TEXTURE_2D",
mipmap:0,internalFormat:"RGB",width:D,height:E,offset:0,format:"RGB",type:"UNSIGNED_SHORT_5_6_5",data:d});else return console.error("No manual decoder for",String.fromCharCode(h&255,h>>8&255,h>>16&255,h>>24&255),"and no native support"),0;else for(y=0;y<N;++y)H=Math.max(4,D)/4*Math.max(4,E)/4*I,new Uint8Array(b,G,H),F.push({tex:"TEXTURE_2D",mipmap:y,internalFormat:A,width:D,height:E,offset:0,type:"UNSIGNED_BYTE",dataOffset:G,dataLength:H}),G+=H,D*=0.5,E*=0.5;return F}function d(a,c,d,e,h,f){var k=
new XMLHttpRequest;k.open("GET",d,!0);k.responseType="arraybuffer";k.onload=function(){if(200==this.status){var d=new Int32Array(this.response,0,r),k=d[t+1]&g?a.TEXTURE_CUBE_MAP:a.TEXTURE_2D;a.bindTexture(k,e);var l=b(a,c,this.response,h);a.texParameteri(k,a.TEXTURE_MAG_FILTER,a.LINEAR);a.texParameteri(k,a.TEXTURE_MIN_FILTER,1<l?a.LINEAR_MIPMAP_LINEAR:a.LINEAR);a.bindTexture(k,null);e.texture_type=k;e.width=d[w];e.height=d[x]}f&&f(e)};k.send(null);return e}var f=542327876,k=131072,g=512,l=4,m=h("DXT1"),
n=h("DXT3"),p=h("DXT5"),r=31,v=0,s=1,u=2,x=3,w=4,M=7,z=20,C=21,t=27;return{dxtToRgb565:e,uploadDDSLevels:b,loadDDSTextureEx:d,loadDDSTexture:function(a,b,c,e){var h=a.createTexture();b=a.getExtension("WEBGL_compressed_texture_s3tc");d(a,b,c,h,!0,e);return h},loadDDSTextureFromMemoryEx:function(a,c,d,e,h){var f=new Int32Array(d,0,r),k=!!(f[t+1]&g),l=k?a.TEXTURE_CUBE_MAP:a.TEXTURE_2D;a.bindTexture(l,e.handler);c=b(a,c,d,h);a.texParameteri(l,a.TEXTURE_MAG_FILTER,a.LINEAR);a.texParameteri(l,a.TEXTURE_MIN_FILTER,
1<c?a.LINEAR_MIPMAP_LINEAR:a.LINEAR);k&&(a.texParameteri(l,a.TEXTURE_WRAP_S,a.CLAMP_TO_EDGE),a.texParameteri(l,a.TEXTURE_WRAP_T,a.CLAMP_TO_EDGE));a.bindTexture(l,null);e.handler&&(e.texture_type=l,e.width=f[w],e.height=f[x]);return e},getDDSTextureFromMemoryEx:function(a){var b=new Int32Array(a,0,r),d=b[t+1]&g?"TEXTURE_CUBE_MAP":"TEXTURE_2D",e=c(a);return{type:d,buffers:e,data:a,width:b[w],height:b[x]}}}}();"undefined"!=typeof t&&(t.DDS=S);if("undefined"==typeof glMatrix)throw"You must include glMatrix on your project";
Math.clamp=function(h,e,a){return e>h?e:a<h?a:h};Math.lerp=function(h,e,a){return h*(1-a)+e*a};Math.lerp01=function(h,e,a){return Math.clamp(h*(1-a)+e*a,0,1)};Math.iLerp=function(h,e,a){return(a-h)/(e-h)};Math.remap=function(h,e,a,b,c){return Math.lerp(b,c,Math.iLerp(e,a,h))};vec3.ZERO=vec3.fromValues(0,0,0);vec3.FRONT=vec3.fromValues(0,0,-1);vec3.UP=vec3.fromValues(0,1,0);vec3.RIGHT=vec3.fromValues(1,0,0);vec2.rotate=function(h,e,a){var b=e[0];e=e[1];var c=Math.cos(a);a=Math.sin(a);h[0]=b*c-e*a;
h[1]=b*a+e*c;return h};vec3.zero=function(h){h[0]=h[1]=0;return h};vec2.perpdot=function(h,e){return h[1]*e[0]+-h[0]*e[1]};vec2.computeSignedAngle=function(h,e){return Math.atan2(vec2.perpdot(h,e),vec2.dot(h,e))};vec2.random=function(h,e){e=e||1;h[0]=Math.random()*e;h[1]=Math.random()*e;return h};vec3.zero=function(h){h[0]=h[1]=h[2]=0;return h};vec3.minValue=function(h){return h[0]<h[1]&&h[0]<h[2]?h[0]:h[1]<h[2]?h[1]:h[2]};vec3.maxValue=function(h){return h[0]>h[1]&&h[0]>h[2]?h[0]:h[1]>h[2]?h[1]:
h[2]};vec3.minValue=function(h){return h[0]<h[1]&&h[0]<h[2]?h[0]:h[1]<h[2]?h[1]:h[2]};vec3.addValue=function(h,e,a){h[0]=e[0]+a;h[1]=e[1]+a;h[2]=e[2]+a};vec3.subValue=function(h,e,a){h[0]=e[0]-a;h[1]=e[1]-a;h[2]=e[2]-a};vec3.toArray=function(h){return[h[0],h[1],h[2]]};vec3.rotateX=function(h,e,a){var b=e[1],c=e[2],d=Math.cos(a);a=Math.sin(a);h[0]=e[0];h[1]=b*d-c*a;h[2]=b*a+c*d;return h};vec3.rotateY=function(h,e,a){var b=e[0],c=e[2],d=Math.cos(a);a=Math.sin(a);h[0]=b*d-c*a;h[1]=e[1];h[2]=b*a+c*d;
return h};vec3.rotateZ=function(h,e,a){var b=e[0],c=e[1],d=Math.cos(a);a=Math.sin(a);h[0]=b*d-c*a;h[1]=b*a+c*d;h[2]=e[2];return h};vec3.angle=function(h,e){return Math.acos(vec3.dot(h,e))};vec3.signedAngle=function(h,e,a){var b=vec3.angle(h,e);h=Math.sign(a[0]*(h[1]*e[2]-h[2]*e[1])+a[1]*(h[2]*e[0]-h[0]*e[2])+a[2]*(h[0]*e[1]-h[1]*e[0]));return b*h};vec3.random=function(h,e){e=e||1;h[0]=Math.random()*e;h[1]=Math.random()*e;h[2]=Math.random()*e;return h};vec3.cartesianToPolar=function(h,e){h=h||vec3.create();
var a=e[0],b=e[1],c=e[2];h[0]=Math.sqrt(a*a+b*b+c*c);h[1]=Math.asin(b/h[0]);h[2]=Math.atan2(a,c);return h};vec3.polarToCartesian=function(h,e){var a=e[0],b=e[1],c=e[2];h[0]=a*Math.cos(b)*Math.sin(c);h[1]=a*Math.sin(b);h[2]=a*Math.cos(b)*Math.cos(c);return h};vec3.reflect=function(h,e,a){var b=e[0],c=e[1],d=e[2];vec3.scale(h,a,-2*vec3.dot(e,a));h[0]+=b;h[1]+=c;h[2]+=d;return h};vec4.random=function(h,e){e=e||1;h[0]=Math.random()*e;h[1]=Math.random()*e;h[2]=Math.random()*e;h[3]=Math.random()*e;return h};
vec4.toArray=function(h){return[h[0],h[1],h[2],h[3]]};mat3.IDENTITY=mat3.create();mat4.IDENTITY=mat4.create();mat4.toArray=function(h){return[h[0],h[1],h[2],h[3],h[4],h[5],h[6],h[7],h[8],h[9],h[10],h[11],h[12],h[13],h[14],h[15]]};mat4.setUpAndOrthonormalize=function(h,e,a){e!=h&&mat4.copy(h,e);e=h.subarray(0,3);vec3.normalize(h.subarray(4,7),a);h=h.subarray(8,11);vec3.cross(e,a,h);vec3.normalize(e,e);vec3.cross(h,e,a);vec3.normalize(h,h)};mat4.multiplyVec3=function(h,e,a){var b=a[0],c=a[1];a=a[2];
h[0]=e[0]*b+e[4]*c+e[8]*a+e[12];h[1]=e[1]*b+e[5]*c+e[9]*a+e[13];h[2]=e[2]*b+e[6]*c+e[10]*a+e[14];return h};mat4.projectVec3=function(h,e,a){var b=a[0],c=a[1];a=a[2];var d=e[1]*b+e[5]*c+e[9]*a+e[13],f=e[2]*b+e[6]*c+e[10]*a+e[14],k=e[3]*b+e[7]*c+e[11]*a+e[15];h[0]=((e[0]*b+e[4]*c+e[8]*a+e[12])/k+1)/2;h[1]=(d/k+1)/2;h[2]=(f/k+1)/2;return h};vec3.project=function(h,e,a,b){b=b||gl.viewport_data;var c=e[0],d=e[1];e=e[2];var f=a[3]*c+a[7]*d+a[11]*e+a[15],k=1-((a[1]*c+a[5]*d+a[9]*e+a[13])/f+1)/2,g=((a[2]*
c+a[6]*d+a[10]*e+a[14])/f+1)/2;h[0]=((a[0]*c+a[4]*d+a[8]*e+a[12])/f+1)/2*b[2]+b[0];h[1]=k*b[3]+b[1];h[2]=g;return h};var T=mat4.create(),F=vec4.create();vec3.unproject=function(h,e,a,b){F[0]=2*(e[0]-b[0])/b[2]-1;F[1]=2*(e[1]-b[1])/b[3]-1;F[2]=2*e[2]-1;F[3]=1;if(!mat4.invert(T,a))return null;vec4.transformMat4(F,F,T);if(0===F[3])return null;h[0]=F[0]/F[3];h[1]=F[1]/F[3];h[2]=F[2]/F[3];return h};mat4.rotateVec3=function(h,e,a){var b=a[0],c=a[1];a=a[2];h[0]=e[0]*b+e[4]*c+e[8]*a;h[1]=e[1]*b+e[5]*c+e[9]*
a;h[2]=e[2]*b+e[6]*c+e[10]*a;return h};mat4.fromTranslationFrontTop=function(h,e,a,b){vec3.cross(h.subarray(0,3),a,b);h.set(b,4);h.set(a,8);h.set(e,12);return h};mat4.translationMatrix=function(h){var e=mat4.create();e[12]=h[0];e[13]=h[1];e[14]=h[2];return e};mat4.setTranslation=function(h,e){h[12]=e[0];h[13]=e[1];h[14]=e[2];return h};mat4.getTranslation=function(h,e){h[0]=e[12];h[1]=e[13];h[2]=e[14];return h};mat4.toRotationMat4=function(h,e){mat4.copy(h,e);h[12]=h[13]=h[14]=0;return h};mat4.swapRows=
function(h,e,a,b){if(h!=e)return mat4.copy(h,e),h[4*a]=e[4*b],h[4*a+1]=e[4*b+1],h[4*a+2]=e[4*b+2],h[4*a+3]=e[4*b+3],h[4*b]=e[4*a],h[4*b+1]=e[4*a+1],h[4*b+2]=e[4*a+2],h[4*b+3]=e[4*a+3],h;e=new Float32Array(matrix.subarray(4*a,5*a));matrix.set(matrix.subarray(4*b,5*b),4*a);matrix.set(e,4*b);return h};mat4.scaleAndAdd=function(h,e,a,b){h[0]=e[0]+a[0]*b;h[1]=e[1]+a[1]*b;h[2]=e[2]+a[2]*b;h[3]=e[3]+a[3]*b;h[4]=e[4]+a[4]*b;h[5]=e[5]+a[5]*b;h[6]=e[6]+a[6]*b;h[7]=e[7]+a[7]*b;h[8]=e[8]+a[8]*b;h[9]=e[9]+a[9]*
b;h[10]=e[10]+a[10]*b;h[11]=e[11]+a[11]*b;h[12]=e[12]+a[12]*b;h[13]=e[13]+a[13]*b;h[14]=e[14]+a[14]*b;h[15]=e[15]+a[15]*b;return h};quat.fromAxisAngle=function(h,e){var a=quat.create();e*=0.5;var b=Math.sin(e);a[0]=b*h[0];a[1]=b*h[1];a[2]=b*h[2];a[3]=Math.cos(e);return a};quat.lookRotation=function(){var h=vec3.create(),e=vec3.create(),a=vec3.create();return function(b,c,d){vec3.normalize(h,c);vec3.cross(e,d,h);vec3.normalize(e,e);vec3.cross(a,h,e);var f=e[0];c=e[1];d=e[2];var k=a[0],g=a[1],l=a[2],
m=h[0],n=h[1],p=h[2],r=f+g+p;if(0<r)return f=Math.sqrt(r+1),b[3]=0.5*f,f=0.5/f,b[0]=(l-n)*f,b[1]=(m-d)*f,b[2]=(c-k)*f,b;if(f>=g&&f>=p)return f=Math.sqrt(1+f-g-p),g=0.5/f,b[0]=0.5*f,b[1]=(c+k)*g,b[2]=(d+m)*g,b[3]=(l-n)*g,b;if(g>p)return f=Math.sqrt(1+g-f-p),g=0.5/f,b[0]=(k+c)*g,b[1]=0.5*f,b[2]=(n+l)*g,b[3]=(m-d)*g,b;f=Math.sqrt(1+p-f-g);g=0.5/f;b[0]=(m+d)*g;b[1]=(n+l)*g;b[2]=0.5*f;b[3]=(c-k)*g;return b}}();quat.toEuler=function(h,e){var a=Math.atan2(2*e[1]*e[3]-2*e[0]*e[2],1-2*e[1]*e[1]-2*e[2]*e[2]),
b=Math.asin(2*e[0]*e[1]+2*e[2]*e[3]),c=Math.atan2(2*e[0]*e[3]-2*e[1]*e[2],1-2*e[0]*e[0]-2*e[2]*e[2]);h||(h=vec3.create());vec3.set(h,a,b,c);return h};quat.fromEuler=function(h,e){var a=e[0],b=e[1],c=e[2],d=Math.cos(a),f=Math.cos(b),k=Math.cos(c),a=Math.sin(a),b=Math.sin(b),c=Math.sin(c),g=0.5*Math.sqrt(1+d*f+d*k-a*b*c+f*k);0==g&&(g=1E-6);quat.set(h,(f*c+d*c+a*b*k)/(4*g),(a*f+a*k+d*b*c)/(4*g),(-a*c+d*b*k+b)/(4*g),g);quat.normalize(h,h);return h};quat.fromMat4=function(h,e){var a=e[0]+e[5]+e[10];if(0<
a){var b=Math.sqrt(a+1);h[3]=0.5*b;b=0.5/b;h[0]=(e[9]-e[6])*b;h[1]=(e[8]-e[2])*b;h[2]=(e[4]-e[1])*b}else{a=0;e[5]>e[0]&&(a=1);e[10]>e[4*a+a]&&(a=2);var c=(a+1)%3,d=(c+1)%3,b=Math.sqrt(e[4*a+a]-e[4*c+c]-e[4*d+d]+1);h[a]=0.5*b;b=0.5/b;h[3]=(e[4*d+c]-e[4*c+d])*b;h[c]=(e[4*c+a]+e[4*a+c])*b;h[d]=(e[4*d+a]+e[4*a+d])*b}quat.normalize(h,h)};vec3.getMat3Column=function(h,e,a){h[0]=e[3*a];h[1]=e[3*a+1];h[2]=e[3*a+2];return h};mat3.setColumn=function(h,e,a){h[3*a]=e[0];h[3*a+1]=e[1];h[3*a+2]=e[2];return h};
quat.fromMat3AndQuat=function(){var h=mat3.create(),e=quat.create(),a=vec3.create(),b=vec3.create(),c=vec3.create(),d=vec3.create(),f=vec3.create(),k=vec3.create(),g=vec3.create(),l=vec3.create(),m=vec3.create(),n=vec3.create();mat3.create();return function(p,r,v){v=v||25;for(var s=0;s<v;++s){var u=mat3.fromQuat(h,p);vec3.getMat3Column(a,u,0);vec3.getMat3Column(b,u,1);vec3.getMat3Column(c,u,2);vec3.getMat3Column(d,r,0);vec3.getMat3Column(f,r,1);vec3.getMat3Column(k,r,2);vec3.cross(g,a,d);vec3.cross(l,
b,f);vec3.cross(m,c,k);vec3.add(n,g,l);vec3.add(n,n,m);u=1/Math.abs(vec3.dot(a,d)+vec3.dot(b,f)+vec3.dot(c,k))+1E-9;vec3.scale(n,n,u);u=vec3.length(n);if(1E-9>u)break;vec3.scale(n,n,1/u);quat.setAxisAngle(e,n,u);quat.mul(p,e,p);quat.normalize(p,p)}return p}}();quat.rotateToFrom=function(){var h=vec3.create();return function(e,a,b){e=e||quat.create();var c=vec3.cross(h,a,b);a=vec3.dot(a,b);if(-0.99>a)return e[0]=0,e[1]=1,e[2]=0,e[3]=0,e;e[0]=0.5*c[0];e[1]=0.5*c[1];e[2]=0.5*c[2];e[3]=0.5*(1+a);quat.normalize(e,
e);return e}}();quat.lookAt=function(){var h=vec3.create();return function(e,a,b){b=vec3.dot(vec3.FRONT,a);if(1E-6>Math.abs(b- -1))return e.set(vec3.UP),e[3]=Math.PI,e;if(1E-6>Math.abs(b-1))return quat.identity(e);b=Math.acos(b);vec3.cross(h,vec3.FRONT,a);vec3.normalize(h,h);quat.setAxisAngle(e,h,b);return e}}();g.Indexer=function(){this.unique=[];this.indices=[];this.map={}};g.Indexer.prototype={add:function(h){var e=JSON.stringify(h);e in this.map||(this.map[e]=this.unique.length,this.unique.push(h));
return this.map[e]}};g.Buffer=function(h,e,a,b,c){g.debug&&console.log("GL.Buffer created");null!==c&&(c=c||t.gl);this.gl=c;this.buffer=null;this.target=h;this.attribute=null;this.data=e;this.spacing=a||3;this.data&&this.gl&&this.upload(b)};g.Buffer.prototype.bind=function(h,e){e=e||this.gl;e.bindBuffer(e.ARRAY_BUFFER,this.buffer);e.enableVertexAttribArray(h);e.vertexAttribPointer(h,this.spacing,this.buffer.gl_type,!1,0,0)};g.Buffer.prototype.unbind=function(h,e){e=e||this.gl;e.disableVertexAttribArray(h)};
g.Buffer.prototype.forEach=function(h){for(var e=this.data,a=0,b=this.spacing,c=e.length;a<c;a+=b)h(e.subarray(a,a+b),a);return this};g.Buffer.prototype.applyTransform=function(h){for(var e=this.data,a=0,b=this.spacing,c=e.length;a<c;a+=b){var d=e.subarray(a,a+b);vec3.transformMat4(d,d,h)}return this};g.Buffer.prototype.upload=function(h){var e=this.spacing||3,a=this.gl;if(a){if(!this.data)throw"No data supplied";var b=this.data;if(!b.buffer)throw"Buffers must be typed arrays";if(this.buffer=this.buffer||
a.createBuffer()){this.buffer.length=b.length;this.buffer.spacing=e;switch(b.constructor){case Int8Array:this.buffer.gl_type=a.BYTE;break;case Uint8ClampedArray:case Uint8Array:this.buffer.gl_type=a.UNSIGNED_BYTE;break;case Int16Array:this.buffer.gl_type=a.SHORT;break;case Uint16Array:this.buffer.gl_type=a.UNSIGNED_SHORT;break;case Int32Array:this.buffer.gl_type=a.INT;break;case Uint32Array:this.buffer.gl_type=a.UNSIGNED_INT;break;case Float32Array:this.buffer.gl_type=a.FLOAT;break;default:throw"unsupported buffer type";
}this.target!=a.ARRAY_BUFFER||this.buffer.gl_type!=a.INT&&this.buffer.gl_type!=a.UNSIGNED_INT||(console.warn("WebGL does not support UINT32 or INT32 as vertex buffer types, converting to FLOAT"),this.buffer.gl_type=a.FLOAT,b=new Float32Array(b));a.bindBuffer(this.target,this.buffer);a.bufferData(this.target,b,h||this.stream_type||a.STATIC_DRAW)}}};g.Buffer.prototype.compile=g.Buffer.prototype.upload;g.Buffer.prototype.setData=function(h,e){if(!h.buffer)throw"Data must be typed array";e=e||0;if(this.data){if(this.data.length<
h.length)throw"buffer is not big enough, you cannot set data to a smaller buffer";if(this.data!=h)if(this.data.length==h.length)this.data.set(h),this.upload();else{var a=new Uint8Array(h.buffer,h.buffer.byteOffset,h.buffer.byteLength);(new Uint8Array(this.data.buffer)).set(a,e);this.uploadRange(e,a.length)}}else this.data=h,this.upload()};g.Buffer.prototype.uploadRange=function(h,e){if(!this.data)throw"No data stored in this buffer";if(!this.data.buffer)throw"Buffers must be typed arrays";var a=new Uint8Array(this.data.buffer,
h,e),b=this.gl;b.bindBuffer(this.target,this.buffer);b.bufferSubData(this.target,h,a)};g.Buffer.prototype.clone=function(h){var e=new g.Buffer;if(h)for(var a in this)e[a]=this[a];else this.target&&(e.target=this.target),this.gl&&(e.gl=this.gl),this.spacing&&(e.spacing=this.spacing),this.data&&(e.data=new t[this.data.constructor](this.data),e.upload());return e};g.Buffer.prototype.toJSON=function(){return this.data?{data_type:getClassName(this.data),data:this.data.toJSON(),target:this.target,attribute:this.attribute,
spacing:this.spacing}:(console.error("cannot serialize a mesh without data"),null)};g.Buffer.prototype.fromJSON=function(h){this.data=new (t[h.data_type]||Float32Array)(h.data);this.target=h.target;this.spacing=h.spacing||3;this.attribute=h.attribute;this.upload(g.STATIC_DRAW)};g.Buffer.prototype.delete=function(){this.gl.deleteBuffer(this.buffer);this.buffer=null};t.Mesh=g.Mesh=function(h,e,a,b){g.debug&&console.log("GL.Mesh created");null!==b&&(this.gl=b=b||t.gl);this._context_id=b.context_id;this.vertexBuffers=
{};this.indexBuffers={};this.info={groups:[]};this._bounding=BBox.create();(h||e)&&this.addBuffers(h,e,a?a.stream_type:null);if(a)for(var c in a)this[c]=a[c]};Mesh.common_buffers={vertices:{spacing:3,attribute:"a_vertex"},vertices2D:{spacing:2,attribute:"a_vertex2D"},normals:{spacing:3,attribute:"a_normal"},coords:{spacing:2,attribute:"a_coord"},coords1:{spacing:2,attribute:"a_coord1"},coords2:{spacing:2,attribute:"a_coord2"},colors:{spacing:4,attribute:"a_color"},tangents:{spacing:3,attribute:"a_tangent"},
bone_indices:{spacing:4,attribute:"a_bone_indices",type:Uint8Array},weights:{spacing:4,attribute:"a_weights"},extra:{spacing:1,attribute:"a_extra"},extra2:{spacing:2,attribute:"a_extra2"},extra3:{spacing:3,attribute:"a_extra3"},extra4:{spacing:4,attribute:"a_extra4"}};Mesh.default_datatype=Float32Array;Object.defineProperty(Mesh.prototype,"bounding",{set:function(h){if(h){if(13>h.length)throw"Bounding must use the BBox bounding format of 13 floats: center, halfsize, min, max, radius";this._bounding.set(h)}},
get:function(){return this._bounding}});Mesh.prototype.addBuffer=function(h,e){e.target==gl.ARRAY_BUFFER?this.vertexBuffers[h]=e:this.indexBuffers[h]=e;if(!e.attribute){var a=g.Mesh.common_buffers[h];a&&(e.attribute=a.attribute)}};Mesh.prototype.addBuffers=function(h,e,a){var b=0;this.vertexBuffers.vertices&&(b=this.vertexBuffers.vertices.data.length/3);for(var c in h){var d=h[c];if(d){if(d.constructor==g.Buffer||d.data)d=d.data;else if("number"!=typeof d[0]){for(var f=[],k=0,q=1E4;k<d.length;k+=
q)f=Array.prototype.concat.apply(f,d.slice(k,k+q));d=f}f=g.Mesh.common_buffers[c];d.constructor===Array&&(k=g.Mesh.default_datatype,f&&f.type&&(k=f.type),d=new k(d));"vertices"==c&&(b=d.length/3);k=d.length/b;f&&f.spacing&&(k=f.spacing);q="a_"+c;f&&f.attribute&&(q=f.attribute);this.vertexBuffers[c]?this.updateVertexBuffer(c,q,k,d,a):this.createVertexBuffer(c,q,k,d,a)}}if(e)for(c in e)if(d=e[c]){if(d.constructor==g.Buffer||d.data)d=d.data;if("number"!=typeof d[0]){f=[];c=0;for(q=1E4;c<d.length;c+=
q)f=Array.prototype.concat.apply(f,d.slice(c,c+q));d=f}d.constructor===Array&&(k=Uint16Array,65536<b&&(k=Uint32Array),d=new k(d));this.createIndexBuffer(c,d)}};Mesh.prototype.createVertexBuffer=function(h,e,a,b,c){var d=g.Mesh.common_buffers[h];!e&&d&&(e=d.attribute);if(!e)throw"Buffer added to mesh without attribute name";!a&&d&&(a=d&&d.spacing?d.spacing:3);if(!b){b=this.getNumVertices();if(!b)throw"Cannot create an empty buffer in a mesh without vertices (vertices are needed to know the size)";
b=new g.Mesh.default_datatype(b*a)}if(!b.buffer)throw"Buffer data MUST be typed array";a=this.vertexBuffers[h]=new g.Buffer(gl.ARRAY_BUFFER,b,a,c,this.gl);a.name=h;a.attribute=e;return a};Mesh.prototype.updateVertexBuffer=function(h,e,a,b,c){var d=this.vertexBuffers[h];d?b.length&&(d.attribute=e,d.spacing=a,d.data=b,d.upload(c)):console.log("buffer not found: ",h)};Mesh.prototype.removeVertexBuffer=function(h,e){var a=this.vertexBuffers[h];a&&(e&&a.delete(),delete this.vertexBuffers[h])};Mesh.prototype.getVertexBuffer=
function(h){return this.vertexBuffers[h]};Mesh.prototype.createIndexBuffer=function(h,e,a){if(e.constructor===Array){var b=Uint16Array,c=this.vertexBuffers.vertices;c&&(65536<c.data.length/3&&(b=Uint32Array),e=new b(e))}return this.indexBuffers[h]=new g.Buffer(gl.ELEMENT_ARRAY_BUFFER,e,0,a,this.gl)};Mesh.prototype.getBuffer=function(h){return this.vertexBuffers[h]};Mesh.prototype.getIndexBuffer=function(h){return this.indexBuffers[h]};Mesh.prototype.removeIndexBuffer=function(h,e){var a=this.indexBuffers[h];
a&&(e&&a.delete(),delete this.indexBuffers[h])};Mesh.prototype.upload=function(h){for(var e in this.vertexBuffers){var a=this.vertexBuffers[e];a.upload(h)}for(var b in this.indexBuffers)a=this.indexBuffers[b],a.upload()};Mesh.prototype.compile=Mesh.prototype.upload;Mesh.prototype.deleteBuffers=function(){for(var h in this.vertexBuffers){var e=this.vertexBuffers[h];e.delete()}this.vertexBuffers={};for(h in this.indexBuffers)e=this.indexBuffers[h],e.delete();this.indexBuffers={}};Mesh.prototype.delete=
Mesh.prototype.deleteBuffers;Mesh.prototype.bindBuffers=function(h){for(var e in this.vertexBuffers){var a=this.vertexBuffers[e],b=h.attributes[a.attribute||e];null!=b&&a.buffer&&(gl.bindBuffer(gl.ARRAY_BUFFER,a.buffer),gl.enableVertexAttribArray(b),gl.vertexAttribPointer(b,a.buffer.spacing,a.buffer.gl_type,!1,0,0))}};Mesh.prototype.unbindBuffers=function(h){for(var e in this.vertexBuffers){var a=this.vertexBuffers[e],b=a.attribute||e;null!=h.attributes[b]&&a.buffer&&gl.disableVertexAttribArray(h.attributes[b])}};
Mesh.prototype.clone=function(h){h=h||t.gl;var e={},a={},b;for(b in this.vertexBuffers){var c=this.vertexBuffers[b];e[b]=new c.data.constructor(c.data)}for(b in this.indexBuffers)c=this.indexBuffers[b],a[b]=new c.data.constructor(c.data);return new g.Mesh(e,a,void 0,h)};Mesh.prototype.cloneShared=function(h){h=h||t.gl;return new g.Mesh(this.vertexBuffers,this.indexBuffers,void 0,h)};Mesh.prototype.toObject=function(){var h={},e={},a;for(a in this.vertexBuffers){var b=this.vertexBuffers[a];h[a]={spacing:b.spacing,
data:new b.data.constructor(b.data)}}for(a in this.indexBuffers)b=this.indexBuffers[a],e[a]={data:new b.data.constructor(b.data)};return{vertexBuffers:h,indexBuffers:e,info:this.info?cloneObject(this.info):null,bounding:this._bounding.toJSON()}};Mesh.prototype.toJSON=function(){var h={vertexBuffers:{},indexBuffers:{},info:this.info?cloneObject(this.info):null,bounding:this._bounding.toJSON()},e;for(e in this.vertexBuffers)h.vertexBuffers[e]=this.vertexBuffers[e].toJSON();for(e in this.indexBuffers)h.indexBuffers[e]=
this.indexBuffers[e].toJSON();return h};Mesh.prototype.fromJSON=function(h){this.vertexBuffers={};this.indexBuffers={};for(var e in h.vertexBuffers)if(h.vertexBuffers[e]){var a=new g.Buffer;a.fromJSON(h.vertexBuffers[e]);!a.attribute&&g.Mesh.common_buffers[e]&&(a.attribute=g.Mesh.common_buffers[e].attribute);this.vertexBuffers[e]=a}for(e in h.indexBuffers)h.indexBuffers[e]&&(a=new g.Buffer,a.fromJSON(h.indexBuffers[e]),this.indexBuffers[e]=a);h.info&&(this.info=cloneObject(h.info));h.bounding&&(this.bounding=
h.bounding)};Mesh.prototype.generateMetadata=function(){var h={},e=this.vertexBuffers.vertices.data,a=this.indexBuffers.triangles.data;h.vertices=e.length/3;h.faces=a?a.length/3:e.length/9;h.indexed=!!this.metadata.faces;this.metadata=h};Mesh.prototype.computeWireframe=function(){var h=this.indexBuffers.triangles,e=this.vertexBuffers.vertices.data.length/3;if(h){for(var a=h.data,b=new g.Indexer,h=0;h<a.length;h+=3)for(var c=a.subarray(h,h+3),d=0;d<c.length;d++){var f=c[d],k=c[(d+1)%c.length];b.add([Math.min(f,
k),Math.max(f,k)])}a=b.unique;b=65536<e?new Uint32Array(2*a.length):new Uint16Array(2*a.length);h=0;for(e=a.length;h<e;++h)b.set(a[h],2*h)}else for(h=e/3,b=65536<e?new Uint32Array(6*h):new Uint16Array(6*h),h=0;h<e;h+=3)b[2*h]=h,b[2*h+1]=h+1,b[2*h+2]=h+1,b[2*h+3]=h+2,b[2*h+4]=h+2,b[2*h+5]=h;this.createIndexBuffer("wireframe",b);return this};Mesh.prototype.flipNormals=function(h){var e=this.vertexBuffers.normals;if(e){for(var a=e.data,b=a.length,c=0;c<b;++c)a[c]*=-1;e.upload(h);this.indexBuffers.triangles||
this.computeIndices();e=this.indexBuffers.triangles;a=e.data;b=a.length;for(c=0;c<b;c+=3){var d=a[c];a[c]=a[c+1];a[c+1]=d}e.upload(h)}};Mesh.prototype.computeIndices=function(){var h=[],e=[],a=[],b=[],c=this.vertexBuffers.normals,d=this.vertexBuffers.coords,f=this.vertexBuffers.vertices.data,k=null;c&&(k=c.data);c=null;d&&(c=d.data);for(var d={},q=f.length/3,l=0;l<q;++l){var m=f.subarray(3*l,3*(l+1)),n=1E3*m[0]|0,p=0,r=d[n];if(r)for(var v=r.length;p<v;p++)if(0.01>vec3.sqrDist(m,h[r[p]])){b.push(p);
break}r&&p!=v||(h.push(m),d[n]?d[n].push(p):d[n]=[p],k&&e.push(k.subarray(3*l,3*(l+1))),c&&a.push(c.subarray(2*l,2*(l+1))),b.push(p))}this.vertexBuffers={};this.createVertexBuffer("vertices",g.Mesh.common_buffers.vertices.attribute,3,R(h));k&&this.createVertexBuffer("normals",g.Mesh.common_buffers.normals.attribute,3,R(e));c&&this.createVertexBuffer("coords",g.Mesh.common_buffers.coords.attribute,2,R(a));this.createIndexBuffer("triangles",b)};Mesh.prototype.explodeIndices=function(h){h=h||"triangles";
var e=this.getIndexBuffer(h);if(e){var a=e.data,e={},b;for(b in this.vertexBuffers){var c=g.Mesh.common_buffers[b];e[b]=new (c.type||Float32Array)(c.spacing*a.length)}b=0;for(var d=a.length;b<d;++b){var f=a[b],k;for(k in this.vertexBuffers){var q=this.vertexBuffers[k],c=g.Mesh.common_buffers[k],c=q.spacing||c.spacing;e[k].set(q.data.subarray(f*c,f*c+c),b*c)}}for(b in e)k=this.vertexBuffers[b],this.createVertexBuffer(b,k.attribute,k.spacing,e[b]);delete this.indexBuffers[h]}};Mesh.prototype.computeNormals=
function(h){if(!this.vertexBuffers.vertices)return console.error("Cannot compute normals of a mesh without vertices");var e=this.vertexBuffers.vertices.data,a=new Float32Array(e.length),b=null;this.indexBuffers.triangles&&(b=this.indexBuffers.triangles.data);for(var c=g.temp_vec3,d=g.temp2_vec3,f,k,q,l,m,n,p=b?b.length:e.length,r=0;r<p;r+=3)b?(f=b[r],k=b[r+1],q=b[r+2],l=e.subarray(3*f,3*f+3),m=e.subarray(3*k,3*k+3),n=e.subarray(3*q,3*q+3),f=a.subarray(3*f,3*f+3),k=a.subarray(3*k,3*k+3),q=a.subarray(3*
q,3*q+3)):(l=e.subarray(3*r,3*r+3),m=e.subarray(3*r+3,3*r+6),n=e.subarray(3*r+6,3*r+9),f=a.subarray(3*r,3*r+3),k=a.subarray(3*r+3,3*r+6),q=a.subarray(3*r+6,3*r+9)),vec3.sub(c,m,l),vec3.sub(d,n,l),vec3.cross(c,c,d),vec3.normalize(c,c),vec3.add(f,f,c),vec3.add(k,k,c),vec3.add(q,q,c);if(b)for(r=0,p=a.length;r<p;r+=3)e=a.subarray(r,r+3),vec3.normalize(e,e);if(p=this.vertexBuffers.normals)p.data=a,p.upload(h);else return this.createVertexBuffer("normals",g.Mesh.common_buffers.normals.attribute,3,a);return p};
Mesh.prototype.computeTangents=function(){var h=this.vertexBuffers.vertices;if(!h)return console.error("Cannot compute tangents of a mesh without vertices");var e=this.vertexBuffers.normals;if(!e)return console.error("Cannot compute tangents of a mesh without normals");var a=this.vertexBuffers.coords;if(!a)return console.error("Cannot compute tangents of a mesh without uvs");var b=this.indexBuffers.triangles;if(!b)return console.error("Cannot compute tangents of a mesh without indices");var h=h.data,
e=e.data,c=a.data,d=b.data;if(h&&e&&c){var f=h.length/3,b=new Float32Array(4*f),a=new Float32Array(6*f),f=a.subarray(3*f),k,g,l=vec3.create(),m=vec3.create(),n=vec3.create(),p=vec3.create();k=0;for(g=d.length;k<g;k+=3){var r=d[k],v=d[k+1],s=d[k+2],u=h.subarray(3*r,3*r+3),x=h.subarray(3*v,3*v+3),w=h.subarray(3*s,3*s+3),M=c.subarray(2*r,2*r+2),z=c.subarray(2*v,2*v+2),C=c.subarray(2*s,2*s+2),t=x[0]-u[0],Q=w[0]-u[0],y=x[1]-u[1],B=w[1]-u[1],x=x[2]-u[2],u=w[2]-u[2],w=z[0]-M[0],F=C[0]-M[0],z=z[1]-M[1],M=
C[1]-M[1],C=w*M-F*z,C=1E-9>Math.abs(C)?0:1/C;vec3.copy(l,[(M*t-z*Q)*C,(M*y-z*B)*C,(M*x-z*u)*C]);vec3.copy(m,[(w*Q-F*t)*C,(w*B-F*y)*C,(w*u-F*x)*C]);vec3.add(a.subarray(3*r,3*r+3),a.subarray(3*r,3*r+3),l);vec3.add(a.subarray(3*v,3*v+3),a.subarray(3*v,3*v+3),l);vec3.add(a.subarray(3*s,3*s+3),a.subarray(3*s,3*s+3),l);vec3.add(f.subarray(3*r,3*r+3),f.subarray(3*r,3*r+3),m);vec3.add(f.subarray(3*v,3*v+3),f.subarray(3*v,3*v+3),m);vec3.add(f.subarray(3*s,3*s+3),f.subarray(3*s,3*s+3),m)}k=0;for(g=h.length;k<
g;k+=3)h=e.subarray(k,k+3),c=a.subarray(k,k+3),vec3.subtract(n,c,vec3.scale(n,h,vec3.dot(h,c))),vec3.normalize(n,n),h=0>vec3.dot(vec3.cross(p,h,c),f.subarray(k,k+3))?-1:1,b.set([n[0],n[1],n[2],h],k/3*4);this.createVertexBuffer("tangents",Mesh.common_buffers.tangents.attribute,4,b)}};Mesh.prototype.computeTextureCoordinates=function(h){var e=this.vertexBuffers.vertices;if(!e)return console.error("Cannot compute uvs of a mesh without vertices");this.explodeIndices("triangles");var e=e.data,a=this.vertexBuffers.coords,
b=new Float32Array(e.length/3*2),c=this.indexBuffers.triangles,d=null;c&&(d=c.data);var c=vec3.create(),f=vec3.create(),k=vec3.create(),g=this.getBoundingBox(),l=BBox.getCenter(g),m=vec3.create();m.set(BBox.getHalfsize(g));vec3.scale(m,m,2);for(var g=d?d.length:e.length/3,n=0;n<g;n+=3){if(d)var p=d[n],r=d[n+1],v=d[n+2],s=e.subarray(3*p,3*p+3),u=e.subarray(3*r,3*r+3),x=e.subarray(3*v,3*v+3),p=b.subarray(2*p,2*p+2),r=b.subarray(2*r,2*r+2),v=b.subarray(2*v,2*v+2);else s=e.subarray(3*n,3*n+3),u=e.subarray(3*
(n+1),3*(n+1)+3),x=e.subarray(3*(n+2),3*(n+2)+3),p=b.subarray(2*n,2*n+2),r=b.subarray(2*(n+1),2*(n+1)+2),v=b.subarray(2*(n+2),2*(n+2)+2);vec3.sub(f,s,u);vec3.sub(k,s,x);vec3.cross(c,f,k);c[0]=Math.abs(c[0]);c[1]=Math.abs(c[1]);c[2]=Math.abs(c[2]);c[0]>c[1]&&c[0]>c[2]?(p[0]=(s[2]-l[2])/m[2],p[1]=(s[1]-l[1])/m[1],r[0]=(u[2]-l[2])/m[2],r[1]=(u[1]-l[1])/m[1],v[0]=(x[2]-l[2])/m[2],v[1]=(x[1]-l[1])/m[1]):c[1]>c[2]?(p[0]=(s[0]-l[0])/m[0],p[1]=(s[2]-l[2])/m[2],r[0]=(u[0]-l[0])/m[0],r[1]=(u[2]-l[2])/m[2],
v[0]=(x[0]-l[0])/m[0],v[1]=(x[2]-l[2])/m[2]):(p[0]=(s[0]-l[0])/m[0],p[1]=(s[1]-l[1])/m[1],r[0]=(u[0]-l[0])/m[0],r[1]=(u[1]-l[1])/m[1],v[0]=(x[0]-l[0])/m[0],v[1]=(x[1]-l[1])/m[1])}a?(a.data=b,a.upload(h)):this.createVertexBuffer("coords",Mesh.common_buffers.coords.attribute,2,b)};Mesh.prototype.getNumVertices=function(){var h=this.vertexBuffers.vertices;return h?h.data.length/h.spacing:0};Mesh.prototype.getNumTriangles=function(){var h=this.getIndexBuffer("triangles");return h?h.data.length/3:this.getNumVertices()/
3};Mesh.computeBoundingBox=function(h,e,a){if(h){var b=0;if(a){for(var c=0;c<a.length;++c)if(a[c]){b=c;break}if(b==a.length){console.warn("mask contains only zeros, no vertices marked");return}}for(var d=vec3.clone(h.subarray(3*b,3*b+3)),f=vec3.clone(h.subarray(3*b,3*b+3)),c=3*b;c<h.length;c+=3)if(!a||a[c/3])b=h.subarray(c,c+3),vec3.min(d,b,d),vec3.max(f,b,f);if(isNaN(d[0])||isNaN(d[1])||isNaN(d[2])||isNaN(f[0])||isNaN(f[1])||isNaN(f[2]))d[0]=d[1]=d[2]=0,f[0]=f[1]=f[2]=0,console.warn("Warning: GL.Mesh has NaN values in vertices");
h=vec3.add(vec3.create(),d,f);vec3.scale(h,h,0.5);f=vec3.subtract(vec3.create(),f,h);return BBox.setCenterHalfsize(e||BBox.create(),h,f)}};Mesh.prototype.getBoundingBox=function(){if(this._bounding)return this._bounding;this.updateBoundingBox();return this._bounding};Mesh.prototype.updateBoundingBox=function(){var h=this.vertexBuffers.vertices;h&&(g.Mesh.computeBoundingBox(h.data,this._bounding),this.info&&this.info.groups&&this.info.groups.length&&this.computeGroupsBoundingBoxes())};Mesh.prototype.computeGroupsBoundingBoxes=
function(){var h=null,e=this.getIndexBuffer("triangles");e&&(h=e.data);e=this.getVertexBuffer("vertices");if(!e)return!1;e=e.data;if(!e.length)return!1;var a=this.info.groups;if(a){for(var b=0;b<a.length;++b){var c=a[b];c.bounding=c.bounding||BBox.create();var d=null;if(h){for(var d=new Uint8Array(e.length/3),f=c.start,k=0,q=c.length;k<q;k+=3)d[h[f+k]]=1,d[h[f+k+1]]=1,d[h[f+k+2]]=1;g.Mesh.computeBoundingBox(e,c.bounding,d)}else d=e.subarray(3*c.start,3*(c.start+c.length)),g.Mesh.computeBoundingBox(d,
c.bounding)}return!0}};Mesh.prototype.setBoundingBox=function(h,e){BBox.setCenterHalfsize(this._bounding,h,e)};Mesh.prototype.freeData=function(){for(var h in this.vertexBuffers)this.vertexBuffers[h].data=null,delete this[this.vertexBuffers[h].name];for(var e in this.indexBuffers)this.indexBuffers[e].data=null,delete this[this.indexBuffers[e].name]};Mesh.prototype.configure=function(h,e){var a={},b={};e=e||{};for(var c in h)if(h[c])if("vertexBuffers"==c||"vertex_buffers"==c)for(d in h[c])a[d]=h[c][d];
else if("indexBuffers"==c||"index_buffers"==c)for(d in h[c])b[d]=h[c][d];else"indices"==c||"lines"==c||"wireframe"==c||"triangles"==c?b[c]=h[c]:g.Mesh.common_buffers[c]?a[c]=h[c]:e[c]=h[c];this.addBuffers(a,b,e.stream_type);for(var d in e)this[d]=e[d];e.bounding||this.updateBoundingBox()};Mesh.prototype.totalMemory=function(){var h=0,e;for(e in this.vertexBuffers)h+=this.vertexBuffers[e].data.buffer.byteLength;for(e in this.indexBuffers)h+=this.indexBuffers[e].data.buffer.byteLength;return h};Mesh.prototype.slice=
function(h,e){var a={},b=this.indexBuffers.triangles;if(!b)return console.warn("splice in not indexed not supported yet"),null;var b=b.data,c=[],d=new Int32Array(b.length);d.fill(-1);var f=h+e;f>=b.length&&(f=b.length);for(var k=0,q=h;q<f;++q){var l=b[q];if(-1!=d[l])c.push(d[l]);else{var m=k++;d[l]=m;c.push(m);for(var n in this.vertexBuffers){var p=this.vertexBuffers[n],m=p.data,p=p.spacing;a[n]||(a[n]=[]);for(var r=a[n],v=0;v<p;++v)r.push(m[v+l*p])}}}a=new g.Mesh(a,{triangles:c},null,gl);a.updateBoundingBox();
return a};Mesh.prototype.simplify=function(){var h=this.getBoundingBox(),e=BBox.getMin(h),h=BBox.getHalfsize(h),h=vec3.scale(vec3.create(),h,2),a=new g.Mesh,b=vec3.create(),c;for(c in this.vertexBuffers){var d=this.vertexBuffers[c].data,f=new Float32Array(d.length);if("vertices"==c)for(var k=0,q=d.length;k<q;k+=3){var l=d.subarray(k,k+3);vec3.sub(b,l,e);vec3.div(b,b,h);b[0]=Math.round(256*b[0])/256;b[1]=Math.round(256*b[1])/256;b[2]=Math.round(256*b[2])/256;vec3.mul(b,b,h);vec3.add(b,b,e);f.set(b,
k)}a.addBuffer()}};Mesh.load=function(h,e,a,b){e=e||{};e.no_gl&&(b=null);a=a||new g.Mesh(null,null,null,b);a.configure(h,e);return a};Mesh.mergeMeshes=function(h,e){function a(a,b,c,d){for(c=b+c;b<c;b+=3){var e=a.subarray(b,b+3);vec3.transformMat4(e,e,d)}}function b(a,b,c,d){for(c=b+c;b<c;b+=2){var e=a.subarray(b,b+2);vec2.transformMat3(e,e,d)}}function c(a,b,c,d){if(d)for(c=b+c;b<c;++b)a[b]+=d}e=e||{};for(var d={},f={},k={},q=[],l=0,m=[],n=[],p={},r=0;r<h.length;++r){var v=h[r],s=v.mesh,u=l;q.push(u);
var x=s.vertexBuffers.vertices.data.length/3,l=l+x,w;for(w in s.vertexBuffers)d[w]=d[w]?d[w]+s.vertexBuffers[w].data.length:s.vertexBuffers[w].data.length;for(w in s.indexBuffers)f[w]=f[w]?f[w]+s.indexBuffers[w].data.length:s.indexBuffers[w].data.length;v={name:"mesh_"+r,start:u,length:x,material:""};if(s.bones){u={};for(w=0;w<s.bones.length;++w)x=s.bones[w],p[x[0]]||(p[x[0]]=n.length,n.push(x)),u[w]=p[x[0]];s=s.vertexBuffers.bone_indices.data;for(w=0;w<s.length;w+=1)s[w]=u[s[w]]}else if(n.length)throw"cannot merge meshes, one contains bones, the other doesnt";
m.push(v)}for(w in d)r=e[w],null===r?delete d[w]:(r||(r=Float32Array),d[w]=new r(d[w]),k[w]=0);for(w in f)r=65536>l?Uint16Array:Uint32Array,f[w]=new r(f[w]),k[w]=0;for(r=0;r<h.length;++r){v=h[r];s=v.mesh;x=u=0;for(w in s.vertexBuffers)d[w]&&("vertices"==w&&(x=s.vertexBuffers[w].data.length/3),d[w].set(s.vertexBuffers[w].data,k[w]),v[w+"_matrix"]&&(l=v[w+"_matrix"],16==l.length?a(d[w],k[w],s.vertexBuffers[w].data.length,l):9==l.length&&b(d[w],k[w],s.vertexBuffers[w].data.length,l)),k[w]+=s.vertexBuffers[w].data.length);
for(w in s.indexBuffers)f[w].set(s.indexBuffers[w].data,k[w]),c(f[w],k[w],s.indexBuffers[w].data.length,q[r]),k[w]+=s.indexBuffers[w].data.length}k={info:{groups:m}};n.length&&(k.bones=n);return"undefined"!=typeof gl||e.only_data?new g.Mesh(d,f,k):{vertexBuffers:d,indexBuffers:f,info:{groups:m}}};Mesh.parsers={};Mesh.encoders={};Mesh.binary_file_formats={};Mesh.compressors={};Mesh.decompressors={};Mesh.fromURL=function(h,e,a,b){b=b||{};a=a||t.gl;var c=new g.Mesh(void 0,void 0,void 0,a);c.ready=!1;
a=h.lastIndexOf(".");var d=h.substr(a+1).toLowerCase();b.extension&&(d=b.extension);b.binary=Mesh.binary_file_formats[d];HttpRequest(h,null,function(a){c.parse(a,d,b);delete c.ready;e&&e.call(c,c,h,b)},function(a){e&&e(null)},b);return c};Mesh.prototype.parse=function(h,e,a){a=a||{};a.mesh=this;e=e.toLowerCase();var b=g.Mesh.parsers[e];if(b)return b.call(null,h,a);throw"GL.Mesh.parse: no parser found for format "+e;};Mesh.prototype.encode=function(h,e){h=h.toLowerCase();var a=g.Mesh.encoders[h];if(a)return a.call(null,
this,e);throw"GL.Mesh.encode: no encoder found for format "+h;};Mesh.getScreenQuad=function(h){h=h||t.gl;var e=h.meshes[":screen_quad"];if(e)return e;var e=new Float32Array([0,0,0,1,1,0,0,1,0,0,0,0,1,0,0,1,1,0]),a=new Float32Array([0,0,1,1,0,1,0,0,1,0,1,1]),e=new g.Mesh({vertices:e,coords:a},void 0,void 0,h);return h.meshes[":screen_quad"]=e};g.linearizeArray=R;g.Mesh.EXTENSION="wbin";g.Mesh.enable_wbin_compression=!0;K.DEFAULT_NORMAL=vec3.fromValues(0,1,0);K.DEFAULT_COORD=vec2.fromValues(0.5,0.5);
K.DEFAULT_COLOR=vec4.fromValues(1,1,1,1);K.prototype.resize=function(h){var e={};this._vertex_data=new Float32Array(3*h);e.vertices=this._vertex_data;normals&&(e.normals=this._normal_data=new Float32Array(3*h));coords&&(e.coords=this._coord_data=new Float32Array(2*h));colors&&(e.colors=this._color_data=new Float32Array(4*h));this.addBuffers(e);this.current_pos=0;this.max_size=h;this._must_update=!0};K.prototype.clear=function(){this.current_pos=0};K.prototype.addPoint=function(h,e,a,b){if(c>=this.max_size)return console.warn("DynamicMesh: not enough space, reserve more"),
!1;var c=this.current_pos++;this._vertex_data.set(h,3*c);this._normal_data&&this._normal_data.set(e||K.DEFAULT_NORMAL,3*c);this._coord_data&&this._coord_data.set(a||K.DEFAULT_COORD,2*c);this._color_data&&this._color_data.set(b||K.DEFAULT_COLOR,4*c);return this._must_update=!0};K.prototype.update=function(h){if(!this._must_update&&!h)return this.current_pos;this._must_update=!1;this.getBuffer("vertices").upload(gl.STREAM_DRAW);this._normal_data&&this.getBuffer("normal").upload(gl.STREAM_DRAW);this._coord_data&&
this.getBuffer("coord").upload(gl.STREAM_DRAW);this._color_data&&this.getBuffer("color").upload(gl.STREAM_DRAW);return this.current_pos};extendClass(K,Mesh);Mesh.plane=function(h,e){h=h||{};h.triangles=[];var a=h.detailX||h.detail||1,b=h.detailY||h.detail||1,c=h.width||h.size||1,d=h.height||h.size||1,f=h.xz,c=0.5*c,d=0.5*d,k=[],q=[],l=[],m=[],n=vec3.fromValues(0,0,1);f&&n.set([0,1,0]);for(var p=0;p<=b;p++)for(var r=p/b,v=0;v<=a;v++){var s=v/a;f?q.push((2*s-1)*c,0,-(2*r-1)*d):q.push((2*s-1)*c,(2*r-
1)*d,0);l.push(s,r);m.push(n[0],n[1],n[2]);v<a&&p<b&&(s=v+p*(a+1),f?(k.push(s+1,s+a+1,s),k.push(s+1,s+a+2,s+a+1)):(k.push(s,s+1,s+a+1),k.push(s+a+1,s+1,s+a+2)))}a=BBox.fromCenterHalfsize([0,0,0],f?[c,0,d]:[c,d,0]);return g.Mesh.load({vertices:q,normals:m,coords:l,triangles:k},{bounding:a},e)};Mesh.plane2D=function(h,e){var a=new Float32Array([-1,1,1,-1,1,1,-1,1,-1,-1,1,-1]),b=new Float32Array([0,1,1,0,1,1,0,1,0,0,1,0]);if(h&&h.size)for(var c=0.5*h.size,d=0;d<a.length;++d)a[d]*=c;return new g.Mesh({vertices2D:a,
coords:b},null,e)};Mesh.point=function(h){return new g.Mesh({vertices:[0,0,0]})};Mesh.cube=function(h,e){h=h||{};var a=0.5*(h.size||1),b={};b.vertices=new Float32Array([-1,1,-1,-1,-1,1,-1,1,1,-1,1,-1,-1,-1,-1,-1,-1,1,1,1,-1,1,1,1,1,-1,1,1,1,-1,1,-1,1,1,-1,-1,-1,1,1,1,-1,1,1,1,1,-1,1,1,-1,-1,1,1,-1,1,-1,1,-1,1,1,-1,1,-1,-1,-1,1,-1,1,-1,-1,-1,-1,-1,-1,1,-1,1,1,1,1,1,-1,-1,1,-1,-1,1,1,1,1,1,-1,-1,-1,1,-1,-1,1,-1,1,-1,-1,-1,1,-1,1,-1,-1,1]);for(var c=0,d=b.vertices.length;c<d;++c)b.vertices[c]*=a;b.normals=
new Float32Array([-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0]);b.coords=new Float32Array([0,1,1,0,1,1,0,1,0,0,1,0,1,1,0,1,0,0,1,1,0,0,1,0,0,1,1,0,1,1,0,1,0,0,1,0,1,1,0,1,0,0,1,1,0,0,1,0,0,1,1,0,1,1,0,1,0,0,1,0,1,1,0,1,0,0,1,1,0,0,1,0]);h.wireframe&&(b.wireframe=new Uint16Array([0,2,2,5,5,4,4,0,6,7,7,10,10,11,
11,6,0,6,2,7,5,10,4,11]));h.bounding=BBox.fromCenterHalfsize([0,0,0],[a,a,a]);return g.Mesh.load(b,h,e)};Mesh.box=function(h,e){h=h||{};var a=h.sizex||1,b=h.sizey||1,c=h.sizez||1,a=0.5*a,b=0.5*b,c=0.5*c,d={};d.vertices=new Float32Array([-1,1,-1,-1,-1,1,-1,1,1,-1,1,-1,-1,-1,-1,-1,-1,1,1,1,-1,1,1,1,1,-1,1,1,1,-1,1,-1,1,1,-1,-1,-1,1,1,1,-1,1,1,1,1,-1,1,1,-1,-1,1,1,-1,1,-1,1,-1,1,1,-1,1,-1,-1,-1,1,-1,1,-1,-1,-1,-1,-1,-1,1,-1,1,1,1,1,1,-1,-1,1,-1,-1,1,1,1,1,1,-1,-1,-1,1,-1,-1,1,-1,1,-1,-1,-1,1,-1,1,-1,
-1,1]);for(var f=0,k=d.vertices.length;f<k;f+=3)d.vertices[f]*=a,d.vertices[f+1]*=b,d.vertices[f+2]*=c;d.normals=new Float32Array([-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0]);d.coords=new Float32Array([0,1,1,0,1,1,0,1,0,0,1,0,1,1,0,1,0,0,1,1,0,0,1,0,0,1,1,0,1,1,0,1,0,0,1,0,1,1,0,1,0,0,1,1,0,0,1,0,0,1,1,0,1,1,
0,1,0,0,1,0,1,1,0,1,0,0,1,1,0,0,1,0]);h.wireframe&&(d.wireframe=new Uint16Array([0,2,2,5,5,4,4,0,6,7,7,10,10,11,11,6,0,6,2,7,5,10,4,11]));h.bounding=BBox.fromCenterHalfsize([0,0,0],[a,b,c]);return g.Mesh.load(d,h,e)};Mesh.circle=function(h,e){h=h||{};var a=h.size||h.radius||1,b=Math.ceil(h.slices||24),c=h.xz||!1,d=h.empty||!1;3>b&&(b=3);var f=2*Math.PI/b,k=vec3.create(),q=vec3.create(),l=vec3.fromValues(0,0,1),m=vec2.fromValues(0.5,0.5),n=vec2.create();c&&l.set([0,1,0]);var p=c?2:1,r=new Float32Array(3*
(b+1)),v=new Float32Array(3*(b+1)),s=new Float32Array(2*(b+1)),u=null;r.set(k,0);v.set(l,0);s.set(m,0);for(k=m=u=0;k<b;++k)u=Math.sin(f*k),m=Math.cos(f*k),q[0]=u*a,q[p]=m*a,n[0]=0.5*u+0.5,n[1]=0.5*m+0.5,r.set(q,3*k+3),v.set(l,3*k+3),s.set(n,2*k+2);if(d)r=r.subarray(3),v=r.subarray(3),s=r.subarray(2),u=null;else{u=new Uint16Array(3*b);d=2;f=1;c&&(d=1,f=2);for(k=0;k<b-1;++k)u[3*k]=0,u[3*k+1]=k+d,u[3*k+2]=k+f;u[3*k]=0;c?(u[3*k+1]=k+1,u[3*k+2]=1):(u[3*k+1]=1,u[3*k+2]=k+1)}h.bounding=BBox.fromCenterHalfsize([0,
0,0],c?[a,0,a]:[a,a,0]);a={vertices:r,normals:v,coords:s,triangles:u};if(h.wireframe){c=new Uint16Array(2*b);for(k=0;k<b;k++)c[2*k]=k,c[2*k+1]=k+1;c[0]=b;a.wireframe=c}return g.Mesh.load(a,h,e)};Mesh.ring=function(h,e){h=h||{};var a=h.size||h.radius||1,b=h.thickness||0.1*a,c=Math.ceil(h.slices||24),d=h.xz||!1,f=h.empty||!1;3>c&&(c=3);var k=2*Math.PI/c;vec3.create();var q=vec3.create(),l=vec3.create(),m=vec3.fromValues(0,0,1);vec2.fromValues(0.5,0.5);var n=vec2.create();d&&m.set([0,1,0]);for(var p=
d?2:1,r=new Float32Array(3*(2*c+2)),v=new Float32Array(3*(2*c+2)),s=new Float32Array(2*(2*c+2)),u=null,x=u=0,w=0;w<=c;++w)u=Math.sin(k*w),x=Math.cos(k*w),q[0]=u*(a-b),q[p]=x*(a-b),n[0]=w/c,n[1]=0,r.set(q,6*w),v.set(m,6*w),s.set(n,4*w),l[0]=u*(a+b),l[p]=x*(a+b),n[1]=1,r.set(l,6*w+3),v.set(m,6*w+3),s.set(n,4*w+2);if(f)r=r.subarray(3),v=r.subarray(3),s=r.subarray(2),u=null;else for(u=new Uint16Array(6*c),f=2,k=1,d&&(f=1,k=2),w=0;w<c;++w)u[6*w]=2*w,u[6*w+1]=2*w+f,u[6*w+2]=2*w+k,u[6*w+3]=2*w+k,u[6*w+4]=
2*w+f,u[6*w+5]=2*w+3;h.bounding=BBox.fromCenterHalfsize([0,0,0],d?[a+b,0,a+b]:[a+b,a+b,0]);a={vertices:r,normals:v,coords:s,triangles:u};if(h.wireframe){b=new Uint16Array(4*c);for(w=0;w<c;w++)b[4*w]=2*w,b[4*w+1]=2*w+2,b[4*w+2]=2*w+1,b[4*w+3]=2*w+3;a.wireframe=b}return g.Mesh.load(a,h,e)};Mesh.cylinder=function(h,e){h=h||{};for(var a=h.radius||h.size||1,b=h.height||h.size||2,c=h.subdivisions||64,d=new Float32Array(36*c),f=new Float32Array(36*c),k=new Float32Array(24*c),g=2*Math.PI/c,l=null,m=0;m<c;++m){var n=
m*g,l=[Math.sin(n),0,Math.cos(n)];d.set([l[0]*a,0.5*b,l[2]*a],18*m);f.set(l,18*m);k.set([m/c,1],12*m);l=[Math.sin(n),0,Math.cos(n)];d.set([l[0]*a,-0.5*b,l[2]*a],18*m+3);f.set(l,18*m+3);k.set([m/c,0],12*m+2);l=[Math.sin(n+g),0,Math.cos(n+g)];d.set([l[0]*a,-0.5*b,l[2]*a],18*m+6);f.set(l,18*m+6);k.set([(m+1)/c,0],12*m+4);l=[Math.sin(n+g),0,Math.cos(n+g)];d.set([l[0]*a,0.5*b,l[2]*a],18*m+9);f.set(l,18*m+9);k.set([(m+1)/c,1],12*m+6);l=[Math.sin(n),0,Math.cos(n)];d.set([l[0]*a,0.5*b,l[2]*a],18*m+12);f.set(l,
18*m+12);k.set([m/c,1],12*m+8);l=[Math.sin(n+g),0,Math.cos(n+g)];d.set([l[0]*a,-0.5*b,l[2]*a],18*m+15);f.set(l,18*m+15);k.set([(m+1)/c,0],12*m+10)}var l=18*m,p=12*m;if(!1===h.caps)d=d.subarray(0,l),f=f.subarray(0,l),k=k.subarray(0,p);else for(var r=vec3.fromValues(0,0.5*b,0),v=vec3.fromValues(0,-0.5*b,0),s=vec3.fromValues(0,1,0),u=vec3.fromValues(0,-1,0),m=0;m<c;++m){var n=m*g,x=vec3.fromValues(Math.sin(n),0,Math.cos(n)),n=vec3.fromValues(Math.sin(n+g),0,Math.cos(n+g));d.set([x[0]*a,0.5*b,x[2]*a],
l+18*m);f.set(s,l+18*m);k.set([0.5*-x[0]+0.5,0.5*x[2]+0.5],p+12*m);d.set([n[0]*a,0.5*b,n[2]*a],l+18*m+3);f.set(s,l+18*m+3);k.set([0.5*-n[0]+0.5,0.5*n[2]+0.5],p+12*m+2);d.set(r,l+18*m+6);f.set(s,l+18*m+6);k.set([0.5,0.5],p+12*m+4);d.set([n[0]*a,-0.5*b,n[2]*a],l+18*m+9);f.set(u,l+18*m+9);k.set([0.5*n[0]+0.5,0.5*n[2]+0.5],p+12*m+6);d.set([x[0]*a,-0.5*b,x[2]*a],l+18*m+12);f.set(u,l+18*m+12);k.set([0.5*x[0]+0.5,0.5*x[2]+0.5],p+12*m+8);d.set(v,l+18*m+15);f.set(u,l+18*m+15);k.set([0.5,0.5],p+12*m+10)}c=
{vertices:d,normals:f,coords:k};h.bounding=BBox.fromCenterHalfsize([0,0,0],[a,0.5*b,a]);h.info={groups:[]};!1!==h.caps&&(h.info.groups.push({name:"side",start:0,length:l/3}),h.info.groups.push({name:"caps",start:l/3,length:(d.length-l)/3}));return Mesh.load(c,h,e)};Mesh.cone=function(h,e){h=h||{};for(var a=h.radius||h.size||1,b=h.height||h.size||2,c=h.subdivisions||64,d=new Float32Array(18*c),f=new Float32Array(18*c),k=new Float32Array(12*c),g=2*Math.PI/c,l=null,m=a/b,n=0;n<c;++n){var p=n*g,l=[Math.sin(p+
0.5*g),m,Math.cos(p+0.5*g)];vec3.normalize(l,l);d.set([0,b,0],18*n);f.set(l,18*n);k.set([n/c,1],12*n);l=[Math.sin(p),m,Math.cos(p)];d.set([l[0]*a,0,l[2]*a],18*n+3);vec3.normalize(l,l);f.set(l,18*n+3);k.set([n/c,0],12*n+2);l=[Math.sin(p+g),m,Math.cos(p+g)];d.set([l[0]*a,0,l[2]*a],18*n+6);vec3.normalize(l,l);f.set(l,18*n+6);k.set([(n+1)/c,0],12*n+4)}l=vec3.fromValues(0,0,0);m=vec3.fromValues(0,-1,0);for(n=0;n<c;++n){var p=n*g,r=vec3.fromValues(Math.sin(p),0,Math.cos(p)),p=vec3.fromValues(Math.sin(p+
g),0,Math.cos(p+g));d.set([p[0]*a,0,p[2]*a],18*n+9);f.set(m,18*n+9);k.set([0.5*p[0]+0.5,0.5*p[2]+0.5],12*n+6);d.set([r[0]*a,0,r[2]*a],18*n+12);f.set(m,18*n+12);k.set([0.5*r[0]+0.5,0.5*r[2]+0.5],12*n+8);d.set(l,18*n+15);f.set(m,18*n+15);k.set([0.5,0.5],12*n+10)}c={vertices:d,normals:f,coords:k};h.bounding=BBox.fromCenterHalfsize([0,0.5*b,0],[a,0.5*b,a]);return Mesh.load(c,h,e)};Mesh.torus=function(h,e){h=h||{};var a=h.outerradius||h.radius||1,b=Math.ceil(h.outerslices||h.slices||24),c=h.innerradius||
0.1*a,d=Math.ceil(h.innerslices||b),f=h.angle||2*Math.PI,k=2*Math.PI/d,q=f/b;vec3.create();var l=vec3.create(),m=vec3.fromValues(0,0,1);vec2.fromValues(0.5,0.5);for(var n=vec2.create(),f=f==2*Math.PI,p=new Float32Array(3*d),r=new Float32Array(3*d),v=0,s=0,u=0;u<d;++u)v=Math.sin(k*u),s=Math.cos(k*u),l[0]=v*c,l[1]=s*c,n[0]=0.5*v+0.5,n[1]=0.5*s+0.5,p.set(l,3*u),vec3.normalize(m,l),r.set(m,3*u);var k=new Float32Array(3*b*d),n=new Float32Array(3*b*d),v=new Float32Array(2*b*d),s=null,x=mat4.create(),w=
vec3.fromValues(-a,0,0),M=vec3.fromValues(0,1,0);vec3.create();for(var s=[],z=d,u=0;u<b;++u){mat4.identity(x);mat4.rotate(x,x,u*q,M);mat4.translate(x,x,w);var t=u*d,z=d;u>=b-1&&(z=(b-1)*-d,f||(z=0));for(var J=0;J<d;++J){var Q=p.subarray(3*J,3*J+3),y=r.subarray(3*J,3*J+3);mat4.multiplyVec3(l,x,Q);mat4.rotateVec3(m,x,y);k.set(l,3*J+3*u*d);n.set(m,3*J+3*u*d);v.set([u/b,J/d],2*J+2*u*d);Q=t+J;y=t+(J+1)%d;s.push(y,Q,Q+z,y,Q+z,y+z)}}a=c+a;h.bounding=BBox.fromCenterHalfsize([0,0,0],[a,a,c]);return g.Mesh.load({vertices:k,
normals:n,coords:v,triangles:s},h,e)};Mesh.sphere=function(h,e){h=h||{};for(var a=h.radius||h.size||1,b=h.lat||h.subdivisions||16,c=h["long"]||h.subdivisions||16,d=new Float32Array((b+1)*(c+1)*3),f=new Float32Array((b+1)*(c+1)*3),k=new Float32Array((b+1)*(c+1)*2),q=new Uint16Array(b*c*6),l=h.hemi?0.5*Math.PI:Math.PI,m=0,n=0,p=0;p<=b;p++)for(var r=p*l/b,v=Math.sin(r),s=Math.cos(r),r=0;r<=c;r++){var u=2*r*Math.PI/c,x=Math.sin(u),u=Math.cos(u)*v,w=s,x=x*v,t=1-r/c,z=1-p/b;d.set([a*u,a*w,a*x],m);f.set([u,
w,x],m);k.set([t,z],n);m+=3;n+=2}for(p=m=0;p<b;p++)for(r=0;r<c;r++)l=p*(c+1)+r,n=l+c+1,q.set([n,l,l+1],m),q.set([n+1,n,l+1],m+3),m+=6;d={vertices:d,normals:f,coords:k,triangles:q};if(h.wireframe){f=new Uint16Array(c*b*4);for(m=k=0;m<b;m++){for(q=0;q<c;q++)f[k]=m*(c+1)+q,f[k+1]=m*(c+1)+q+1,k+=2;f[k-2*c]=m*(c+1)+q}for(m=0;m<c;m++)for(q=0;q<b;q++)f[k]=q*(c+1)+m,f[k+1]=(q+1)*(c+1)+m,k+=2;d.wireframe=f}h.bounding=h.hemi?BBox.fromCenterHalfsize([0,0.5*a,0],[a,0.5*a,a],a):BBox.fromCenterHalfsize([0,0,0],
[a,a,a],a);return g.Mesh.load(d,h,e)};Mesh.grid=function(h,e){h=h||{};var a=h.lines||11;0>a&&(a=1);for(var b=h.size||10,c=new Float32Array(12*a),d=0.5*b,f=0,k=-d,b=b/(a-1),q=0;q<a;q++)c[f]=k,c[f+2]=-d,c[f+3]=k,c[f+5]=d,c[f+6]=d,c[f+8]=k,c[f+9]=-d,c[f+11]=k,k+=b,f+=12;return new g.Mesh({vertices:c},h,e)};Mesh.icosahedron=function(h,e){function a(a,c){var d=l[a]<l[c]?l[a]+":"+l[c]:l[c]+":"+l[a],e=s[d];if(e)return e;e=f.length/3;f.push(0.5*(f[3*l[a]]+f[3*l[c]]),0.5*(f[3*l[a]+1]+f[3*l[c]+1]),0.5*(f[3*
l[a]+2]+f[3*l[c]+2]));var h=Math.sqrt(f[3*e]*f[3*e]+f[3*e+1]*f[3*e+1]+f[3*e+2]*f[3*e+2]),g=f[3*e]/h,m=f[3*e+1]/h,n=f[3*e+2]/h;k.push(g,m,n);q.push(Math.atan2(g,n)/Math.PI*0.5,Math.acos(m)/Math.PI);f[3*e]*=b/h;f[3*e+1]*=b/h;f[3*e+2]*=b/h;return s[d]=e}h=h||{};var b=h.radius||h.size||1,c=void 0===h.subdivisions?0:h.subdivisions;6<c&&(c=6);for(var d=(1+Math.sqrt(5))/2,f=[-1,d,0,1,d,0,-1,-d,0,1,-d,0,0,-1,d,0,1,d,0,-1,-d,0,1,-d,d,0,-1,d,0,1,-d,0,-1,-d,0,1],k=[],q=[],l=[0,11,5,0,5,1,0,1,7,0,7,10,0,10,11,
1,5,9,5,11,4,11,10,2,10,7,6,7,1,8,3,9,4,3,4,2,3,2,6,3,6,8,3,8,9,4,9,5,2,4,11,6,2,10,8,6,7,9,8,1],d=f.length,m=0;m<d;m+=3){var n=Math.sqrt(f[m]*f[m]+f[m+1]*f[m+1]+f[m+2]*f[m+2]),p=f[m]/n,r=f[m+1]/n,v=f[m+2]/n;k.push(p,r,v);q.push(Math.atan2(p,v),Math.acos(r));f[m]*=b/n;f[m+1]*=b/n;f[m+2]*=b/n}for(var s={},n=0;n<c;++n){p=[];d=l.length;for(m=0;m<d;m+=3){var r=a(m,m+1),v=a(m+1,m+2),u=a(m+2,m);p.push(l[m],r,u);p.push(l[m+1],v,r);p.push(l[m+2],u,v);p.push(r,v,u)}l=p}h.bounding=BBox.fromCenterHalfsize([0,
0,0],[b,b,b],b);return new g.Mesh.load({vertices:f,coords:q,normals:k,triangles:l},h,e)};Mesh.shape=function(h,e,a){function b(a,b){var c=a[b+1];a[b+1]=a[b+2];a[b+2]=-c}e=e||{};if("undefined"===typeof earcut)throw"To use GL.Mesh.shape you must download and include earcut.js (do not link it directly!): https://raw.githubusercontent.com/mapbox/earcut/master/src/earcut.js";if(!h||!h.length||1==h.length%2)throw"GL.Mesh.shape line missing, must be an array of 2D vertices";var c=e.extrude||0;h[0].constructor===
Array&&(h=earcut.flatten(h));var d=earcut(h).reverse();console.log(d);for(var f=[],k=[],q=[],l=[h[0],h[1]],m=[h[0],h[1]],n=0;n<h.length;n+=2)l[0]=Math.min(l[0],h[n]),l[1]=Math.max(l[1],h[n]),m[0]=Math.min(m[0],h[n+1]),m[1]=Math.max(m[1],h[n+1]);var p=l[1]-l[0],r=m[1]-m[0],v=[],s=null;if(c){for(var s=[],u=vec3.create(),x=h.length,n=0;n<h.length;n+=2){var w=h[n],t=h[n+1],z=h[(n+2)%x],C=h[(n+3)%x],J=f.length/3;f.push(w,0.5*c,t);f.push(w,-0.5*c,t);f.push(z,0.5*c,C);f.push(z,-0.5*c,C);vec3.normalize(u,
vec3.cross(u,[0,1,0],[z-w,0,C-t]));k.push(u[0],u[1],u[2],u[0],u[1],u[2],u[0],u[1],u[2],u[0],u[1],u[2]);var y=(w-l[0])/p,B=(t-m[0])/r,w=(z-l[0])/p,t=(C-m[0])/r;q.push(y,B,y,B,w,t,w,t);s.push(J,J+2,J+1,J+2,J+3,J+1)}v.push({name:"side",start:0,length:s.length});u=f.length/3;x=s.length;for(n=0;n<h.length;n+=2)w=h[n],t=h[n+1],y=(w-l[0])/p,B=(t-m[0])/r,f.push(w,0.5*c,t),f.push(w,-0.5*c,t),k.push(0,1,0,0,-1,0),q.push(y,B,y,B);for(n=0;n<d.length;n+=3)s.push(u+2*d[n],u+2*d[n+1],u+2*d[n+2]),s.push(u+2*d[n]+
1,u+2*d[n+2]+1,u+2*d[n+1]+1);v.push({name:"caps",start:x,length:s.length});e.bounding=BBox.fromCenterHalfsize([0.5*(l[0]+l[1]),0,0.5*(m[0]+m[1])],[0.5*p,0.5*c,0.5*r],vec2.len([0.5*p,0.5*c,0.5*r]))}else{for(n=0;n<h.length;n+=2)f.push(h[n],0,h[n+1]),k.push(0,1,0),q.push((h[n]-l[0])/p,(h[n+1]-m[0])/r);s=d;v.push({name:"side",start:0,length:s.length});e.bounding=BBox.fromCenterHalfsize([0.5*(l[0]+l[1]),0,0.5*(m[0]+m[1])],[0.5*p,0,0.5*r],vec2.len([0.5*p,0,0.5*r]))}if(e.xy){for(n=0;n<f.length;n+=3)b(f,
n),b(k,n);e.bounding=c?BBox.fromCenterHalfsize([0.5*(l[0]+l[1]),0.5*(m[0]+m[1]),0],[0.5*p,0.5*r,0.5*c],vec2.len([0.5*p,0.5*r,0.5*c])):BBox.fromCenterHalfsize([0.5*(l[0]+l[1]),0.5*(m[0]+m[1]),0],[0.5*p,0.5*r,0],vec2.len([0.5*p,0.5*r,0]))}e.info={groups:v};return new g.Mesh.load({vertices:f,coords:q,normals:k,triangles:s},e,a)};t.Texture=g.Texture=function e(a,b,c,d){function f(a){return a.constructor!==Array?a:k==g.FLOAT?new Float32Array(a):k==g.HALF_FLOAT_OES?new Uint16Array(a):new Uint8Array(a)}
c=c||{};this.gl=d=d||t.gl;this._context_id=d.context_id;a=parseInt(a);b=parseInt(b);g.debug&&console.log("GL.Texture created: ",a,b);this.handler=d.createTexture();this.width=a;this.height=b;c.depth&&(this.depth=c.depth);this.texture_type=c.texture_type||d.TEXTURE_2D;this.format=c.format||e.DEFAULT_FORMAT;this.internalFormat=c.internalFormat;this.type=c.type||e.DEFAULT_TYPE;this.magFilter=c.magFilter||c.filter||e.DEFAULT_MAG_FILTER;this.minFilter=c.minFilter||c.filter||e.DEFAULT_MIN_FILTER;this.wrapS=
c.wrap||c.wrapS||e.DEFAULT_WRAP_S;this.wrapT=c.wrap||c.wrapT||e.DEFAULT_WRAP_T;this.data=null;e.MAX_TEXTURE_IMAGE_UNITS||(e.MAX_TEXTURE_IMAGE_UNITS=d.getParameter(d.MAX_TEXTURE_IMAGE_UNITS));this.has_mipmaps=!1;if(this.format==d.DEPTH_COMPONENT&&1==d.webgl_version&&!d.extensions.WEBGL_depth_texture)throw"Depth Texture not supported";if(this.type==d.FLOAT&&!d.extensions.OES_texture_float&&1==d.webgl_version)throw"Float Texture not supported";if(this.type==d.HALF_FLOAT_OES){if(!d.extensions.OES_texture_half_float&&
1==d.webgl_version)throw"Half Float Texture extension not supported.";1<d.webgl_version&&(console.warn("using HALF_FLOAT_OES in WebGL2 is deprecated, suing HALF_FLOAT instead"),this.type=this.format==d.RGB?d.RGB16F:d.RGBA16F)}if(!(isPowerOfTwo(this.width)&&isPowerOfTwo(this.height)||(this.minFilter==d.NEAREST||this.minFilter==d.LINEAR)&&this.wrapS==d.CLAMP_TO_EDGE&&this.wrapT==d.CLAMP_TO_EDGE))if(c.ignore_pot)this.minFilter=this.magFilter=d.LINEAR,this.wrapS=this.wrapT=d.CLAMP_TO_EDGE;else throw"Cannot use texture-wrap or mipmaps in Non-Power-of-Two textures";
if(a&&b){this.internalFormat||this.computeInternalFormat();d.activeTexture(d.TEXTURE0+e.MAX_TEXTURE_IMAGE_UNITS-1);d.bindTexture(this.texture_type,this.handler);d.texParameteri(this.texture_type,d.TEXTURE_MAG_FILTER,this.magFilter);d.texParameteri(this.texture_type,d.TEXTURE_MIN_FILTER,this.minFilter);d.texParameteri(this.texture_type,d.TEXTURE_WRAP_S,this.wrapS);d.texParameteri(this.texture_type,d.TEXTURE_WRAP_T,this.wrapT);c.anisotropic&&d.extensions.EXT_texture_filter_anisotropic&&d.texParameterf(g.TEXTURE_2D,
d.extensions.EXT_texture_filter_anisotropic.TEXTURE_MAX_ANISOTROPY_EXT,c.anisotropic);var k=this.type,q=c.pixel_data;if(q&&!q.buffer){if(this.texture_type==g.TEXTURE_CUBE_MAP)if(q[0].constructor===Number)q=f(q),q=[q,q,q,q,q,q];else for(var l=0;l<q.length;++l)q[l]=f(q[l]);else q=f(q);this.data=q}e.setUploadOptions(c);if(this.texture_type==g.TEXTURE_2D)d.texImage2D(g.TEXTURE_2D,0,this.internalFormat,a,b,0,this.format,this.type,q||null),g.isPowerOfTwo(a)&&g.isPowerOfTwo(b)&&c.minFilter&&c.minFilter!=
d.NEAREST&&c.minFilter!=d.LINEAR&&(d.generateMipmap(this.texture_type),this.has_mipmaps=!0);else if(this.texture_type==g.TEXTURE_CUBE_MAP)for(a=a*a*(this.format==g.RGBA?4:3),l=0;6>l;++l)(b=q)&&(b.constructor===Array?b=b[l]:b.subarray(a*l,a*(l+1))),d.texImage2D(d.TEXTURE_CUBE_MAP_POSITIVE_X+l,0,this.internalFormat,this.width,this.height,0,this.format,this.type,b||null);else if(this.texture_type==g.TEXTURE_3D){if(1==this.gl.webgl_version)throw"TEXTURE_3D not supported in WebGL 1. Enable WebGL 2 in the context by passing webgl2:true to the context";
if(!c.depth)throw"3d texture depth must be set in the options.depth";d.pixelStorei(d.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1);d.pixelStorei(d.UNPACK_FLIP_Y_WEBGL,!1);d.texImage3D(g.TEXTURE_3D,0,this.internalFormat,a,b,c.depth,0,this.format,this.type,q||null)}d.bindTexture(this.texture_type,null);d.activeTexture(d.TEXTURE0)}};Texture.DEFAULT_TYPE=g.UNSIGNED_BYTE;Texture.DEFAULT_FORMAT=g.RGBA;Texture.DEFAULT_MAG_FILTER=g.LINEAR;Texture.DEFAULT_MIN_FILTER=g.LINEAR;Texture.DEFAULT_WRAP_S=g.CLAMP_TO_EDGE;Texture.DEFAULT_WRAP_T=
g.CLAMP_TO_EDGE;Texture.EXTENSION="png";Texture.framebuffer=null;Texture.renderbuffer=null;Texture.loading_color=new Uint8Array([0,0,0,0]);Texture.use_renderbuffer_pool=!0;Texture.prototype.computeInternalFormat=function(){this.internalFormat=this.format;if(this.format==g.DEPTH_COMPONENT)if(this.minFilter=g.NEAREST,2==gl.webgl_version)if(this.type==g.UNSIGNED_SHORT)this.internalFormat=g.DEPTH_COMPONENT16;else if(this.type==g.UNSIGNED_INT)this.internalFormat=g.DEPTH_COMPONENT24;else if(this.type==
g.FLOAT)this.internalFormat=g.DEPTH_COMPONENT32F;else throw"unsupported type for a depth texture";else{if(1==gl.webgl_version){if(this.type==g.FLOAT)throw"WebGL 1.0 does not support float depth textures";this.internalFormat=g.DEPTH_COMPONENT}}else this.format==gl.RGBA&&(2==gl.webgl_version?this.type==g.FLOAT?this.internalFormat=g.RGBA32F:this.type==g.HALF_FLOAT?this.internalFormat=g.RGBA16F:this.type==g.HALF_FLOAT_OES&&(console.warn("webgl 2 does not use HALF_FLOAT_OES, converting to HALF_FLOAT"),
this.type=g.HALF_FLOAT,this.internalFormat=g.RGBA16F):1==gl.webgl_version&&this.type==g.HALF_FLOAT&&(console.warn("webgl 1 does not use HALF_FLOAT, converting to HALF_FLOAT_OES"),this.type=g.HALF_FLOAT_OES))};Texture.prototype.delete=function(){gl.deleteTexture(this.handler);this.handler=null};Texture.prototype.getProperties=function(){return{width:this.width,height:this.height,type:this.type,format:this.format,texture_type:this.texture_type,magFilter:this.magFilter,minFilter:this.minFilter,wrapS:this.wrapS,
wrapT:this.wrapT}};Texture.prototype.hasSameProperties=function(e){return e?e.width==this.width&&e.height==this.height&&e.type==this.type&&e.format==this.format&&e.texture_type==this.texture_type:!1};Texture.prototype.hasSameSize=function(e){return e?e.width==this.width&&e.height==this.height:!1};Texture.prototype.toJSON=function(){return""};Texture.isDepthSupported=function(){return null!=gl.extensions.WEBGL_depth_texture};Texture.prototype.bind=function(e){void 0==e&&(e=0);var a=this.gl;a.activeTexture(a.TEXTURE0+
e);a.bindTexture(this.texture_type,this.handler);return e};Texture.prototype.unbind=function(e){void 0===e&&(e=0);var a=this.gl;a.activeTexture(a.TEXTURE0+e);a.bindTexture(this.texture_type,null)};Texture.prototype.setParameter=function(e,a){this.bind(0);this.gl.texParameteri(this.texture_type,e,a);switch(e){case this.gl.TEXTURE_MAG_FILTER:this.magFilter=a;break;case this.gl.TEXTURE_MIN_FILTER:this.minFilter=a;break;case this.gl.TEXTURE_WRAP_S:this.wrapS=a;break;case this.gl.TEXTURE_WRAP_T:this.wrapT=
a}};Texture.setUploadOptions=function(e,a){a=a||t.gl;Texture.disable_deprecated||(e?(a.pixelStorei(a.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!!e.premultiply_alpha),a.pixelStorei(a.UNPACK_FLIP_Y_WEBGL,!e.no_flip)):(a.pixelStorei(a.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),a.pixelStorei(a.UNPACK_FLIP_Y_WEBGL,!0)));a.pixelStorei(a.UNPACK_ALIGNMENT,1)};Texture.flipYData=function(e,a,b,c){var d=new e.constructor(a*c),f=0,k=a*(b-1)*c;b=Math.floor(0.5*b);for(var g=0;g<b;++g){var l=e.subarray(f,f+a*c),m=e.subarray(k,k+a*
c);d.set(l);l.set(m);m.set(d);f+=a*c;k-=a*c;if(f>k)break}};Texture.prototype.uploadImage=function(e,a){this.bind();var b=this.gl;if(!e)throw"uploadImage parameter must be Image";Texture.setUploadOptions(a,b);try{a&&a.subimage?1==b.webgl_version?b.texSubImage2D(b.TEXTURE_2D,0,0,0,this.format,this.type,e):b.texSubImage2D(b.TEXTURE_2D,0,0,0,e.videoWidth||e.width,e.videoHeight||e.height,this.format,this.type,e):(b.texImage2D(b.TEXTURE_2D,0,this.format,this.format,this.type,e),this.width=e.videoWidth||
e.width,this.height=e.videoHeight||e.height),this.data=e}catch(c){console.error(c);if("file:"==location.protocol)throw'image not loaded for security reasons (serve this page over "http://" instead)';throw"image not loaded for security reasons (image must originate from the same domain as this page or use Cross-Origin Resource Sharing)";}this.minFilter&&this.minFilter!=b.NEAREST&&this.minFilter!=b.LINEAR&&(b.generateMipmap(this.texture_type),this.has_mipmaps=!0);b.bindTexture(this.texture_type,null)};
Texture.prototype.uploadData=function(e,a,b){a=a||{};if(!e)throw"no data passed";var c=this.gl;this.bind();Texture.setUploadOptions(a,c);var d=a.mipmap_level||0,f=this.width,k=this.height,f=f>>d,k=k>>d,q=this.internalFormat||this.format;this.type==g.HALF_FLOAT_OES&&e.constructor===Float32Array&&console.warn("cannot uploadData to a HALF_FLOAT texture from a Float32Array, must be Uint16Array. To upload it we recomment to create a FLOAT texture, upload data there and copy to your HALF_FLOAT.");if(this.texture_type==
g.TEXTURE_2D)1==c.webgl_version?e.buffer&&e.buffer.constructor==ArrayBuffer?c.texImage2D(this.texture_type,d,q,f,k,0,this.format,this.type,e):c.texImage2D(this.texture_type,d,q,this.format,this.type,e):2==c.webgl_version&&c.texImage2D(this.texture_type,d,q,f,k,0,this.format,this.type,e);else if(this.texture_type==g.TEXTURE_3D)c.pixelStorei(c.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),c.pixelStorei(c.UNPACK_FLIP_Y_WEBGL,!1),c.texImage3D(this.texture_type,d,q,f,k,this.depth>>d,0,this.format,this.type,e);else if(this.texture_type==
g.TEXTURE_CUBE_MAP)c.texImage2D(c.TEXTURE_CUBE_MAP_POSITIVE_X+(a.cubemap_face||0),d,q,f,k,0,this.format,this.type,e);else throw"cannot uploadData for this texture type";this.data=e;!b&&this.minFilter&&this.minFilter!=c.NEAREST&&this.minFilter!=c.LINEAR&&(c.generateMipmap(this.texture_type),this.has_mipmaps=!0);c.bindTexture(this.texture_type,null)};Texture.cubemap_camera_parameters=[{type:"posX",dir:vec3.fromValues(1,0,0),up:vec3.fromValues(0,1,0),right:vec3.fromValues(0,0,-1)},{type:"negX",dir:vec3.fromValues(-1,
0,0),up:vec3.fromValues(0,1,0),right:vec3.fromValues(0,0,1)},{type:"posY",dir:vec3.fromValues(0,1,0),up:vec3.fromValues(0,0,-1),right:vec3.fromValues(1,0,0)},{type:"negY",dir:vec3.fromValues(0,-1,0),up:vec3.fromValues(0,0,1),right:vec3.fromValues(1,0,0)},{type:"posZ",dir:vec3.fromValues(0,0,1),up:vec3.fromValues(0,1,0),right:vec3.fromValues(1,0,0)},{type:"negZ",dir:vec3.fromValues(0,0,-1),up:vec3.fromValues(0,1,0),right:vec3.fromValues(-1,0,0)}];Texture.prototype.drawTo=function(e,a){function b(){6E4<=
g.getTime()-this.time?(c.deleteRenderbuffer(c._renderbuffers_pool[m]),delete c._renderbuffers_pool[m]):setTimeout(b.bind(this),6E4)}var c=this.gl,d=c.getViewport(),f=g.getTime(),k=c.getParameter(c.FRAMEBUFFER_BINDING),q=c._framebuffer=c._framebuffer||c.createFramebuffer();c.bindFramebuffer(c.FRAMEBUFFER,q);var l=null;if(Texture.use_renderbuffer_pool){c._renderbuffers_pool||(c._renderbuffers_pool={});var m=this.width+":"+this.height;c._renderbuffers_pool[m]?(l=c._renderbuffers_pool[m],l.time=f,c.bindRenderbuffer(c.RENDERBUFFER,
l)):(c._renderbuffers_pool[m]=l=c.createRenderbuffer(),l.time=f,l.width=this.width,l.height=this.height,c.bindRenderbuffer(c.RENDERBUFFER,l),setTimeout(b.bind(l),6E4))}else l=c._renderbuffer=c._renderbuffer||c.createRenderbuffer(),l.width=this.width,l.height=this.height,c.bindRenderbuffer(c.RENDERBUFFER,l);this.format===c.DEPTH_COMPONENT?c.renderbufferStorage(c.RENDERBUFFER,c.RGBA4,this.width,this.height):c.renderbufferStorage(c.RENDERBUFFER,c.DEPTH_COMPONENT16,this.width,this.height);c.viewport(0,
0,this.width,this.height);c._current_texture_drawto=this;c._current_fbo_color=q;c._current_fbo_depth=l;if(this.texture_type==c.TEXTURE_2D)this.format!==c.DEPTH_COMPONENT?(c.framebufferTexture2D(c.FRAMEBUFFER,c.COLOR_ATTACHMENT0,c.TEXTURE_2D,this.handler,0),c.framebufferRenderbuffer(c.FRAMEBUFFER,c.DEPTH_ATTACHMENT,c.RENDERBUFFER,l)):(c.framebufferRenderbuffer(c.FRAMEBUFFER,c.COLOR_ATTACHMENT0,c.RENDERBUFFER,l),c.framebufferTexture2D(c.FRAMEBUFFER,c.DEPTH_ATTACHMENT,c.TEXTURE_2D,this.handler,0)),e(this,
a);else if(this.texture_type==c.TEXTURE_CUBE_MAP)for(this.format!==c.DEPTH_COMPONENT?c.framebufferRenderbuffer(c.FRAMEBUFFER,c.DEPTH_ATTACHMENT,c.RENDERBUFFER,l):c.framebufferRenderbuffer(c.FRAMEBUFFER,c.COLOR_ATTACHMENT0,c.RENDERBUFFER,l),f=0;6>f;f++)this.format!==c.DEPTH_COMPONENT?c.framebufferTexture2D(c.FRAMEBUFFER,c.COLOR_ATTACHMENT0,c.TEXTURE_CUBE_MAP_POSITIVE_X+f,this.handler,0):c.framebufferTexture2D(c.FRAMEBUFFER,c.DEPTH_ATTACHMENT,c.TEXTURE_CUBE_MAP_POSITIVE_X+f,this.handler,0),e(this,f,
a);this.data=null;c._current_texture_drawto=null;c._current_fbo_color=null;c._current_fbo_depth=null;c.bindFramebuffer(c.FRAMEBUFFER,k);c.bindRenderbuffer(c.RENDERBUFFER,null);c.viewport(d[0],d[1],d[2],d[3]);return this};Texture.prototype.copyTo=function(e,a,b){var c=this.gl;if(!e)throw"target_texture required";var d=c.getParameter(c.FRAMEBUFFER_BINDING),f=c.getViewport();a||(a=this.texture_type==c.TEXTURE_2D?g.Shader.getScreenShader():g.Shader.getCubemapCopyShader());c.disable(c.BLEND);c.disable(c.DEPTH_TEST);
a&&b&&a.uniforms(b);b=c.__copy_fbo;b||(b=c.__copy_fbo=c.createFramebuffer());c.bindFramebuffer(c.FRAMEBUFFER,b);c.viewport(0,0,e.width,e.height);if(this.texture_type==c.TEXTURE_2D)if(this.format!==c.DEPTH_COMPONENT&&this.format!==c.DEPTH_STENCIL)c.framebufferTexture2D(c.FRAMEBUFFER,c.COLOR_ATTACHMENT0,c.TEXTURE_2D,e.handler,0),this.toViewport(a);else{a=c._color_renderbuffer=c._color_renderbuffer||c.createRenderbuffer();b=a.width=e.width;var k=a.height=e.height;c.bindRenderbuffer(c.RENDERBUFFER,a);
c.renderbufferStorage(c.RENDERBUFFER,c.RGBA4,b,k);c.framebufferRenderbuffer(c.FRAMEBUFFER,c.COLOR_ATTACHMENT0,c.RENDERBUFFER,a);b=e.format==c.DEPTH_STENCIL?c.DEPTH_STENCIL_ATTACHMENT:c.DEPTH_ATTACHMENT;c.framebufferTexture2D(c.FRAMEBUFFER,b,c.TEXTURE_2D,e.handler,0);a=c.checkFramebufferStatus(c.FRAMEBUFFER);if(a!==c.FRAMEBUFFER_COMPLETE)throw"FBO not complete: "+a;c.enable(c.DEPTH_TEST);c.depthFunc(c.ALWAYS);c.colorMask(!1,!1,!1,!1);a=g.Shader.getCopyDepthShader();this.toViewport(a);c.colorMask(!0,
!0,!0,!0);c.disable(c.DEPTH_TEST);c.depthFunc(c.LEQUAL);c.framebufferRenderbuffer(c.FRAMEBUFFER,c.COLOR_ATTACHMENT0,c.RENDERBUFFER,null);c.framebufferTexture2D(c.FRAMEBUFFER,b,c.TEXTURE_2D,null,0)}else if(this.texture_type==c.TEXTURE_CUBE_MAP)for(a.uniforms({u_texture:0}),b=g.temp_mat3,k=0;6>k;k++){c.framebufferTexture2D(c.FRAMEBUFFER,c.COLOR_ATTACHMENT0,c.TEXTURE_CUBE_MAP_POSITIVE_X+k,e.handler,0);var q=g.Texture.cubemap_camera_parameters[k];mat3.identity(b);b.set(q.right,0);b.set(q.up,3);b.set(q.dir,
6);this.toViewport(a,{u_rotation:b})}c.setViewport(f);c.bindFramebuffer(c.FRAMEBUFFER,d);e.minFilter&&e.minFilter!=c.NEAREST&&e.minFilter!=c.LINEAR&&(e.bind(),c.generateMipmap(e.texture_type),e.has_mipmaps=!0);e.data=null;c.bindTexture(e.texture_type,null);return this};Texture.prototype.blit=function(){var e=new Float32Array(4);return function(a,b,c){var d=this.gl;if(this.texture_type!=d.TEXTURE_2D||this.format===d.DEPTH_COMPONENT||this.format===d.DEPTH_STENCIL)throw"blit only support TEXTURE_2D of RGB or RGBA. use copyTo instead";
var f=d.getParameter(d.FRAMEBUFFER_BINDING);e.set(d.viewport_data);(b=b||g.Shader.getScreenShader())&&c&&b.uniforms(c);c=d.__copy_fbo;c||(c=d.__copy_fbo=d.createFramebuffer());d.bindFramebuffer(d.FRAMEBUFFER,c);d.viewport(0,0,a.width,a.height);d.framebufferTexture2D(d.FRAMEBUFFER,d.COLOR_ATTACHMENT0,d.TEXTURE_2D,a.handler,0);this.bind(0);b.draw(g.Mesh.getScreenQuad(),d.TRIANGLES);d.setViewport(e);d.bindFramebuffer(d.FRAMEBUFFER,f);a.data=null;d.bindTexture(a.texture_type,null);return this}}();Texture.prototype.toViewport=
function(e,a){e=e||Shader.getScreenShader();var b=Mesh.getScreenQuad();this.bind(0);a&&e.uniforms(a);e.draw(b,gl.TRIANGLES)};Texture.prototype.fill=function(e,a){if(e.constructor===g.Shader)this.drawTo(function(){e.toViewport()});else{var b=gl.getParameter(gl.COLOR_CLEAR_VALUE);gl.clearColor(e[0],e[1],e[2],e[3]);this.drawTo(function(){gl.clear(gl.COLOR_BUFFER_BIT)});gl.clearColor(b[0],b[1],b[2],b[3])}!a&&this.minFilter&&this.minFilter!=gl.NEAREST&&this.minFilter!=gl.LINEAR&&(this.bind(),gl.generateMipmap(this.texture_type),
this.has_mipmaps=!0)};Texture.prototype.renderQuad=function(){var e=mat3.create(),a=vec2.create(),b=vec2.create(),c=vec4.fromValues(1,1,1,1);return function(d,f,k,g,l,m){a[0]=d;a[1]=f;b[0]=k;b[1]=g;l=l||Shader.getQuadShader(this.gl);d=Mesh.getScreenQuad(this.gl);this.bind(0);l.uniforms({u_texture:0,u_position:a,u_color:c,u_size:b,u_viewport:gl.viewport_data.subarray(2,4),u_transform:e});m&&l.uniforms(m);l.draw(d,gl.TRIANGLES)}}();Texture.prototype.applyBlur=function(e,a,b,c,d){var f=this.gl;void 0===
e&&(e=1);void 0===a&&(a=1);f.disable(f.DEPTH_TEST);f.disable(f.BLEND);c=c||this;var k=!d;if(d===c)throw"cannot use applyBlur in a texture using as temporary itself";if(c&&this.texture_type!==c.texture_type)throw"cannot use applyBlur with textures of different texture_type";var q=f.getParameter(f.FRAMEBUFFER_BINDING),l=f.getViewport(),m=f.__copy_fbo;m||(m=f.__copy_fbo=f.createFramebuffer());f.bindFramebuffer(f.FRAMEBUFFER,m);f.viewport(0,0,this.width,this.height);if(this.texture_type===f.TEXTURE_2D)m=
g.Shader.getBlurShader(),d||(d=g.Texture.getTemporary(this.width,this.height,this)),f.framebufferTexture2D(f.FRAMEBUFFER,f.COLOR_ATTACHMENT0,f.TEXTURE_2D,d.handler,0),this.toViewport(m,{u_texture:0,u_intensity:b,u_offset:[0,a/this.height]}),f.framebufferTexture2D(f.FRAMEBUFFER,f.COLOR_ATTACHMENT0,f.TEXTURE_2D,c.handler,0),f.viewport(0,0,c.width,c.height),d.toViewport(m,{u_intensity:b,u_offset:[e/d.width,0]}),k&&g.Texture.releaseTemporary(d);else if(this.texture_type===f.TEXTURE_CUBE_MAP){m=g.Shader.getCubemapBlurShader();
m.uniforms({u_texture:0,u_intensity:b,u_offset:[e/this.width,a/this.height]});this.bind(0);e=Mesh.getScreenQuad();e.bindBuffers(m);m.bind();a=null;a=d||c!=this?c:d=g.Texture.getTemporary(c.width,c.height,c);b=g.temp_mat3;for(var n=0;6>n;++n){f.framebufferTexture2D(f.FRAMEBUFFER,f.COLOR_ATTACHMENT0,f.TEXTURE_CUBE_MAP_POSITIVE_X+n,a.handler,0);var p=g.Texture.cubemap_camera_parameters[n];mat3.identity(b);b.set(p.right,0);b.set(p.up,3);b.set(p.dir,6);m._setUniform("u_rotation",b);f.drawArrays(f.TRIANGLES,
0,6)}e.unbindBuffers(m);d&&d.copyTo(c);d&&k&&g.Texture.releaseTemporary(d)}f.setViewport(l);f.bindFramebuffer(f.FRAMEBUFFER,q);c.data=null;c.minFilter&&c.minFilter!=f.NEAREST&&c.minFilter!=f.LINEAR&&(c.bind(),f.generateMipmap(c.texture_type),c.has_mipmaps=!0);f.bindTexture(c.texture_type,null)};Texture.fromURL=function(e,a,b,c){c=c||t.gl;a=a||{};a=Object.create(a);var d=a.texture||new g.Texture(1,1,a,c);64>e.length&&(d.url=e);d.bind();var f=a.temp_color||Texture.loading_color;c.pixelStorei(c.UNPACK_ALIGNMENT,
4);f=a.type==c.FLOAT?new Float32Array(f):new Uint8Array(f);c.texImage2D(c.TEXTURE_2D,0,d.format,d.width,d.height,0,d.format,d.type,f);c.bindTexture(d.texture_type,null);d.ready=!1;f=null;a.extension&&(f=a.extension);if(!f&&512>e.length){var k=e,q=e.indexOf("?");-1!=q&&(k=e.substr(0,q));q=k.lastIndexOf(".");-1!=q&&(f=k.substr(q+1).toLowerCase())}"dds"==f?(f=c.getExtension("WEBKIT_WEBGL_compressed_texture_s3tc")||c.getExtension("WEBGL_compressed_texture_s3tc"),k=new g.Texture(0,0,a,c),S.loadDDSTextureEx(c,
f,e,k.handler,!0,function(a){d.texture_type=a.texture_type;d.handler=a;delete d.ready;b&&b(d,e)})):"tga"==f?HttpRequest(e,null,function(f){if(f=g.Texture.parseTGA(f))a.texture=d,f.flipY&&(a.no_flip=!0),"RGB"==f.format&&(d.format=c.RGB),d=g.Texture.fromMemory(f.width,f.height,f.pixels,a),delete d.ready,b&&b(d,e)},null,{binary:!0}):(f=new Image,f.src=e,f.onload=function(){a.texture=d;g.Texture.fromImage(this,a);delete d.ready;b&&b(d,e)},f.onerror=function(){b&&b(null)});return d};Texture.parseTGA=function(e){if(!e||
e.constructor!==ArrayBuffer)throw"TGA: data must be ArrayBuffer";e=new Uint8Array(e);for(var a=new Uint8Array([0,0,2,0,0,0,0,0,0,0,0,0]),b=e.subarray(0,12),c=0;c<b.length;c++)if(a[c]!=b[c])return console.error("TGA header is not valid"),null;c=e.subarray(12,18);a={};a.width=256*c[1]+c[0];a.height=256*c[3]+c[2];a.bpp=c[4];a.bytesPerPixel=a.bpp/8;a.imageSize=a.width*a.height*a.bytesPerPixel;a.pixels=e.subarray(18,18+a.imageSize);a.pixels=new Uint8Array(a.pixels);a.flipY=0==(c[5]&32);for(c=0;c<a.imageSize;c+=
a.bytesPerPixel)e=a.pixels[c],a.pixels[c]=a.pixels[c+2],a.pixels[c+2]=e;a.format=32==a.bpp?"RGBA":"RGB";return a};Texture.fromImage=function(e,a){a=a||{};var b=a.texture||new g.Texture(e.width,e.height,a);b.uploadImage(e,a);b.bind();gl.texParameteri(b.texture_type,gl.TEXTURE_MAG_FILTER,b.magFilter);gl.texParameteri(b.texture_type,gl.TEXTURE_MIN_FILTER,b.minFilter);gl.texParameteri(b.texture_type,gl.TEXTURE_WRAP_S,b.wrapS);gl.texParameteri(b.texture_type,gl.TEXTURE_WRAP_T,b.wrapT);g.isPowerOfTwo(b.width)&&
g.isPowerOfTwo(b.height)?a.minFilter&&a.minFilter!=gl.NEAREST&&a.minFilter!=gl.LINEAR&&(b.bind(),gl.generateMipmap(b.texture_type),b.has_mipmaps=!0):(gl.texParameteri(b.texture_type,gl.TEXTURE_MIN_FILTER,g.LINEAR),gl.texParameteri(b.texture_type,gl.TEXTURE_WRAP_S,g.CLAMP_TO_EDGE),gl.texParameteri(b.texture_type,gl.TEXTURE_WRAP_T,g.CLAMP_TO_EDGE),b.has_mipmaps=!1);gl.bindTexture(b.texture_type,null);b.data=e;a.keep_image&&(b.img=e);return b};Texture.fromVideo=function(e,a){a=a||{};var b=a.texture||
new g.Texture(e.videoWidth,e.videoHeight,a);b.bind();b.uploadImage(e,a);a.minFilter&&a.minFilter!=gl.NEAREST&&a.minFilter!=gl.LINEAR&&(b.bind(),gl.generateMipmap(b.texture_type),b.has_mipmaps=!0,b.data=e);gl.bindTexture(b.texture_type,null);return b};Texture.fromTexture=function(e,a){a=a||{};var b=new g.Texture(e.width,e.height,a);e.copyTo(b);return b};Texture.prototype.clone=function(e){var a=this.getProperties();if(e)for(var b in e)a[b]=e[b];return Texture.fromTexture(this,a)};Texture.fromMemory=
function(e,a,b,c){c=c||{};var d=c.texture||new g.Texture(e,a,c);Texture.setUploadOptions(c);d.bind();b.constructor===Array&&(b=c.type==gl.FLOAT?new Float32Array(b):c.type==g.HALF_FLOAT||c.type==g.HALF_FLOAT_OES?new Uint16Array(b):new Uint8Array(b));gl.texImage2D(gl.TEXTURE_2D,0,d.format,e,a,0,d.format,d.type,b);d.width=e;d.height=a;d.data=b;c.minFilter&&c.minFilter!=gl.NEAREST&&c.minFilter!=gl.LINEAR&&(gl.generateMipmap(gl.TEXTURE_2D),d.has_mipmaps=!0);gl.bindTexture(d.texture_type,null);return d};
Texture.fromDDSInMemory=function(e,a){a=a||{};var b=a.texture||new g.Texture(0,0,a);g.Texture.setUploadOptions(a);b.bind();var c=gl.getExtension("WEBKIT_WEBGL_compressed_texture_s3tc")||gl.getExtension("WEBGL_compressed_texture_s3tc");S.loadDDSTextureFromMemoryEx(gl,c,e,b,!0);gl.bindTexture(b.texture_type,null);return b};Texture.fromShader=function(e,a,b,c){c=c||{};e=new g.Texture(e,a,c);e.drawTo(function(){gl.disable(gl.BLEND);gl.disable(gl.DEPTH_TEST);gl.disable(gl.CULL_FACE);var a=Mesh.getScreenQuad();
b.draw(a)});return e};Texture.cubemapFromImages=function(e,a){a=a||{};if(6!=e.length)throw"missing images to create cubemap";var b=e[0].width,c=e[0].height;a.texture_type=gl.TEXTURE_CUBE_MAP;var d=null;a.texture?(d=a.texture,d.width=b,d.height=c):d=new g.Texture(b,c,a);Texture.setUploadOptions(a);d.bind();try{for(b=0;6>b;b++)gl.texImage2D(gl.TEXTURE_CUBE_MAP_POSITIVE_X+b,0,d.format,d.format,d.type,e[b]);d.data=e}catch(f){if("file:"==location.protocol)throw'image not loaded for security reasons (serve this page over "http://" instead)';
throw"image not loaded for security reasons (image must originate from the same domain as this page or use Cross-Origin Resource Sharing)";}a.minFilter&&a.minFilter!=gl.NEAREST&&a.minFilter!=gl.LINEAR&&(gl.generateMipmap(gl.TEXTURE_CUBE_MAP),d.has_mipmaps=!0);d.unbind();return d};Texture.cubemapFromImage=function(e,a){a=a||{};if(e.width!=e.height/6&&0!=e.height%6&&!a.faces&&!a.is_polar)return console.error("Cubemap image not valid, only 1x6 (vertical) or 6x3 (cross) formats. Check size:",e.width,
e.height),null;var b=e.width,c=e.height;if(a.is_polar){var d=a.size||g.nearestPowerOfTwo(e.height),f=g.Texture.fromImage(e,{ignore_pot:!0,wrap:gl.REPEAT,filter:gl.LINEAR}),b=new g.Texture(d,d,{texture_type:gl.TEXTURE_CUBE_MAP,format:gl.RGBA});if(a.texture){var c=a.texture,k;for(k in b)c[k]=b[k];b=c}var q=mat3.create(),l={u_texture:0,u_rotation:q};gl.disable(gl.DEPTH_TEST);gl.disable(gl.BLEND);var m=g.Shader.getPolarToCubemapShader();b.drawTo(function(a,b){var c=g.Texture.cubemap_camera_parameters[b];
mat3.identity(q);q.set(c.right,0);q.set(c.up,3);q.set(c.dir,6);f.toViewport(m,l)});a.keep_image&&(b.img=e);return b}void 0!==a.is_cross?(a.faces=Texture.generateCubemapCrossFacesInfo(e.width,a.is_cross),b=c=e.width/4):a.faces?(b=a.width||a.faces[0].width,c=a.height||a.faces[0].height):c/=6;if(b!=c)return console.log("Texture not valid, width and height for every face must be square"),null;d=b;a.no_flip=!0;var n=[];for(k=0;6>k;k++){var p=createCanvas(d,d),r=p.getContext("2d");a.faces?r.drawImage(e,
a.faces[k].x,a.faces[k].y,a.faces[k].width||d,a.faces[k].height||d,0,0,d,d):r.drawImage(e,0,c*k,b,c,0,0,d,d);n.push(p)}k=Texture.cubemapFromImages(n,a);a.keep_image&&(k.img=e);return k};Texture.generateCubemapCrossFacesInfo=function(e,a){void 0===a&&(a=1);var b=e/4;return[{x:2*b,y:b,width:b,height:b},{x:0,y:b,width:b,height:b},{x:a*b,y:0,width:b,height:b},{x:a*b,y:2*b,width:b,height:b},{x:b,y:b,width:b,height:b},{x:3*b,y:b,width:b,height:b}]};Texture.cubemapFromURL=function(e,a,b){a=a||{};a=Object.create(a);
a.texture_type=gl.TEXTURE_CUBE_MAP;var c=a.texture||new g.Texture(1,1,a);c.bind();Texture.setUploadOptions(a);for(var d=a.temp_color||[0,0,0,255],d=a.type==gl.FLOAT?new Float32Array(d):new Uint8Array(d),f=0;6>f;f++)gl.texImage2D(gl.TEXTURE_CUBE_MAP_POSITIVE_X+f,0,c.format,1,1,0,c.format,c.type,d);gl.bindTexture(c.texture_type,null);c.ready=!1;d=new Image;d.src=e;d.onload=function(){a.texture=c;(c=g.Texture.cubemapFromImage(this,a))&&delete c.ready;b&&b(c)};return c};Texture.prototype.getPixels=function(e,
a){a=a||0;var b=this.gl,c=b.getViewport(),d=b.getParameter(b.FRAMEBUFFER_BINDING);if(this.format==b.DEPTH_COMPONENT)throw"cannot use getPixels in depth textures";b.disable(b.DEPTH_TEST);var f=b.__copy_fbo;f||(f=b.__copy_fbo=b.createFramebuffer());b.bindFramebuffer(b.FRAMEBUFFER,f);var f=null,k=this.width>>a,q=this.height>>a;b.viewport(0,0,k,q);this.texture_type==b.TEXTURE_2D?b.framebufferTexture2D(b.FRAMEBUFFER,b.COLOR_ATTACHMENT0,b.TEXTURE_2D,this.handler,a):this.texture_type==b.TEXTURE_CUBE_MAP&&
b.framebufferTexture2D(b.FRAMEBUFFER,b.COLOR_ATTACHMENT0,b.TEXTURE_CUBE_MAP_POSITIVE_X+(e||0),this.handler,a);var l=this.format==b.RGB?3:4,l=4,m=this.type,f=m==b.UNSIGNED_BYTE?new Uint8Array(k*q*l):m==g.HALF_FLOAT||m==g.HALF_FLOAT_OES?new Uint16Array(k*q*l):new Float32Array(k*q*l);b.readPixels(0,0,k,q,3==l?b.RGB:b.RGBA,m,f);b.bindFramebuffer(b.FRAMEBUFFER,d);b.viewport(c[0],c[1],c[2],c[3]);return f};Texture.prototype.setPixels=function(e,a,b,c){a={no_flip:a};c&&(a.cubemap_face=c);this.uploadData(e,
a,b)};Texture.prototype.getCubemapPixels=function(){if(this.texture_type!==gl.TEXTURE_CUBE_MAP)throw"this texture is not a cubemap";return[this.getPixels(0),this.getPixels(1),this.getPixels(2),this.getPixels(3),this.getPixels(4),this.getPixels(5)]};Texture.prototype.setCubemapPixels=function(e,a){if(this.texture_type!==gl.TEXTURE_CUBE_MAP)throw"this texture is not a cubemap, it should be created with { texture_type: gl.TEXTURE_CUBE_MAP }";for(var b=0;6>b;++b)this.setPixels(e[b],a,5!=b,b)};Texture.prototype.toCanvas=
function(e,a,b){b=b||8192;var c=this.gl,d=Math.min(this.width,b),f=Math.min(this.height,b);this.texture_type==c.TEXTURE_CUBE_MAP&&(d*=4,f*=3);e=e||createCanvas(d,f);e.width!=d&&(e.width=d);e.height!=f&&(e.height=f);var k=null;if(this.texture_type==c.TEXTURE_2D)this.width!=d||this.height!=f||this.type!=c.UNSIGNED_BYTE?(k=new g.Texture(d,f,{format:c.RGBA,filter:c.NEAREST}),this.copyTo(k),k=k.getPixels()):k=this.getPixels(),b=e.getContext("2d"),c=b.getImageData(0,0,d,f),c.data.set(k),b.putImageData(c,
0,0),a&&(k=createCanvas(d,f),a=k.getContext("2d"),a.translate(0,k.height),a.scale(1,-1),a.drawImage(e,0,0,k.width,k.height),b.clearRect(0,0,b.canvas.width,b.canvas.height),b.drawImage(k,0,0));else if(this.texture_type==c.TEXTURE_CUBE_MAP){d=createCanvas(this.width,this.height);a=d.getContext("2d");f=g.Texture.generateCubemapCrossFacesInfo(e.width,1);b=e.getContext("2d");b.fillStyle="black";b.fillRect(0,0,e.width,e.height);var q=this;this.type!=c.UNSIGNED_BYTE&&(q=new g.Texture(this.width,this.height,
{format:c.RGBA,texture_type:c.TEXTURE_CUBE_MAP,filter:c.NEAREST,type:c.UNSIGNED_BYTE}),this.copyTo(q));for(var l=0;6>l;l++)c=a.getImageData(0,0,d.width,d.height),k=q.getPixels(l),c.data.set(k),a.putImageData(c,0,0),b.drawImage(d,f[l].x,f[l].y,d.width,d.height)}return e};Texture.binary_extension="png";Texture.prototype.toBinary=function(e,a){for(var b=this.toCanvas(null,e).toDataURL(a),c=b.indexOf(","),b=b.substr(c+1),b=atob(b),c=b.length,d=new Uint8Array(c),f=0;f<c;++f)d[f]=b.charCodeAt(f);return d};
Texture.prototype.toBlob=function(e,a){var b=this.toBinary(e);return new Blob([b],{type:a||"image/png"})};Texture.prototype.toBlobAsync=function(e,a,b){var c=this.toCanvas(null,e);c.toBlob?c.toBlob(b,a):(e=this.toBlob(e,a),b&&b(e))};Texture.prototype.toBase64=function(e){var a=this.width,b=this.height,c=this.getPixels(),d=createCanvas(a,b),f=d.getContext("2d"),k=f.getImageData(0,0,a,b);k.data.set(c);f.putImageData(k,0,0);e&&(e=createCanvas(a,b),a=e.getContext("2d"),a.translate(0,b),a.scale(1,-1),
a.drawImage(d,0,0),d=e);return d.toDataURL("image/png")};Texture.prototype.generateMetadata=function(){var e={};e.width=this.width;e.height=this.height;this.metadata=e};Texture.compareFormats=function(e,a){return e&&a?e==a?!0:e.width!=a.width||e.height!=a.height||e.type!=a.type||e.format!=a.format||e.texture_type!=a.texture_type?!1:!0:!1};Texture.blend=function(e,a,b,c){if(!e||!a)return!1;if(e==a)return c?e.copyTo(c):e.toViewport(),!0;gl.disable(gl.BLEND);gl.disable(gl.DEPTH_TEST);gl.disable(gl.CULL_FACE);
var d=g.Shader.getBlendShader(),f=g.Mesh.getScreenQuad();a.bind(1);d.uniforms({u_texture:0,u_texture2:1,u_factor:b});if(c)return c.drawTo(function(){if(e==c||a==c)throw"Blend output cannot be the same as the input";e.bind(0);d.draw(f,gl.TRIANGLES)}),!0;e.bind(0);d.draw(f,gl.TRIANGLES);return!0};Texture.cubemapToTexture2D=function(e,a,b,c,d){if(!e||e.texture_type!=gl.TEXTURE_CUBE_MAP)throw"No cubemap in convert";a=a||e.width;c=c?e.type:gl.UNSIGNED_BYTE;d=d||0;b||(b=new g.Texture(2*a,a,{minFilter:gl.NEAREST,
type:c}));var f=gl.shaders.cubemap_to_texture2D;f||(f=gl.shaders.cubemap_to_texture2D=new g.Shader(g.Shader.SCREEN_VERTEX_SHADER,"\t\tprecision mediump float;\n\t\t#define PI 3.14159265358979323846264\n\t\tuniform samplerCube texture;\t\tvarying vec2 v_coord;\t\tuniform float u_yaw;\n\t\tvoid main() {\t\t\tfloat alpha = ((1.0 - v_coord.x) * 2.0) * PI + u_yaw;\t\t\tfloat beta = (v_coord.y * 2.0 - 1.0) * PI * 0.5;\t\t\tvec3 N = vec3( -cos(alpha) * cos(beta), sin(beta), sin(alpha) * cos(beta) );\t\t\tgl_FragColor = textureCube(texture,N);\t\t}"));
f.setUniform("u_yaw",d);b.drawTo(function(){gl.disable(gl.DEPTH_TEST);gl.disable(gl.CULL_FACE);gl.disable(gl.BLEND);e.toViewport(f)});return b};Texture.getWhiteTexture=function(e){e=e||t.gl;var a=e.textures[":white"];if(a)return a;a=new Uint8Array([255,255,255,255]);return e.textures[":white"]=new g.Texture(1,1,{pixel_data:a})};Texture.getBlackTexture=function(e){e=e||t.gl;var a=e.textures[":black"];if(a)return a;a=new Uint8Array([0,0,0,255]);return e.textures[":black"]=new g.Texture(1,1,{pixel_data:a})};
Texture.getTemporary=function(e,a,b,c){c=c||t.gl;c._texture_pool||(c._texture_pool=[]);var d=g.TEXTURE_2D,f=Texture.DEFAULT_TYPE,k=Texture.DEFAULT_FORMAT;b&&(b.texture_type&&(d=b.texture_type),b.type&&(f=b.type),b.format&&(k=b.format));b=d+":"+f+":"+e+"x"+a+":"+k;c=c._texture_pool;for(var q=0;q<c.length;++q){var l=c[q];if(l._key==b)return c.splice(q,1),l._pool=0,l}l=new g.Texture(e,a,{type:f,texture_type:d,format:k});l._key=b;l._pool=0;return l};Texture.releaseTemporary=function(e,a){a=a||t.gl;a._texture_pool||
(a._texture_pool=[]);0<e._pool&&console.warn("this texture is already in the textures pool");var b=a._texture_pool;b||(b=a._texture_pool=[]);e._pool=getTime();b.push(e);20<b.length&&(b.sort(function(a,b){return b._pool-a._pool}),e=b.pop(),e._pool=0,e.delete())};Texture.nextPOT=function(e){return Math.pow(2,Math.ceil(Math.log(e)/Math.log(2)))};g.FBO=B;B.prototype.setTextures=function(e,a,b){if(a&&a.constructor===g.Texture){if(a.format!==g.DEPTH_COMPONENT&&a.format!==g.DEPTH_STENCIL&&a.format!==g.DEPTH_COMPONENT16&&
a.format!==g.DEPTH_COMPONENT24&&a.format!==g.DEPTH_COMPONENT32F)throw"FBO Depth texture must be of format: gl.DEPTH_COMPONENT, gl.DEPTH_STENCIL or gl.DEPTH_COMPONENT16/24/32F (only in webgl2)";if(a.type!=g.UNSIGNED_SHORT&&a.type!=g.UNSIGNED_INT&&a.type!=g.UNSIGNED_INT_24_8_WEBGL&&a.type!=g.FLOAT)throw"FBO Depth texture must be of type: gl.UNSIGNED_SHORT, gl.UNSIGNED_INT, gl.UNSIGNED_INT_24_8_WEBGL";}var c=this.depth_texture==a;if(c&&e){if(e.constructor!==Array)throw"FBO: color_textures parameter must be an array containing all the textures to be binded in the color";
if(e.length==this.color_textures.length)for(var d=0;d<e.length;++d){if(e[d]!=this.color_textures[d]){c=!1;break}}else c=!1}this._stencil_enabled!==this.stencil&&(c=!1);if(!c){this.color_textures.length=e?e.length:0;if(e)for(d=0;d<e.length;++d)this.color_textures[d]=e[d];this.depth_texture=a;this.update(b)}};B.prototype.update=function(e){this._old_fbo_handler=gl.getParameter(gl.FRAMEBUFFER_BINDING);this.handler||(this.handler=gl.createFramebuffer());var a=-1,b=-1,c=null,d=null,f=this.color_textures,
k=this.depth_texture;if(f&&f.length)for(var q=0;q<f.length;q++){var l=f[q];if(l.constructor!==g.Texture)throw"FBO can only bind instances of GL.Texture";if(-1==a)a=l.width;else if(a!=l.width)throw"Cannot bind textures with different dimensions";if(-1==b)b=l.height;else if(b!=l.height)throw"Cannot bind textures with different dimensions";if(null==c)c=l.type,d=l.format;else if(c!=l.type)throw"Cannot bind textures to a FBO with different pixel formats";if(l.texture_type!=gl.TEXTURE_2D)throw"Cannot bind a Cubemap to a FBO";
}else a=k.width,b=k.height;this.width=a;this.height=b;gl.bindFramebuffer(gl.FRAMEBUFFER,this.handler);var m=gl.extensions.WEBGL_draw_buffers;if(1==gl.webgl_version&&!m&&f&&1<f.length)throw"Rendering to several textures not supported by your browser";var n=1==gl.webgl_version?gl.FRAMEBUFFER:gl.DRAW_FRAMEBUFFER;gl.framebufferRenderbuffer(n,gl.DEPTH_ATTACHMENT,gl.RENDERBUFFER,null);gl.framebufferRenderbuffer(n,gl.DEPTH_STENCIL_ATTACHMENT,gl.RENDERBUFFER,null);if(k&&k.constructor===g.Texture){if(1==gl.webgl_version&&
!gl.extensions.WEBGL_depth_texture)throw"Rendering to depth texture not supported by your browser";this.stencil&&k.format!==gl.DEPTH_STENCIL&&console.warn("Stencil cannot be enabled if there is a depth texture with a DEPTH_STENCIL format");k.format==gl.DEPTH_STENCIL?gl.framebufferTexture2D(n,gl.DEPTH_STENCIL_ATTACHMENT,gl.TEXTURE_2D,k.handler,0):gl.framebufferTexture2D(n,gl.DEPTH_ATTACHMENT,gl.TEXTURE_2D,k.handler,0)}else q=null,k&&k.constructor===WebGLRenderbuffer&&k.width==a&&k.height==b?q=this._depth_renderbuffer=
k:(q=this._depth_renderbuffer=this._depth_renderbuffer||gl.createRenderbuffer(),q.width=a,q.height=b),gl.bindRenderbuffer(gl.RENDERBUFFER,q),this.stencil?(gl.renderbufferStorage(gl.RENDERBUFFER,gl.DEPTH_STENCIL,a,b),gl.framebufferRenderbuffer(n,gl.DEPTH_STENCIL_ATTACHMENT,gl.RENDERBUFFER,q)):(gl.renderbufferStorage(gl.RENDERBUFFER,gl.DEPTH_COMPONENT16,a,b),gl.framebufferRenderbuffer(n,gl.DEPTH_ATTACHMENT,gl.RENDERBUFFER,q));if(f&&f.length)for(this.order=[],q=0;q<f.length;q++)l=f[q],gl.framebufferTexture2D(n,
gl.COLOR_ATTACHMENT0+q,gl.TEXTURE_2D,l.handler,0),this.order.push(gl.COLOR_ATTACHMENT0+q);else k=this._color_renderbuffer=this._color_renderbuffer||gl.createRenderbuffer(),k.width=a,k.height=b,gl.bindRenderbuffer(gl.RENDERBUFFER,k),gl.renderbufferStorage(gl.RENDERBUFFER,gl.RGBA4,a,b),gl.framebufferRenderbuffer(n,gl.COLOR_ATTACHMENT0,gl.RENDERBUFFER,k);for(q=a=f?f.length:0;q<this._num_binded_textures;++q)gl.framebufferTexture2D(n,gl.COLOR_ATTACHMENT0+q,gl.TEXTURE_2D,null,0);this._num_binded_textures=
a;this._stencil_enabled=this.stencil;f&&1<f.length&&(m?m.drawBuffersWEBGL(this.order):gl.drawBuffers(this.order));f=gl.checkFramebufferStatus(n);if(f!==gl.FRAMEBUFFER_COMPLETE)throw d!=g.RGB||c!=g.FLOAT&&c!=g.HALF_FLOAT_OES||console.error("Tip: Firefox does not support RGB channel float/half_float textures, you must use RGBA"),"FBO not complete: "+f;gl.bindTexture(gl.TEXTURE_2D,null);gl.bindRenderbuffer(gl.RENDERBUFFER,null);e||gl.bindFramebuffer(n,this._old_fbo_handler)};B.prototype.bind=function(e){if(!this.color_textures.length&&
!this.depth_texture)throw"FBO: no textures attached to FBO";this._old_viewport.set(gl.viewport_data);this._old_fbo_handler=e?gl.getParameter(gl.FRAMEBUFFER_BINDING):null;this._old_fbo_handler!=this.handler&&gl.bindFramebuffer(gl.FRAMEBUFFER,this.handler);for(e=0;e<this.color_textures.length;++e)this.color_textures[e]._in_current_fbo=!0;this.depth_texture&&(this.depth_texture._in_current_fbo=!0);gl.viewport(0,0,this.width,this.height);B.current=this};B.prototype.unbind=function(){gl.bindFramebuffer(gl.FRAMEBUFFER,
this._old_fbo_handler);this._old_fbo_handler=null;gl.setViewport(this._old_viewport);for(var e=0;e<this.color_textures.length;++e)this.color_textures[e]._in_current_fbo=!1;this.depth_texture&&(this.depth_texture._in_current_fbo=!1);B.current=null};B.prototype.switchTo=function(e){e._old_fbo_handler=this._old_fbo_handler;e._old_viewport.set(this._old_viewport);gl.bindFramebuffer(gl.FRAMEBUFFER,e.handler);this._old_fbo_handler=null;gl.viewport(0,0,this.width,this.height);for(var a=0;a<this.color_textures.length;++a)this.color_textures[a]._in_current_fbo=
!1;this.depth_texture&&(this.depth_texture._in_current_fbo=!1);for(a=0;a<e.color_textures.length;++a)e.color_textures[a]._in_current_fbo=!0;e.depth_texture&&(e.depth_texture._in_current_fbo=!0);B.current=e};B.prototype.delete=function(){gl.deleteFramebuffer(this.handler);this.handler=null};B.supported={};B.testSupport=function(e,a){var b=e+":"+a;if(null!=B.supported[b])return B.supported[b];var c=new g.Texture(1,1,{format:a,type:e});try{new g.FBO([c])}catch(d){return console.warn("This browser WEBGL implementation doesn't support this FBO format: "+
g.reverse[e]+" "+g.reverse[a]),B.supported[b]=!1}return B.supported[b]=!0};B.prototype.toSingle=function(e){e=e||0;if(!(2>this.color_textures.length)){var a=gl.extensions.WEBGL_draw_buffers;a?a.drawBuffersWEBGL([this.order[e]]):gl.drawBuffers([this.order[e]])}};B.prototype.toMulti=function(){if(!(2>this.color_textures.length)){var e=gl.extensions.WEBGL_draw_buffers;e?e.drawBuffersWEBGL(this.order):gl.drawBuffers(this.order)}};B.prototype.clearSecondary=function(e){if(this.order&&!(2>this.order.length)){for(var a=
gl.extensions.WEBGL_draw_buffers,b=[gl.NONE],c=1;c<this.order.length;++c)b.push(this.order[c]);a?a.drawBuffersWEBGL(b):gl.drawBuffers(b);gl.clearColor(e[0],e[1],e[2],e[3]);gl.clear(gl.COLOR_BUFFER_BIT);a?a.drawBuffersWEBGL(this.order):gl.drawBuffers(this.order)}};t.Shader=g.Shader=function a(b,c,d){g.debug&&console.log("GL.Shader created");if(!b||!c)throw"GL.Shader source code parameter missing";this._context_id=t.gl.context_id;var f=this.gl=t.gl,k=a.expandMacros(d);d=b.constructor===String?a.injectCode(k,
b,f):b;k=c.constructor===String?a.injectCode(k,c,f):c;this.program=f.createProgram();b=b.constructor===String?g.Shader.compileSource(f.VERTEX_SHADER,d):b;c=c.constructor===String?g.Shader.compileSource(f.FRAGMENT_SHADER,k):c;f.attachShader(this.program,b,f);f.attachShader(this.program,c,f);f.linkProgram(this.program);this.vs_shader=b;this.fs_shader=c;this.attributes={};this.uniformInfo={};this.samplers={};a.use_async?this._first_use=!0:this.checkLink()};Shader.use_async=!0;Shader.expandMacros=function(a){var b=
"";if(a)for(var c in a)b+="#define "+c+" "+(a[c]?a[c]:"")+"\n";return b};Shader.injectCode=function(a,b,c){var d=b.indexOf("\n");c=c?"#define WEBGL"+c.webgl_version+"\n":"";var f=b.substr(0,d).trim();return-1==f.indexOf("#version")?c+a+b:f+"\n"+c+a+b.substr(d)};Shader.compileSource=function(a,b,c,d){c=c||t.gl;d=d||c.createShader(a);c.shaderSource(d,b);c.compileShader(d);return d};Shader.parseError=function(a,b,c){if(!a)return null;var d=a.split(" "),f=d[5].split(":");return{type:d[0],line_number:parseInt(f[1]),
line_pos:parseInt(f[0]),line_code:("Fragment"==d[0]?c:b).split("\n")[parseInt(f[1])],err:a}};Shader.prototype.updateShader=function(a,b,c){var d=this.gl||t.gl,f=Shader.expandMacros(c);this.program?(d.detachShader(this.program,this.vs_shader),d.detachShader(this.program,this.fs_shader)):this.program=d.createProgram();f=Shader.expandMacros(c);c=a.constructor===String?Shader.injectCode(f,a,d):a;f=b.constructor===String?Shader.injectCode(f,b,d):b;a=a.constructor===String?g.Shader.compileSource(d.VERTEX_SHADER,
c):a;b=b.constructor===String?g.Shader.compileSource(d.FRAGMENT_SHADER,f):b;d.attachShader(this.program,a,d);d.attachShader(this.program,b,d);d.linkProgram(this.program);this.vs_shader=a;this.fs_shader=b;this.attributes={};this.uniformInfo={};this.samplers={};Shader.use_async?this._first_use=!0:this.checkLink()};Shader.prototype.extractShaderInfo=function(){for(var a=this.gl,b=a.getProgramParameter(this.program,a.ACTIVE_UNIFORMS),c=0;c<b;++c){var d=a.getActiveUniform(this.program,c);if(!d)break;var f=
d.name,k=f.indexOf("[");-1!=k&&-1==f.indexOf("].")&&(f=f.substr(0,k));if(d.type==g.SAMPLER_2D||d.type==g.SAMPLER_CUBE||d.type==g.SAMPLER_3D||d.type==g.INT_SAMPLER_2D||d.type==g.INT_SAMPLER_CUBE||d.type==g.INT_SAMPLER_3D||d.type==g.UNSIGNED_INT_SAMPLER_2D||d.type==g.UNSIGNED_INT_SAMPLER_CUBE||d.type==g.UNSIGNED_INT_SAMPLER_3D)this.samplers[f]=d.type;var k=Shader.getUniformFunc(d),q=!1;if(d.type==a.FLOAT_MAT2||d.type==a.FLOAT_MAT3||d.type==a.FLOAT_MAT4)q=!0;var l=g.TYPE_LENGTH[d.type]||1;this.uniformInfo[f]=
{type:d.type,func:k,size:d.size,type_length:l,is_matrix:q,loc:a.getUniformLocation(this.program,f),data:new Float32Array(l*d.size)}}c=0;for(b=a.getProgramParameter(this.program,a.ACTIVE_ATTRIBUTES);c<b;++c){d=a.getActiveAttrib(this.program,c);if(!d)break;k=Shader.getUniformFunc(d);l=g.TYPE_LENGTH[d.type]||1;this.uniformInfo[d.name]={type:d.type,func:k,type_length:l,size:d.size,loc:null};this.attributes[d.name]=a.getAttribLocation(this.program,d.name)}};Shader.prototype.hasUniform=function(a){return this.uniformInfo[a]};
Shader.prototype.hasAttribute=function(a){return this.attributes[a]};Shader.getUniformFunc=function(a){var b=null;switch(a.type){case g.FLOAT:b=1==a.size?gl.uniform1f:gl.uniform1fv;break;case g.FLOAT_MAT2:b=gl.uniformMatrix2fv;break;case g.FLOAT_MAT3:b=gl.uniformMatrix3fv;break;case g.FLOAT_MAT4:b=gl.uniformMatrix4fv;break;case g.FLOAT_VEC2:b=gl.uniform2fv;break;case g.FLOAT_VEC3:b=gl.uniform3fv;break;case g.FLOAT_VEC4:b=gl.uniform4fv;break;case g.UNSIGNED_INT:case g.INT:b=1==a.size?gl.uniform1i:
gl.uniform1iv;break;case g.INT_VEC2:b=gl.uniform2iv;break;case g.INT_VEC3:b=gl.uniform3iv;break;case g.INT_VEC4:b=gl.uniform4iv;break;case g.SAMPLER_2D:case g.SAMPLER_3D:case g.SAMPLER_CUBE:case g.INT_SAMPLER_2D:case g.INT_SAMPLER_3D:case g.INT_SAMPLER_CUBE:case g.UNSIGNED_INT_SAMPLER_2D:case g.UNSIGNED_INT_SAMPLER_3D:case g.UNSIGNED_INT_SAMPLER_CUBE:b=gl.uniform1i;break;default:b=gl.uniform1f}return b};Shader.fromURL=function(a,b,c){function d(){var a=new g.Shader(k,q),b;for(b in a)f[b]=a[b];f.ready=
!0}var f=new g.Shader("\n\t\t\tprecision highp float;\n\t\t\tattribute vec3 a_vertex;\n\t\t\tattribute mat4 u_mvp;\n\t\t\tvoid main() { \n\t\t\t\tgl_Position = u_mvp * vec4(a_vertex,1.0); \n\t\t\t}\n\t\t","\n\t\t\tprecision highp float;\n\t\t\tvoid main() {\n\t\t\t\tgl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\n\t\t\t}\n\t\t\t");f.ready=!1;var k=null,q=null;HttpRequest(a,null,function(a){k=a;q&&d()});HttpRequest(b,null,function(a){q=a;k&&d()});return f};Shader.prototype.checkLink=function(){this._first_use=
!1;if(!gl.getShaderParameter(this.vs_shader,gl.COMPILE_STATUS))throw"Vertex shader compile error: "+gl.getShaderInfoLog(this.vs_shader);if(!gl.getShaderParameter(this.fs_shader,gl.COMPILE_STATUS))throw"Fragment shader compile error: "+gl.getShaderInfoLog(this.fs_shader);if(!gl.getProgramParameter(this.program,gl.LINK_STATUS))throw"link error: "+gl.getProgramInfoLog(this.program);this.extractShaderInfo()};Shader.prototype.bind=function(){var a=this.gl;Shader.use_async&&this._first_use&&(this.checkLink(),
this._first_use=!1);a.useProgram(this.program);a._current_shader=this};Shader.prototype.getLocation=function(a){return this.uniformInfo[a]?this.uniformInfo[a].loc:null};Shader._temp_uniform=new Float32Array(16);Shader.prototype.uniforms=function(a){var b=this.gl;this._first_use&&this.checkLink();b.useProgram(this.program);b._current_shader=this;for(var c in a)this.uniformInfo[c]&&this._setUniform(c,a[c]);return this};Shader.prototype.uniformsArray=function(a){var b=this.gl;this._first_use&&this.checkLink();
b.useProgram(this.program);b._current_shader=this;for(var b=0,c=a.length;b<c;++b){var d=a[b],f;for(f in d)this._setUniform(f,d[f])}return this};Shader.prototype.setUniform=function(){return function(a,b){this.gl._current_shader!=this&&this.bind();var c=this.uniformInfo[a];c&&null!==c.loc&&null!=b&&(b.constructor===Array&&(c.data.set(b),b=c.data),c.is_matrix?c.func.call(this.gl,c.loc,!1,b):c.func.call(this.gl,c.loc,b))}}();Shader.prototype._setUniform=function(){return function(a,b){this._first_use&&
this.checkLink();var c=this.uniformInfo[a];c&&null!==c.loc&&null!=b&&(b.constructor===Array&&(c.data.set(b),b=c.data),c.is_matrix?c.func.call(this.gl,c.loc,!1,b):c.func.call(this.gl,c.loc,b))}}();Shader.prototype.draw=function(a,b,c){c=void 0===c?b==gl.LINES?"lines":"triangles":c;this.drawBuffers(a.vertexBuffers,c?a.indexBuffers[c]:null,2>arguments.length?gl.TRIANGLES:b)};Shader.prototype.drawRange=function(a,b,c,d,f){f=void 0===f?b==gl.LINES?"lines":"triangles":f;this.drawBuffers(a.vertexBuffers,
f?a.indexBuffers[f]:null,b,c,d)};var P=new Uint8Array(16),U=new Uint8Array(16);Shader.prototype.drawBuffers=function(a,b,c,d,f){if(0!=f){var k=this.gl;this._first_use&&this.checkLink();k.useProgram(this.program);var g=0;P.set(U);for(var l in a){var m=a[l],n=m.attribute||l,p=this.attributes[n];null!=p&&m.buffer&&(P[p]=1,k.bindBuffer(k.ARRAY_BUFFER,m.buffer),k.enableVertexAttribArray(p),k.vertexAttribPointer(p,m.buffer.spacing,m.buffer.gl_type,!1,0,0),g=m.buffer.length/m.buffer.spacing)}a=0;0<d&&(a=
d);b&&(g=b.buffer.length-a);0<f&&f<g&&(g=f);a*=b&&b.data?b.data.constructor.BYTES_PER_ELEMENT:1;for(n in this.attributes)p=this.attributes[n],P[p]||k.disableVertexAttribArray(this.attributes[n]);!g||b&&!b.buffer||(b?(k.bindBuffer(k.ELEMENT_ARRAY_BUFFER,b.buffer),k.drawElements(c,g,b.buffer.gl_type,a),k.bindBuffer(k.ELEMENT_ARRAY_BUFFER,null)):k.drawArrays(c,a,g),k.draw_calls++);return this}};Shader._instancing_arrays=[];Shader.prototype.drawInstanced=function(a,b,c,d,f,k,q){if(0!==k){var l=this.gl;
if(1==l.webgl_version&&!l.extensions.ANGLE_instanced_arrays)throw"instancing not supported";this._first_use&&this.checkLink();l.useProgram(this.program);var m=0;P.set(U);var n=a.vertexBuffers,p;for(p in n){var r=n[p],v=r.attribute||p,s=this.attributes[v];null!=s&&r.buffer&&(P[s]=1,l.bindBuffer(l.ARRAY_BUFFER,r.buffer),l.enableVertexAttribArray(s),l.vertexAttribPointer(s,r.buffer.spacing,r.buffer.gl_type,!1,0,0),m=r.buffer.length/r.buffer.spacing)}n=null;c&&(c.constructor===String?n=a.getIndexBuffer(c):
c.constructor===g.Buffer&&(n=c));a=0;0<f&&(a=f);n&&(m=n.buffer.length-a);0<k&&k<m&&(m=k);a*=n&&n.data?n.data.constructor.BYTES_PER_ELEMENT:1;for(v in this.attributes)s=this.attributes[v],P[s]||l.disableVertexAttribArray(this.attributes[v]);f=l.extensions.ANGLE_instanced_arrays;k=s=0;for(var u in d){p=d[u];s=p.length;v=this.attributes[u];if(null==v)return;var x=c=0;p.constructor===Array?(c=p[0].constructor===Number?1:p[0].length,x=c*p.length):(c=this.uniformInfo[u].type_length,x=p.length,s=x/c);r=
Shader._instancing_arrays[k];if(!r||r.data.length<x)r=Shader._instancing_arrays[k]={data:new Float32Array(x),buffer:l.createBuffer()};r.uniform=u;r.element_size=c;if(p.constructor===Array)for(x=0;x<p.length;++x)r.data.set(p[x],x*c);else r.data.set(p);l.bindBuffer(l.ARRAY_BUFFER,r.buffer);l.bufferData(l.ARRAY_BUFFER,r.data,l.STREAM_DRAW);if(16==c)for(c=0;4>c;++c)l.enableVertexAttribArray(v+c),l.vertexAttribPointer(v+c,4,l.FLOAT,!1,64,16*c),f?f.vertexAttribDivisorANGLE(v+c,1):l.vertexAttribDivisor(v+
c,1);else l.enableVertexAttribArray(v),l.vertexAttribPointer(v,c,l.FLOAT,!1,4*c,0),f?f.vertexAttribDivisorANGLE(v,1):l.vertexAttribDivisor(v,1);k+=1}q&&(s=q);f?n?(l.bindBuffer(l.ELEMENT_ARRAY_BUFFER,n.buffer),f.drawElementsInstancedANGLE(b,m,n.buffer.gl_type,a,s),l.bindBuffer(l.ELEMENT_ARRAY_BUFFER,null)):f.drawArraysInstancedANGLE(b,a,m,s):n?(l.bindBuffer(l.ELEMENT_ARRAY_BUFFER,n.buffer),l.drawElementsInstanced(b,m,n.buffer.gl_type,a,s),l.bindBuffer(l.ELEMENT_ARRAY_BUFFER,null)):l.drawArraysInstanced(b,
a,m,s);for(b=0;b<k;++b)if(d=Shader._instancing_arrays[b],v=this.attributes[d.uniform],c=d.element_size,16==c)for(c=0;4>c;++c)l.disableVertexAttribArray(v+c),f?f.vertexAttribDivisorANGLE(v+c,0):l.vertexAttribDivisor(v+c,0);else l.enableVertexAttribArray(v),f?f.vertexAttribDivisorANGLE(v,0):l.vertexAttribDivisor(v,0);return this}};Shader.expandImports=function(a,b){b=b||Shader.files;var c={};if(!b)throw"Shader.files not initialized, assign files there";return a.replace(/#import\s+\"([a-zA-Z0-9_\.]+)\"\s*\n/g,
function(a){a=a.split('"')[1];if(c[a])return"//already imported: "+a+"\n";var f=b[a];c[a]=!0;return f?f+"\n":"//import code not found: "+a+"\n"})};Shader.dumpErrorToConsole=function(a,b,c){console.error(a);var d=null,d=-1!=a.indexOf("Fragment")?c:b;a=d.split("\n");for(var f in a)a[f]=f+"| "+a[f];console.groupCollapsed("Shader code");console.log(a.join("\n"));console.groupEnd()};Shader.convertTo100=function(a,b){};Shader.convertTo300=function(a,b){};Shader.validateValue=function(a,b){if(null===a||
void 0===a)return!1;switch(b.type){case g.INT:case g.FLOAT:case g.SAMPLER_2D:case g.SAMPLER_3D:case g.SAMPLER_CUBE:case g.INT_SAMPLER_2D:case g.INT_SAMPLER_3D:case g.INT_SAMPLER_CUBE:case g.UNSIGNED_INT_SAMPLER_2D:case g.UNSIGNED_INT_SAMPLER_3D:case g.UNSIGNED_INT_SAMPLER_CUBE:return isNumber(a);case g.INT_VEC2:case g.FLOAT_VEC2:return 2===a.length;case g.INT_VEC3:case g.FLOAT_VEC3:return 3===a.length;case g.INT_VEC4:case g.FLOAT_VEC4:case g.FLOAT_MAT2:return 4===a.length;case g.FLOAT_MAT3:return 8===
a.length;case g.FLOAT_MAT4:return 16===a.length}return!0};Shader.DEFAULT_VERTEX_SHADER="\n\t\t\tprecision highp float;\n\t\t\tattribute vec3 a_vertex;\n\t\t\tattribute vec3 a_normal;\n\t\t\tattribute vec2 a_coord;\n\t\t\tvarying vec3 v_position;\n\t\t\tvarying vec3 v_normal;\n\t\t\tvarying vec2 v_coord;\n\t\t\tuniform mat4 u_model;\n\t\t\tuniform mat4 u_mvp;\n\t\t\tvoid main() {\n\t\t\t\tv_position = (u_model * vec4(a_vertex,1.0)).xyz;\n\t\t\t\tv_normal = (u_model * vec4(a_normal,0.0)).xyz;\n\t\t\t\tv_coord = a_coord;\n\t\t\t\tgl_Position = u_mvp * vec4(a_vertex,1.0);\n\t\t\t}\n\t\t\t";
Shader.SCREEN_VERTEX_SHADER="\n\t\t\tprecision highp float;\n\t\t\tattribute vec3 a_vertex;\n\t\t\tattribute vec2 a_coord;\n\t\t\tvarying vec2 v_coord;\n\t\t\tvoid main() { \n\t\t\t\tv_coord = a_coord; \n\t\t\t\tgl_Position = vec4(a_coord * 2.0 - 1.0, 0.0, 1.0); \n\t\t\t}\n\t\t\t";Shader.SCREEN_FRAGMENT_SHADER="\n\t\t\tprecision highp float;\n\t\t\tuniform sampler2D u_texture;\n\t\t\tvarying vec2 v_coord;\n\t\t\tvoid main() {\n\t\t\t\tgl_FragColor = texture2D(u_texture, v_coord);\n\t\t\t}\n\t\t\t";
Shader.SCREEN_FRAGMENT_FX="\n\t\t\tprecision highp float;\n\t\t\tuniform sampler2D u_texture;\n\t\t\tvarying vec2 v_coord;\n\t\t\t#ifdef FX_UNIFORMS\n\t\t\t\tFX_UNIFORMS\n\t\t\t#endif\n\t\t\tvoid main() {\n\t\t\t\tvec2 uv = v_coord;\n\t\t\t\tvec4 color = texture2D(u_texture, uv);\n\t\t\t\t#ifdef FX_CODE\n\t\t\t\t\tFX_CODE ;\n\t\t\t\t#endif\n\t\t\t\tgl_FragColor = color;\n\t\t\t}\n\t\t\t";Shader.SCREEN_COLORED_FRAGMENT_SHADER="\n\t\t\tprecision highp float;\n\t\t\tuniform sampler2D u_texture;\n\t\t\tuniform vec4 u_color;\n\t\t\tvarying vec2 v_coord;\n\t\t\tvoid main() {\n\t\t\t\tgl_FragColor = u_color * texture2D(u_texture, v_coord);\n\t\t\t}\n\t\t\t";
Shader.BLEND_FRAGMENT_SHADER="\n\t\t\tprecision highp float;\n\t\t\tuniform sampler2D u_texture;\n\t\t\tuniform sampler2D u_texture2;\n\t\t\tuniform float u_factor;\n\t\t\tvarying vec2 v_coord;\n\t\t\tvoid main() {\n\t\t\t\tgl_FragColor = mix( texture2D(u_texture, v_coord), texture2D(u_texture2, v_coord), u_factor);\n\t\t\t}\n\t\t\t";Shader.QUAD_VERTEX_SHADER="\n\t\t\tprecision highp float;\n\t\t\tattribute vec3 a_vertex;\n\t\t\tattribute vec2 a_coord;\n\t\t\tvarying vec2 v_coord;\n\t\t\tuniform vec2 u_position;\n\t\t\tuniform vec2 u_size;\n\t\t\tuniform vec2 u_viewport;\n\t\t\tuniform mat3 u_transform;\n\t\t\tvoid main() { \n\t\t\t\tvec3 pos = vec3(u_position + vec2(a_coord.x,1.0 - a_coord.y)  * u_size, 1.0);\n\t\t\t\tv_coord = a_coord; \n\t\t\t\tpos = u_transform * pos;\n\t\t\t\tpos.z = 0.0;\n\t\t\t\t//normalize\n\t\t\t\tpos.x = (2.0 * pos.x / u_viewport.x) - 1.0;\n\t\t\t\tpos.y = -((2.0 * pos.y / u_viewport.y) - 1.0);\n\t\t\t\tgl_Position = vec4(pos, 1.0); \n\t\t\t}\n\t\t\t";
Shader.QUAD_FRAGMENT_SHADER="\n\t\t\tprecision highp float;\n\t\t\tuniform sampler2D u_texture;\n\t\t\tuniform vec4 u_color;\n\t\t\tvarying vec2 v_coord;\n\t\t\tvoid main() {\n\t\t\t\tgl_FragColor = u_color * texture2D(u_texture, v_coord);\n\t\t\t}\n\t\t\t";Shader.QUAD2_FRAGMENT_SHADER="\n\t\t\tprecision highp float;\n\t\t\tuniform sampler2D u_texture;\n\t\t\tuniform vec4 u_color;\n\t\t\tuniform vec4 u_texture_area;\n\t\t\tvarying vec2 v_coord;\n\t\t\tvoid main() {\n\t\t\t    vec2 uv = vec2( mix(u_texture_area.x, u_texture_area.z, v_coord.x), 1.0 - mix(u_texture_area.w, u_texture_area.y, v_coord.y) );\n\t\t\t\tgl_FragColor = u_color * texture2D(u_texture, uv);\n\t\t\t}\n\t\t\t";
Shader.PRIMITIVE2D_VERTEX_SHADER="\n\t\t\tprecision highp float;\n\t\t\tattribute vec3 a_vertex;\n\t\t\tuniform vec2 u_viewport;\n\t\t\tuniform mat3 u_transform;\n\t\t\tvoid main() { \n\t\t\t\tvec3 pos = a_vertex;\n\t\t\t\tpos = u_transform * pos;\n\t\t\t\tpos.z = 0.0;\n\t\t\t\t//normalize\n\t\t\t\tpos.x = (2.0 * pos.x / u_viewport.x) - 1.0;\n\t\t\t\tpos.y = -((2.0 * pos.y / u_viewport.y) - 1.0);\n\t\t\t\tgl_Position = vec4(pos, 1.0); \n\t\t\t}\n\t\t\t";Shader.FLAT_VERTEX_SHADER="\n\t\t\tprecision highp float;\n\t\t\tattribute vec3 a_vertex;\n\t\t\tuniform mat4 u_mvp;\n\t\t\tvoid main() { \n\t\t\t\tgl_Position = u_mvp * vec4(a_vertex,1.0); \n\t\t\t}\n\t\t\t";
Shader.FLAT_FRAGMENT_SHADER="\n\t\t\tprecision highp float;\n\t\t\tuniform vec4 u_color;\n\t\t\tvoid main() {\n\t\t\t\tgl_FragColor = u_color;\n\t\t\t}\n\t\t\t";Shader.SCREEN_FLAT_FRAGMENT_SHADER=Shader.FLAT_FRAGMENT_SHADER;Shader.createFX=function(a,b,c){a=g.Shader.removeComments(a,!0);b=g.Shader.removeComments(b,!0);a={FX_CODE:a,FX_UNIFORMS:b||""};if(!c)return new g.Shader(g.Shader.SCREEN_VERTEX_SHADER,g.Shader.SCREEN_FRAGMENT_FX,a);c.updateShader(g.Shader.SCREEN_VERTEX_SHADER,g.Shader.SCREEN_FRAGMENT_FX,
a);return c};Shader.replaceCodeUsingContext=function(a,b){return a.replace(/\{\{[a-zA-Z0-9_]*\}\}/g,function(a){a=a.replace(/[\{\}]/g,"");return b[a]||""})};Shader.removeComments=function(a,b){if(!a)return"";a=a.replace(/(\/\*([^*]|[\r\n]|(\*+([^*\/]|[\r\n])))*\*+\/)|(\/\/.*)/g,"");for(var c=a.split("\n"),d=[],f=0;f<c.length;++f){var k=c[f],g=k.indexOf("//");-1!=g&&(k=c[f].substr(0,g));k=k.trim();k.length&&d.push(k)}return d.join(b?"":"\n")};Shader.prototype.toViewport=function(a){var b=g.Mesh.getScreenQuad();
a&&this.uniforms(a);this.draw(b)};Shader.getScreenShader=function(a){a=a||t.gl;var b=a.shaders[":screen"];if(b)return b;b=a.shaders[":screen"]=new g.Shader(Shader.SCREEN_VERTEX_SHADER,Shader.SCREEN_FRAGMENT_SHADER);return b.uniforms({u_texture:0})};Shader.getFlatScreenShader=function(a){a=a||t.gl;var b=a.shaders[":flat_screen"];if(b)return b;b=a.shaders[":flat_screen"]=new g.Shader(Shader.SCREEN_VERTEX_SHADER,Shader.FLAT_FRAGMENT_SHADER);return b.uniforms({u_color:[1,1,1,1]})};Shader.getColoredScreenShader=
function(a){a=a||t.gl;var b=a.shaders[":colored_screen"];if(b)return b;b=a.shaders[":colored_screen"]=new g.Shader(Shader.SCREEN_VERTEX_SHADER,Shader.SCREEN_COLORED_FRAGMENT_SHADER);return b.uniforms({u_texture:0,u_color:vec4.fromValues(1,1,1,1)})};Shader.getQuadShader=function(a){a=a||t.gl;var b=a.shaders[":quad"];return b?b:a.shaders[":quad"]=new g.Shader(Shader.QUAD_VERTEX_SHADER,Shader.QUAD_FRAGMENT_SHADER)};Shader.getPartialQuadShader=function(a){a=a||t.gl;var b=a.shaders[":quad2"];return b?
b:a.shaders[":quad2"]=new g.Shader(Shader.QUAD_VERTEX_SHADER,Shader.QUAD2_FRAGMENT_SHADER)};Shader.getBlendShader=function(a){a=a||t.gl;var b=a.shaders[":blend"];return b?b:a.shaders[":blend"]=new g.Shader(Shader.SCREEN_VERTEX_SHADER,Shader.BLEND_FRAGMENT_SHADER)};Shader.getBlurShader=function(a){a=a||t.gl;var b=a.shaders[":blur"];if(b)return b;b=new g.Shader(Shader.SCREEN_VERTEX_SHADER,"\n\t\t\tprecision highp float;\n\t\t\tvarying vec2 v_coord;\n\t\t\tuniform sampler2D u_texture;\n\t\t\tuniform vec2 u_offset;\n\t\t\tuniform float u_intensity;\n\t\t\tvoid main() {\n\t\t\t   vec4 sum = vec4(0.0);\n\t\t\t   sum += texture2D(u_texture, v_coord + u_offset * -4.0) * 0.05/0.98;\n\t\t\t   sum += texture2D(u_texture, v_coord + u_offset * -3.0) * 0.09/0.98;\n\t\t\t   sum += texture2D(u_texture, v_coord + u_offset * -2.0) * 0.12/0.98;\n\t\t\t   sum += texture2D(u_texture, v_coord + u_offset * -1.0) * 0.15/0.98;\n\t\t\t   sum += texture2D(u_texture, v_coord) * 0.16/0.98;\n\t\t\t   sum += texture2D(u_texture, v_coord + u_offset * 4.0) * 0.05/0.98;\n\t\t\t   sum += texture2D(u_texture, v_coord + u_offset * 3.0) * 0.09/0.98;\n\t\t\t   sum += texture2D(u_texture, v_coord + u_offset * 2.0) * 0.12/0.98;\n\t\t\t   sum += texture2D(u_texture, v_coord + u_offset * 1.0) * 0.15/0.98;\n\t\t\t   gl_FragColor = u_intensity * sum;\n\t\t\t}\n\t\t\t");
return a.shaders[":blur"]=b};Shader.getCopyDepthShader=function(a){a=a||t.gl;var b=a.shaders[":copy_depth"];if(b)return b;b=new g.Shader(Shader.SCREEN_VERTEX_SHADER,"\n\t\t\t#extension GL_EXT_frag_depth : enable\n\t\t\tprecision highp float;\n\t\t\tvarying vec2 v_coord;\n\t\t\tuniform sampler2D u_texture;\n\t\t\tvoid main() {\n\t\t\t   gl_FragDepthEXT = texture2D( u_texture, v_coord ).x;\n\t\t\t   gl_FragColor = vec4(1.0);\n\t\t\t}\n\t\t\t");return a.shaders[":copy_depth"]=b};Shader.getCubemapShowShader=
function(a){a=a||t.gl;var b=a.shaders[":show_cubemap"];if(b)return b;b=new g.Shader(Shader.DEFAULT_VERTEX_SHADER,"\n\t\t\tprecision highp float;\n\t\t\tvarying vec3 v_normal;\n\t\t\tuniform samplerCube u_texture;\n\t\t\tvoid main() {\n\t\t\t   gl_FragColor = textureCube( u_texture, v_normal );\n\t\t\t}\n\t\t\t");b.uniforms({u_texture:0});return a.shaders[":show_cubemap"]=b};Shader.getPolarToCubemapShader=function(a){a=a||t.gl;var b=a.shaders[":polar_to_cubemap"];if(b)return b;b=new g.Shader(Shader.SCREEN_VERTEX_SHADER,
"\n\t\t\tprecision highp float;\n\t\t\tvarying vec2 v_coord;\n\t\t\tuniform sampler2D u_texture;\n\t\t\tuniform mat3 u_rotation;\n\t\t\tvoid main() {\n\t\t\t\tvec2 uv = vec2( v_coord.x, 1.0 - v_coord.y );\n\t\t\t\tvec3 dir = normalize( vec3( uv - vec2(0.5), 0.5 ));\n\t\t\t\tdir = u_rotation * dir;\n\t\t\t\tfloat u = atan(dir.x,dir.z) / 6.28318531;\n\t\t\t\tfloat v = (asin(dir.y) / 1.57079633) * 0.5 + 0.5;\n\t\t\t\tu = mod(u,1.0);\n\t\t\t\tv = mod(v,1.0);\n\t\t\t   gl_FragColor = texture2D( u_texture, vec2(u,v) );\n\t\t\t}\n\t\t\t");
return a.shaders[":polar_to_cubemap"]=b};Shader.getCubemapCopyShader=function(a){a=a||t.gl;var b=a.shaders[":copy_cubemap"];if(b)return b;b=new g.Shader(Shader.SCREEN_VERTEX_SHADER,"\n\t\t\tprecision highp float;\n\t\t\tvarying vec2 v_coord;\n\t\t\tuniform samplerCube u_texture;\n\t\t\tuniform mat3 u_rotation;\n\t\t\tvoid main() {\n\t\t\t\tvec2 uv = vec2( v_coord.x, 1.0 - v_coord.y );\n\t\t\t\tvec3 dir = vec3( uv - vec2(0.5), 0.5 );\n\t\t\t\tdir = u_rotation * dir;\n\t\t\t   gl_FragColor = textureCube( u_texture, dir );\n\t\t\t}\n\t\t\t");
return a.shaders[":copy_cubemap"]=b};Shader.getCubemapBlurShader=function(a){a=a||t.gl;var b=a.shaders[":blur_cubemap"];if(b)return b;b=new g.Shader(Shader.SCREEN_VERTEX_SHADER,"\n\t\t\t#ifndef NUM_SAMPLES\n\t\t\t\t#define NUM_SAMPLES 4\n\t\t\t#endif\n\t\t\t\n\t\t\tprecision highp float;\n\t\t\tvarying vec2 v_coord;\n\t\t\tuniform samplerCube u_texture;\n\t\t\tuniform mat3 u_rotation;\n\t\t\tuniform vec2 u_offset;\n\t\t\tuniform float u_intensity;\n\t\t\tvoid main() {\n\t\t\t\tvec4 sum = vec4(0.0);\n\t\t\t\tvec2 uv = vec2( v_coord.x, 1.0 - v_coord.y ) - vec2(0.5);\n\t\t\t\tvec3 dir = vec3(0.0);\n\t\t\t\tvec4 color = vec4(0.0);\n\t\t\t\tfor( int x = -2; x <= 2; x++ )\n\t\t\t\t{\n\t\t\t\t\tfor( int y = -2; y <= 2; y++ )\n\t\t\t\t\t{\n\t\t\t\t\t\tdir.xy = uv + vec2( u_offset.x * float(x), u_offset.y * float(y)) * 0.5;\n\t\t\t\t\t\tdir.z = 0.5;\n\t\t\t\t\t\tdir = u_rotation * dir;\n\t\t\t\t\t\tcolor = textureCube( u_texture, dir );\n\t\t\t\t\t\tcolor.xyz = color.xyz * color.xyz;/*linearize*/\n\t\t\t\t\t\tsum += color;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tsum /= 25.0;\n\t\t\t   gl_FragColor = vec4( sqrt( sum.xyz ), sum.w ) ;\n\t\t\t}\n\t\t\t");
return a.shaders[":blur_cubemap"]=b};Shader.FXAA_FUNC="\n\tuniform vec2 u_viewportSize;\n\tuniform vec2 u_iViewportSize;\n\t#define FXAA_REDUCE_MIN   (1.0/ 128.0)\n\t#define FXAA_REDUCE_MUL   (1.0 / 8.0)\n\t#define FXAA_SPAN_MAX     8.0\n\t\n\t/* from mitsuhiko/webgl-meincraft based on the code on geeks3d.com */\n\t/* fragCoord MUST BE IN PIXELS */\n\tvec4 applyFXAA(sampler2D tex, vec2 fragCoord)\n\t{\n\t\tvec4 color = vec4(0.0);\n\t\t/*vec2 u_iViewportSize = vec2(1.0 / u_viewportSize.x, 1.0 / u_viewportSize.y);*/\n\t\tvec3 rgbNW = texture2D(tex, (fragCoord + vec2(-1.0, -1.0)) * u_iViewportSize).xyz;\n\t\tvec3 rgbNE = texture2D(tex, (fragCoord + vec2(1.0, -1.0)) * u_iViewportSize).xyz;\n\t\tvec3 rgbSW = texture2D(tex, (fragCoord + vec2(-1.0, 1.0)) * u_iViewportSize).xyz;\n\t\tvec3 rgbSE = texture2D(tex, (fragCoord + vec2(1.0, 1.0)) * u_iViewportSize).xyz;\n\t\tvec3 rgbM  = texture2D(tex, fragCoord  * u_iViewportSize).xyz;\n\t\tvec3 luma = vec3(0.299, 0.587, 0.114);\n\t\tfloat lumaNW = dot(rgbNW, luma);\n\t\tfloat lumaNE = dot(rgbNE, luma);\n\t\tfloat lumaSW = dot(rgbSW, luma);\n\t\tfloat lumaSE = dot(rgbSE, luma);\n\t\tfloat lumaM  = dot(rgbM,  luma);\n\t\tfloat lumaMin = min(lumaM, min(min(lumaNW, lumaNE), min(lumaSW, lumaSE)));\n\t\tfloat lumaMax = max(lumaM, max(max(lumaNW, lumaNE), max(lumaSW, lumaSE)));\n\t\t\n\t\tvec2 dir;\n\t\tdir.x = -((lumaNW + lumaNE) - (lumaSW + lumaSE));\n\t\tdir.y =  ((lumaNW + lumaSW) - (lumaNE + lumaSE));\n\t\t\n\t\tfloat dirReduce = max((lumaNW + lumaNE + lumaSW + lumaSE) * (0.25 * FXAA_REDUCE_MUL), FXAA_REDUCE_MIN);\n\t\t\n\t\tfloat rcpDirMin = 1.0 / (min(abs(dir.x), abs(dir.y)) + dirReduce);\n\t\tdir = min(vec2(FXAA_SPAN_MAX, FXAA_SPAN_MAX), max(vec2(-FXAA_SPAN_MAX, -FXAA_SPAN_MAX), dir * rcpDirMin)) * u_iViewportSize;\n\t\t\n\t\tvec3 rgbA = 0.5 * (texture2D(tex, fragCoord * u_iViewportSize + dir * (1.0 / 3.0 - 0.5)).xyz + \n\t\t\ttexture2D(tex, fragCoord * u_iViewportSize + dir * (2.0 / 3.0 - 0.5)).xyz);\n\t\tvec3 rgbB = rgbA * 0.5 + 0.25 * (texture2D(tex, fragCoord * u_iViewportSize + dir * -0.5).xyz + \n\t\t\ttexture2D(tex, fragCoord * u_iViewportSize + dir * 0.5).xyz);\n\t\t\n\t\t//return vec4(rgbA,1.0);\n\t\tfloat lumaB = dot(rgbB, luma);\n\t\tif ((lumaB < lumaMin) || (lumaB > lumaMax))\n\t\t\tcolor = vec4(rgbA, 1.0);\n\t\telse\n\t\t\tcolor = vec4(rgbB, 1.0);\n\t\treturn color;\n\t}\n";
Shader.getFXAAShader=function(a){a=a||t.gl;var b=a.shaders[":fxaa"];if(b)return b;var b=new g.Shader(Shader.SCREEN_VERTEX_SHADER,"\n\t\t\tprecision highp float;\n\t\t\tvarying vec2 v_coord;\n\t\t\tuniform sampler2D u_texture;\n\t\t\t"+Shader.FXAA_FUNC+"\n\t\t\t\n\t\t\tvoid main() {\n\t\t\t   gl_FragColor = applyFXAA( u_texture, v_coord * u_viewportSize) ;\n\t\t\t}\n\t\t\t"),c=vec2.fromValues(a.viewport_data[2],a.viewport_data[3]),d=vec2.fromValues(1/a.viewport_data[2],1/a.viewport_data[3]);b.setup=
function(){c[0]=a.viewport_data[2];c[1]=a.viewport_data[3];d[0]=1/a.viewport_data[2];d[1]=1/a.viewport_data[3];this.uniforms({u_viewportSize:c,u_iViewportSize:d})};return a.shaders[":fxaa"]=b};Shader.getFlatShader=function(a){a=a||t.gl;var b=a.shaders[":flat"];if(b)return b;b=new g.Shader(Shader.FLAT_VERTEX_SHADER,Shader.FLAT_FRAGMENT_SHADER);b.uniforms({u_color:[1,1,1,1]});return a.shaders[":flat"]=b};g.create=function(a){function b(a){if(!l.ignore_events){var c=l.mouse.buttons;g.augmentEvent(a,
f);a.eventType=a.eventType||a.type;var d=getTime();p.dragging=a.dragging;p.position[0]=a.canvasx;p.position[1]=a.canvasy;p.x=a.canvasx;p.y=a.canvasy;p.mousex=a.mousex;p.mousey=a.mousey;p.canvasx=a.canvasx;p.canvasy=a.canvasy;p.clientx=a.mousex;p.clienty=a.mousey;p.buttons=a.buttons;p.left_button=!!(p.buttons&g.LEFT_MOUSE_BUTTON_MASK);p.middle_button=!!(p.buttons&g.MIDDLE_MOUSE_BUTTON_MASK);p.right_button=!!(p.buttons&g.RIGHT_MOUSE_BUTTON_MASK);if("mousedown"==a.eventType){0==c&&(f.removeEventListener("mousemove",
b),c=f.ownerDocument,c.addEventListener("mousemove",b),c.addEventListener("mouseup",b));n=d;if(l.onmousedown)l.onmousedown(a);y.trigger(l,"mousedown")}else if("mousemove"==a.eventType){if(l.onmousemove)l.onmousemove(a);y.trigger(l,"mousemove",a)}else if("mouseup"==a.eventType){0==l.mouse.buttons&&(f.addEventListener("mousemove",b),c=f.ownerDocument,c.removeEventListener("mousemove",b),c.removeEventListener("mouseup",b));a.click_time=d-n;if(l.onmouseup)l.onmouseup(a);y.trigger(l,"mouseup",a)}else if("mousewheel"==
a.eventType||"wheel"==a.eventType||"DOMMouseScroll"==a.eventType){a.eventType="mousewheel";a.wheel="wheel"==a.type?-a.deltaY:null!=a.wheelDeltaY?a.wheelDeltaY:-60*a.detail;a.delta=void 0!==a.wheelDelta?a.wheelDelta/40:a.deltaY?-a.deltaY/3:0;if(l.onmousewheel)l.onmousewheel(a);y.trigger(l,"mousewheel",a)}else if("dragstart"==a.eventType){if(l.ondragstart)l.ondragstart(a);y.trigger(l,"dragstart",a)}if(l.onmouse)l.onmouse(a);if(!a.skip_preventDefault)return"mousemove"!=a.eventType&&a.stopPropagation(),
a.preventDefault(),!1}}function c(a){var b=a.changedTouches,c=b[0],d="";if(!(l.ontouch&&!0===l.ontouch(a)||!0===y.trigger(l,a.type,a)||!r||a.touches.length&&a.changedTouches[0].identifier!==a.touches[0].identifier||1<b)){switch(a.type){case "touchstart":d="mousedown";break;case "touchmove":d="mousemove";break;case "touchend":d="mouseup";break;default:return}b=document.createEvent("MouseEvent");b.initMouseEvent(d,!0,!0,window,1,c.screenX,c.screenY,c.clientX,c.clientY,!1,!1,!1,!1,0,null);b.originalEvent=
b;b.is_touch=!0;c.target.dispatchEvent(b);a.preventDefault()}}function d(a){a.eventType=a.type;l.ongesture&&!1===l.ongesture(a)||!1!==y.trigger(l,a.type,a)&&a.preventDefault()}a=a||{};var f=null;if(a.canvas)if("string"==typeof a.canvas){if(f=document.getElementById(a.canvas),!f)throw"Canvas element not found: "+a.canvas;}else f=a.canvas;else{var k=null;a.container&&(k=a.container.constructor===String?document.querySelector(a.container):a.container);if(k&&!a.width){var q=k.getBoundingClientRect();
a.width=q.width;a.height=q.height}f=createCanvas(a.width||800,a.height||600);k&&k.appendChild(f)}"alpha"in a||(a.alpha=!1);var l=null,k=null;2==a.version?k=["webgl2","experimental-webgl2"]:1==a.version||void 0===a.version?k=["webgl","experimental-webgl"]:0===a.version&&(k=["webgl2","experimental-webgl2","webgl","experimental-webgl"]);if(!k)throw"Incorrect WebGL version, must be 1 or 2";q={alpha:void 0===a.alpha?!0:a.alpha,depth:void 0===a.depth?!0:a.depth,stencil:void 0===a.stencil?!0:a.stencil,antialias:void 0===
a.antialias?!0:a.antialias,premultipliedAlpha:void 0===a.premultipliedAlpha?!0:a.premultipliedAlpha,preserveDrawingBuffer:void 0===a.preserveDrawingBuffer?!0:a.preserveDrawingBuffer};for(a=0;a<k.length;++a){try{l=f.getContext(k[a],q)}catch(m){}if(l)break}if(!l){if(f.getContext("webgl"))throw"WebGL supported but not with those parameters";throw"WebGL not supported";}l.webgl_version="WebGL2RenderingContext"===l.constructor.name?2:1;t.gl=l;f.is_webgl=!0;f.gl=l;l.context_id=this.last_context_id++;l.extensions=
{};k=l.getSupportedExtensions();for(a=0;a<k.length;++a)l.extensions[k[a]]=l.getExtension(k[a]);l.HIGH_PRECISION_FORMAT=1==l.webgl_version?l.extensions.OES_texture_half_float?g.HALF_FLOAT_OES:l.extensions.OES_texture_float?g.FLOAT:g.UNSIGNED_BYTE:g.HALF_FLOAT_OES;l.max_texture_units=l.getParameter(l.MAX_TEXTURE_IMAGE_UNITS);l._viewport_func?console.warn("Creating LiteGL context over the same canvas twice"):(l._viewport_func=l.viewport,l.viewport_data=new Float32Array([0,0,l.canvas.width,l.canvas.height]),
l.viewport=function(a,b,c,d){var f=this.viewport_data;f[0]=a|0;f[1]=b|0;f[2]=c|0;f[3]=d|0;this._viewport_func(a,b,c,d)},l.getViewport=function(a){return a?(a[0]=l.viewport_data[0],a[1]=l.viewport_data[1],a[2]=l.viewport_data[2],a[3]=l.viewport_data[3],a):new Float32Array(l.viewport_data)},l.setViewport=function(a,b){l.viewport_data.set(a);b&&(l.viewport_data[1]=this.drawingBufferHeight-a[1]-a[3]);this._viewport_func(a[0],l.viewport_data[1],a[2],a[3])});if(!g.reverse)for(a in g.reverse={},l)l[a]&&
l[a].constructor===Number&&(g.reverse[l[a]]=a);if("undefined"==typeof glMatrix)throw"glMatrix not found, LiteGL requires glMatrix to be included";var n=0;l.shaders={};l.textures={};l.meshes={};l.draw_calls=0;l.makeCurrent=function(){t.gl=this};l.execute=function(a){var b=t.gl;t.gl=this;a();t.gl=b};l.animate=function(a){function b(){if(!l.destroyed){f._requestFrame_id=c(b);var a=getTime(),g=0.001*(a-d);f.mouse&&(f.mouse.last_buttons=k);k=f.mouse.buttons;if(f.onupdate)f.onupdate(g);y.trigger(f,"update",
g);f.ondraw&&(g=t.gl,t.gl=f,f.ondraw(),y.trigger(f,"draw"),t.gl=g);d=a}}if(!1===a)t.cancelAnimationFrame(this._requestFrame_id),this._requestFrame_id=null;else{var c=t.requestAnimationFrame,d=getTime(),f=this,k=0;this._requestFrame_id=c(b)}};l.destroy=function(){v&&(document.removeEventListener("keydown",v),document.removeEventListener("keyup",v));this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas);this.destroyed=!0;t.gl==this&&(t.gl=null)};var p=l.mouse={buttons:0,last_buttons:0,
left_button:!1,middle_button:!1,right_button:!1,position:new Float32Array(2),x:0,y:0,deltax:0,deltay:0,clientx:0,clienty:0,isInsideRect:function(a,b,c,d,f){var k=this.y;f&&(k=l.canvas.height-k);return this.x>a&&this.x<a+c&&k>b&&k<b+d?!0:!1},isButtonPressed:function(a){if(a==g.LEFT_MOUSE_BUTTON)return this.buttons&g.LEFT_MOUSE_BUTTON_MASK;if(a==g.MIDDLE_MOUSE_BUTTON)return this.buttons&g.MIDDLE_MOUSE_BUTTON_MASK;if(a==g.RIGHT_MOUSE_BUTTON)return this.buttons&g.RIGHT_MOUSE_BUTTON_MASK},wasButtonPressed:function(a){var b=
0;a==g.LEFT_MOUSE_BUTTON?b=g.LEFT_MOUSE_BUTTON_MASK:a==g.MIDDLE_MOUSE_BUTTON?b=g.MIDDLE_MOUSE_BUTTON_MASK:a==g.RIGHT_MOUSE_BUTTON&&(b=g.RIGHT_MOUSE_BUTTON_MASK);return this.buttons&b&&!(this.last_buttons&b)}};l.captureMouse=function(a,c){f.addEventListener("mousedown",b);f.addEventListener("mousemove",b);f.addEventListener("dragstart",b);a&&(f.addEventListener("mousewheel",b,!1),f.addEventListener("wheel",b,!1));f.addEventListener("contextmenu",function(a){a.preventDefault();return!1});c&&this.captureTouch(!0)};
var r=!1;l.captureTouch=function(a){r=a;f.addEventListener("touchstart",c,!0);f.addEventListener("touchmove",c,!0);f.addEventListener("touchend",c,!0);f.addEventListener("touchcancel",c,!0);f.addEventListener("gesturestart",d);f.addEventListener("gesturechange",d);f.addEventListener("gestureend",d)};l.keys={};var v=null;l.captureKeys=function(a,b){function c(b){var d=a;b.eventType=b.type;var f=b.target.nodeName.toLowerCase();if("input"!==f&&"textarea"!==f&&"select"!==f){b.character=String.fromCharCode(b.keyCode).toLowerCase();
var f=!1,k=g.mapKeyCode(b.keyCode);k||(k=b.character);b.altKey||b.ctrlKey||b.metaKey||(k&&(l.keys[k]="keydown"==b.type),f=l.keys[b.keyCode],l.keys[b.keyCode]="keydown"==b.type);if(f!=l.keys[b.keyCode]){if("keydown"==b.type&&l.onkeydown)l.onkeydown(b);else if("keyup"==b.type&&l.onkeyup)l.onkeyup(b);y.trigger(l,b.type,b)}if(l.onkey)l.onkey(b);d&&(b.isChar||g.blockable_keys[b.keyIdentifier||b.key])&&b.preventDefault()}}v||(l.keys={},document.addEventListener("keydown",c),document.addEventListener("keyup",
c),v=c)};l.gamepads=null;l.captureGamepads=function(){var a=navigator.getGamepads||navigator.webkitGetGamepads||navigator.mozGetGamepads;a&&(this.gamepads=a.call(navigator))};l.getGamepads=function(a){var b=navigator.getGamepads||navigator.webkitGetGamepads||navigator.mozGetGamepads;if(b){b=b.call(navigator);this.gamepads||(this.gamepads=[]);for(var c=0;4>c;c++){var d=b[c];if(d&&!a){var f=d,k=f.xbox||{axes:[],buttons:{},hat:""};k.axes.lx=f.axes[0];k.axes.ly=f.axes[1];k.axes.rx=f.axes[2];k.axes.ry=
f.axes[3];k.axes.triggers=f.axes[4];for(var g=0;g<f.buttons.length;g++)switch(g){case 0:k.buttons.a=f.buttons[g].pressed;break;case 1:k.buttons.b=f.buttons[g].pressed;break;case 2:k.buttons.x=f.buttons[g].pressed;break;case 3:k.buttons.y=f.buttons[g].pressed;break;case 4:k.buttons.lb=f.buttons[g].pressed;break;case 5:k.buttons.rb=f.buttons[g].pressed;break;case 6:k.buttons.lt=f.buttons[g].pressed;break;case 7:k.buttons.rt=f.buttons[g].pressed;break;case 8:k.buttons.back=f.buttons[g].pressed;break;
case 9:k.buttons.start=f.buttons[g].pressed;break;case 10:k.buttons.ls=f.buttons[g].pressed;break;case 11:k.buttons.rs=f.buttons[g].pressed;break;case 12:f.buttons[g].pressed&&(k.hat+="up");break;case 13:f.buttons[g].pressed&&(k.hat+="down");break;case 14:f.buttons[g].pressed&&(k.hat+="left");break;case 15:f.buttons[g].pressed&&(k.hat+="right");break;case 16:k.buttons.home=f.buttons[g].pressed}f.xbox=k}if(d&&!d.prev_buttons){d.prev_buttons=new Uint8Array(32);f=new CustomEvent("gamepadconnected");
f.eventType=f.type;f.gamepad=d;if(this.ongamepadconnected)this.ongamepadconnected(f);y.trigger(l,"gamepadconnected",f)}if(d)for(k=0;k<d.buttons.length;++k){g=d.buttons[k];g.was_pressed=!1;if(g.pressed&&!d.prev_buttons[k]){g.was_pressed=!0;f=new CustomEvent("gamepadButtonDown");f.eventType=f.type;f.button=g;f.which=k;f.gamepad=d;if(l.onbuttondown)l.onbuttondown(f);y.trigger(l,"buttondown",f)}else if(!g.pressed&&d.prev_buttons[k]){f=new CustomEvent("gamepadButtonUp");f.eventType=f.type;f.button=g;f.which=
k;f.gamepad=d;if(l.onbuttondown)l.onbuttondown(f);y.trigger(l,"buttonup",f)}d.prev_buttons[k]=g.pressed?1:0}}return this.gamepads=b}};l.fullscreen=function(){var a=this.canvas;a.requestFullScreen?a.requestFullScreen():a.webkitRequestFullScreen?a.webkitRequestFullScreen():a.mozRequestFullScreen?a.mozRequestFullScreen():console.error("Fullscreen not supported")};l.snapshot=function(a,b,c,d,f){var k=createCanvas(c,d),g=k.getContext("2d"),m=g.getImageData(0,0,k.width,k.height),q=new Uint8Array(c*d*4);
l.readPixels(a,b,k.width,k.height,l.RGBA,l.UNSIGNED_BYTE,q);m.data.set(q);g.putImageData(m,0,0);if(f)return k;a=createCanvas(c,d);g=a.getContext("2d");g.translate(0,d);g.scale(1,-1);g.drawImage(k,0,0);return a};var s={};l.loadTexture=function(a,b,c){if(this.textures[a])return this.textures[a];if(s[a])return null;var d=new Image;d.url=a;d.onload=function(){var a=g.Texture.fromImage(this,b);a.img=this;l.textures[this.url]=a;delete s[this.url];c&&c(a)};d.src=a;s[a]=!0;return null};l.drawTexture=function(){var a=
mat3.create(),b=vec2.create(),c=vec2.create(),d=vec4.create(),f=vec4.fromValues(1,1,1,1),k=vec2.create(),g={u_texture:0,u_position:b,u_color:f,u_size:c,u_texture_area:d,u_viewport:k,u_transform:a};return function(a,f,m,q,n,p,r,u,s,v,t){b[0]=f;b[1]=m;void 0===q&&(q=a.width);void 0===n&&(n=a.height);c[0]=q;c[1]=n;void 0===p&&(p=0);void 0===r&&(r=0);void 0===u&&(u=a.width);void 0===s&&(s=a.height);d[0]=p/a.width;d[1]=r/a.height;d[2]=(p+u)/a.width;d[3]=(r+s)/a.height;k[0]=this.viewport_data[2];k[1]=this.viewport_data[3];
v=v||Shader.getPartialQuadShader(this);f=Mesh.getScreenQuad(this);a.bind(0);v.uniforms(g);t&&v.uniforms(t);v.draw(f,l.TRIANGLES)}}();l.canvas.addEventListener("webglcontextlost",function(a){a.preventDefault();l.context_lost=!0;if(l.onlosecontext)l.onlosecontext(a)},!1);l.reset=function(){l.viewport(0,0,this.canvas.width,this.canvas.height);l.disable(l.BLEND);l.disable(l.CULL_FACE);l.disable(l.DEPTH_TEST);l.frontFace(l.CCW);l._current_texture_drawto=null;l._current_fbo_color=null;l._current_fbo_depth=
null};l.dump=function(){console.log("userAgent: ",navigator.userAgent);console.log("Supported extensions:");var a=l.getSupportedExtensions();console.log(a.join(","));a="VENDOR VERSION MAX_VERTEX_ATTRIBS MAX_VARYING_VECTORS MAX_VERTEX_UNIFORM_VECTORS MAX_VERTEX_TEXTURE_IMAGE_UNITS MAX_FRAGMENT_UNIFORM_VECTORS MAX_TEXTURE_SIZE MAX_TEXTURE_IMAGE_UNITS".split(" ");console.log("WebGL info:");for(var b in a)console.log(" * "+a[b]+": "+l.getParameter(l[a[b]]));console.log("*************************************************")};
l.reset();return l};g.mapKeyCode=function(a){return{8:"BACKSPACE",9:"TAB",13:"ENTER",16:"SHIFT",17:"CTRL",27:"ESCAPE",32:"SPACE",37:"LEFT",38:"UP",39:"RIGHT",40:"DOWN"}[a]||(65<=a&&90>=a?String.fromCharCode(a):null)};g.dragging=!1;g.last_pos=[0,0];g.augmentEvent=function(a,b){var c=null;b=b||a.target||gl.canvas;c=b.getBoundingClientRect();a.mousex=a.clientX-c.left;a.mousey=a.clientY-c.top;a.canvasx=a.mousex;a.canvasy=c.height-a.mousey;a.deltax=0;a.deltay=0;"mousedown"==a.type?this.dragging=!0:"mousemove"!=
a.type&&"mouseup"==a.type&&0==a.buttons&&(this.dragging=!1);void 0===a.movementX||g.isMobile()?(a.deltax=a.mousex-this.last_pos[0],a.deltay=a.mousey-this.last_pos[1]):(a.deltax=a.movementX,a.deltay=a.movementY);this.last_pos[0]=a.mousex;this.last_pos[1]=a.mousey;a.dragging=this.dragging;a.leftButton=!!(gl.mouse.buttons&g.LEFT_MOUSE_BUTTON_MASK);a.middleButton=!!(gl.mouse.buttons&g.MIDDLE_MOUSE_BUTTON_MASK);a.rightButton=!!(gl.mouse.buttons&g.RIGHT_MOUSE_BUTTON_MASK);a.buttons_mask=0;a.leftButton&&
(a.buttons_mask=1);a.middleButton&&(a.buttons_mask|=2);a.rightButton&&(a.buttons_mask|=4);a.isButtonPressed=function(a){return this.buttons_mask&1<<a}};g.isMobile=function(){return void 0!==this.mobile?this.mobile:t.navigator?navigator.userAgent.match(/iPhone/i)||navigator.userAgent.match(/iPod/i)||navigator.userAgent.match(/iPad/i)||navigator.userAgent.match(/SamsungBrowser/i)||navigator.userAgent.match(/Mobile\ VR/i)||navigator.userAgent.match(/Android/i)?this.mobile=!0:this.mobile=!1:this.mobile=
!1};var y=t.LEvent=g.LEvent={bind:function(a,b,c,d){if(!a)throw"cannot bind event to null";if(!c)throw"cannot bind to null callback";if(a.constructor===String)throw"cannot bind event to a string";var f=a.__levents;f||(Object.defineProperty(a,"__levents",{value:{},enumerable:!1}),f=a.__levents);f.hasOwnProperty(b)?f[b].push([c,d]):f[b]=[[c,d]];if(a.onLEventBinded)a.onLEventBinded(b,c,d)},unbind:function(a,b,c,d){if(!a)throw"cannot unbind event to null";if(!c)throw"cannot unbind from null callback";
if(a.constructor===String)throw"cannot bind event to a string";var f=a.__levents;if(f&&f.hasOwnProperty(b)){for(var k=0,g=f[b].length;k<g;++k){var l=f[b][k];if(l[0]===c&&l[1]===d){f[b].splice(k,1);break}}0==f[b].length&&delete f[b];if(a.onLEventUnbinded)a.onLEventUnbinded(b,c,d)}},unbindAll:function(a,b,c){if(!a)throw"cannot unbind events in null";var d=a.__levents;if(d){if(a.onLEventUnbindAll)a.onLEventUnbindAll(b,c);if(b)for(var f in d){a=d[f];for(var k=a.length-1;0<=k;--k)a[k][1]!=b||c&&c!==a[k][0]||
a.splice(k,1)}else delete a.__levents}},unbindAllEvent:function(a,b){if(!a)throw"cannot unbind events in null";var c=a.__levents;if(c&&(delete c[b],a.onLEventUnbindAll))a.onLEventUnbindAll(b,target_instance,callback)},isBind:function(a,b,c,d){if(!a)throw"LEvent cannot have null as instance";if(a=a.__levents){if(!a.hasOwnProperty(b))return!1;for(var f=0,k=a[b].length;f<k;++f){var g=a[b][f];if(g[0]===c&&g[1]===d)return!0}return!1}},hasBind:function(a,b){if(!a)throw"LEvent cannot have null as instance";
var c=a.__levents;return c&&c.hasOwnProperty(b)&&c[b].length?!0:!1},hasBindTo:function(a,b){if(!a)throw"LEvent cannot have null as instance";var c=a.__levents;if(!c)return!1;for(var d in c)for(var f=c[d],k=0;k<f.length;++k)if(f[k][1]===b)return!0;return!1},trigger:function(a,b,c,d,f){if(!a)throw"cannot trigger event from null";if(a.constructor===String)throw"cannot bind event to a string";a=a.__levents;if(!a||!a.hasOwnProperty(b))return!1;a=a[b];if(d)for(d=a.length-1;0<=d;--d){var k=a[d];if(f){if(k&&
!0===k[0].apply(k[1],c))return!0}else if(k&&!0===k[0].call(k[1],b,c))return!0}else{d=0;for(var g=a.length;d<g;++d)if(k=a[d],f){if(k&&!0===k[0].apply(k[1],c))return!0}else if(k&&!0===k[0].call(k[1],b,c))return!0}return!1},triggerArray:function(a,b,c,d,f){for(var k=!1,g=0,l=a.length;g<l;++g){var m=a[g];if(!m)throw"cannot trigger event from null";if(m.constructor===String)throw"cannot bind event to a string";if((m=m.__levents)&&m.hasOwnProperty(b))if(d)for(var n=m[b].length-1;0<=n;--n){var p=m[b][n];
if(f){if(!0===p[0].apply(p[1],c)){k=!0;break}}else if(!0===p[0].call(p[1],b,c)){k=!0;break}}else for(var n=0,r=m[b].length;n<r;++n)if(p=m[b][n],f){if(!0===p[0].apply(p[1],c)){k=!0;break}}else if(!0===p[0].call(p[1],b,c)){k=!0;break}}return k},extendObject:function(a){a.bind=function(a,c,d){return y.bind(this,a,c,d)};a.trigger=function(a,c){return y.trigger(this,a,c)};a.unbind=function(a,c,d){return y.unbind(this,a,c,instance)};a.unbindAll=function(a,c){return y.unbindAll(this,a,c)}},extendClass:function(a){this.extendObject(a.prototype)}};
t.CLIP_INSIDE=g.CLIP_INSIDE=0;t.CLIP_OUTSIDE=g.CLIP_OUTSIDE=1;t.CLIP_OVERLAP=g.CLIP_OVERLAP=2;t.geo={last_t:-1,createPlane:function(a,b){return new Float32Array([b[0],b[1],b[2],-vec3.dot(a,b)])},distancePointToPlane:function(a,b){return(vec3.dot(a,b)+b[3])/Math.sqrt(b[0]*b[0]+b[1]*b[1]+b[2]*b[2])},distance2PointToPlane:function(a,b){return(vec3.dot(a,b)+b[3])/(b[0]*b[0]+b[1]*b[1]+b[2]*b[2])},projectPointOnLine:function(a,b,c,d){d=d||vec3.create();a=vec3.fromValues(a[0]-b[0],a[1]-b[1],a[2]-b[2]);c=
vec3.fromValues(c[0]-b[0],c[1]-b[1],c[2]-b[2]);a=vec3.dot(a,c)/vec3.dot(c,c);d[0]=b[0]+a[0]*c[0];d[1]=b[1]+a[1]*c[1];d[2]=b[2]+a[2]*c[2];return d},project2DPointOnLine:function(a,b,c,d){d=d||vec2.create();a=vec2.fromValues(a[0]-b[0],a[1]-b[1]);c=vec2.fromValues(c[0]-b[0],c[1]-b[1]);a=vec2.dot(a,c)/vec2.dot(c,c);d[0]=b[0]+a[0]*c[0];d[1]=b[1]+a[1]*c[1];return d},projectPointOnPlane:function(a,b,c,d){d=d||vec3.create();b=vec3.subtract(vec3.create(),a,b);b=vec3.dot(b,c);return vec3.subtract(d,a,vec3.scale(vec3.create(),
c,b))},reflectPointInPlane:function(a,b,c){b=-(-1*(b[0]*c[0]+b[1]*c[1]+b[2]*c[2])+c[0]*a[0]+c[1]*a[1]+c[2]*a[2])/(c[0]*c[0]+c[1]*c[1]+c[2]*c[2]);return vec3.fromValues(a[0]+b*c[0]*2,a[1]+b*c[1]*2,a[2]+b*c[2]*2)},testRayPlane:function(a,b,c,d,f){c=vec3.dot(c,d)-vec3.dot(d,a);d=vec3.dot(d,b);if(Math.abs(d)<EPSILON)return!1;d=this.last_t=c/d;if(0>d)return!1;f&&vec3.add(f,a,vec3.scale(f,b,d));return!0},testSegmentPlane:function(){var a=vec3.create();return function(b,c,d,f,k){d=vec3.dot(d,f)-vec3.dot(f,
b);c=vec3.sub(a,c,b);f=vec3.dot(f,c);if(Math.abs(f)<EPSILON)return!1;f=this.last_t=d/f;if(0>f||1<f)return!1;k&&vec3.add(k,b,vec3.scale(k,c,f));return!0}}(),testRaySphere:function(){var a=vec3.create();return function(b,c,d,f,k,g){var l=vec3.subtract(a,b,d),m=c[0]*c[0]+c[1]*c[1]+c[2]*c[2];d=2*l[0]*c[0]+2*l[1]*c[1]+2*l[2]*c[2];f=d*d-4*m*(l[0]*l[0]+l[1]*l[1]+l[2]*l[2]-f*f);if(0>f)return!1;if(k){f=Math.sqrt(f);l=1/(2*m);m=(-d+f)*l;d=(-d-f)*l;d=m<d?m:d;if(void 0!==g&&d>g)return!1;this.last_t=d;vec3.add(k,
b,vec3.scale(k,c,d))}return!0}}(),hitTestTriangle:function(){var a=vec3.create(),b=vec3.create(),c=vec3.create(),d=vec3.create(),f=vec3.create();return function(k,g,l,m,n,p){vec3.subtract(a,m,l);vec3.subtract(b,n,l);vec3.cross(c,a,b);vec3.normalize(c,c);m=vec3.dot(c,vec3.subtract(d,l,k))/vec3.dot(c,g);if(0<m){vec3.add(f,k,vec3.scale(d,g,m));var r=vec3.subtract(d,d,l);l=vec3.dot(b,b);n=vec3.dot(b,a);var v=vec3.dot(b,r),s=vec3.dot(a,a),r=vec3.dot(a,r),u=l*s-n*n,s=(s*v-n*r)/u;l=(l*r-n*v)/u;if(0<=s&&
0<=l&&1>=s+l)return this.last_t=m,p&&vec3.add(p,k,vec3.scale(d,g,m)),!0}return!1}}(),testRayCylinder:function(a,b,c,d,f,k){var g=vec3.clone(a);a=vec3.add(vec3.create(),a,vec3.scale(vec3.create(),b,1E5));var l=0;b=vec3.subtract(vec3.create(),d,c);var m=vec3.subtract(vec3.create(),g,c);c=vec3.subtract(vec3.create(),a,g);d=vec3.dot(m,b);a=vec3.dot(c,b);b=vec3.dot(b,b);if(0>d&&0>d+a||d>b&&d+a>b)return!1;var n=vec3.dot(c,c),p=vec3.dot(m,c),l=b*n-a*a;f=vec3.dot(m,m)-f*f;var r=b*f-d*d;if(Math.abs(l)<EPSILON){if(0<
r)return!1;k&&vec3.add(k,g,vec3.scale(k,c,0>d?-p/n:d>b?(a-p)/n:0));return!0}m=b*p-a*d;r=m*m-l*r;if(0>r)return!1;l=(-m-Math.sqrt(r))/l;if(0>l||1<l)return!1;if(0>d+l*a){if(0>=a)return!1;l=-d/a;k&&vec3.add(k,g,vec3.scale(k,c,l));return 0>=f+2*l*(p+l*n)}if(d+l*a>b){if(0<=a)return!1;l=(b-d)/a;k&&vec3.add(k,g,vec3.scale(k,c,l));return 0>=f+b-2*d+l*(2*(p-a)+l*n)}this.last_t=l;k&&vec3.add(k,g,vec3.scale(k,c,l));return!0},testRayBox:function(){var a=new Float32Array(3),b=new Float32Array(3),c=new Float32Array(3);
return function(d,f,k,g,l,m){m=m||Number.MAX_VALUE;var n=!0,p=0;a.fill(0);c.fill(0);b.fill(0);for(p=0;3>p;++p)d[p]<k[p]?(a[p]=1,b[p]=k[p],n=!1):d[p]>g[p]?(a[p]=0,b[p]=g[p],n=!1):a[p]=2;if(n)return this.last_t=0,l&&vec3.copy(l,d),!0;for(p=0;3>p;++p)c[p]=2!=a[p]&&0!=f[p]?(b[p]-d[p])/f[p]:-1;n=0;for(p=1;3>p;p++)c[n]<c[p]&&(n=p);if(0>c[n]||c[n]>m)return!1;this.last_t=c[n];for(p=0;3>p;++p)if(n!=p){m=d[p]+c[n]*f[p];if(m<k[p]||m>g[p])return!1;l&&(l[p]=m)}else l&&(l[p]=b[p]);return!0}}(),testRayBBox:function(){var a=
mat4.create(),b=vec3.create(),c=vec3.create();return function(d,f,k,g,l,m,n){if(!d||!f||!k)throw"parameters missing";g&&(mat4.invert(a,g),vec3.add(b,d,f),d=vec3.transformMat4(c,d,a),vec3.transformMat4(b,b,a),vec3.sub(b,b,d),f=vec3.normalize(b,b));d=this.testRayBox(d,f,k.subarray(6,9),k.subarray(9,12),l,m);!n&&g&&l&&vec3.transformMat4(l,l,g);return d}}(),testPointBBox:function(a,b){return a[0]<b[6]||a[0]>b[9]||a[1]<b[7]||a[0]>b[10]||a[2]<b[8]||a[0]>b[11]?!1:!0},testBBoxBBox:function(a,b){if(Math.abs(b[0]-
a[0])>a[3]+b[3]||Math.abs(b[1]-a[1])>a[4]+b[4]||Math.abs(b[2]-a[2])>a[5]+b[5])return!1;var c=BBox.getMin(b);geo.testPointBBox(c,a)&&(c=BBox.getMax(b),geo.testPointBBox(c,a));return!0},testSphereBBox:function(a,b,c){for(var d=0,f=BBox.getMin(c),k=BBox.getMax(c),g=0;3>g;++g)a[g]<f[g]?(c=a[g]-f[g],d+=c*c):a[g]>k[g]&&(c=a[g]-k[g],d+=c*c);return d<=b*b?!0:!1},closestPointBetweenLines:function(a,b,c,d,f,k){b=vec3.subtract(vec3.create(),b,a);d=vec3.subtract(vec3.create(),d,c);var g=vec3.subtract(vec3.create(),
a,c),l=vec3.dot(b,b),m=vec3.dot(b,d),n=vec3.dot(d,d),p=vec3.dot(b,g),r=vec3.dot(d,g),v=l*n-m*m,s;v<EPSILON?(s=0,l=m>n?p/m:r/n):(s=(m*r-n*p)/v,l=(l*r-m*p)/v);f&&vec3.add(f,a,vec3.scale(vec3.create(),b,s));k&&vec3.add(k,c,vec3.scale(vec3.create(),d,l));a=vec3.add(vec3.create(),g,vec3.subtract(vec3.create(),vec3.scale(vec3.create(),b,s),vec3.scale(vec3.create(),d,l)));return vec3.length(a)},extractPlanes:function(a,b){function c(a){var c=b.subarray(a,a+3),c=vec3.length(c);0!==c&&(c=1/c,b[a]*=c,b[a+1]*=
c,b[a+2]*=c,b[a+3]*=c)}b=b||new Float32Array(24);b.set([a[3]-a[0],a[7]-a[4],a[11]-a[8],a[15]-a[12]],0);c(0);b.set([a[3]+a[0],a[7]+a[4],a[11]+a[8],a[15]+a[12]],4);c(4);b.set([a[3]+a[1],a[7]+a[5],a[11]+a[9],a[15]+a[13]],8);c(8);b.set([a[3]-a[1],a[7]-a[5],a[11]-a[9],a[15]-a[13]],12);c(12);b.set([a[3]-a[2],a[7]-a[6],a[11]-a[10],a[15]-a[14]],16);c(16);b.set([a[3]+a[2],a[7]+a[6],a[11]+a[10],a[15]+a[14]],20);c(20);return b},frustumTestBox:function(a,b){var c=0,d=0,c=planeBoxOverlap(a.subarray(0,4),b);if(c==
CLIP_OUTSIDE)return CLIP_OUTSIDE;d+=c;c=planeBoxOverlap(a.subarray(4,8),b);if(c==CLIP_OUTSIDE)return CLIP_OUTSIDE;d+=c;c=planeBoxOverlap(a.subarray(8,12),b);if(c==CLIP_OUTSIDE)return CLIP_OUTSIDE;d+=c;c=planeBoxOverlap(a.subarray(12,16),b);if(c==CLIP_OUTSIDE)return CLIP_OUTSIDE;d+=c;c=planeBoxOverlap(a.subarray(16,20),b);if(c==CLIP_OUTSIDE)return CLIP_OUTSIDE;d+=c;c=planeBoxOverlap(a.subarray(20,24),b);return c==CLIP_OUTSIDE?CLIP_OUTSIDE:0==d+c?CLIP_INSIDE:CLIP_OVERLAP},frustumTestSphere:function(a,
b,c){var d,f=!1;d=distanceToPlane(a.subarray(0,4),b);if(d<-c)return CLIP_OUTSIDE;d>=-c&&d<=c&&(f=!0);d=distanceToPlane(a.subarray(4,8),b);if(d<-c)return CLIP_OUTSIDE;d>=-c&&d<=c&&(f=!0);d=distanceToPlane(a.subarray(8,12),b);if(d<-c)return CLIP_OUTSIDE;d>=-c&&d<=c&&(f=!0);d=distanceToPlane(a.subarray(12,16),b);if(d<-c)return CLIP_OUTSIDE;d>=-c&&d<=c&&(f=!0);d=distanceToPlane(a.subarray(16,20),b);if(d<-c)return CLIP_OUTSIDE;d>=-c&&d<=c&&(f=!0);d=distanceToPlane(a.subarray(20,24),b);if(d<-c)return CLIP_OUTSIDE;
d>=-c&&d<=c&&(f=!0);return f?CLIP_OVERLAP:CLIP_INSIDE},testPoint2DInPolygon:function(a,b){for(var c=!1,d=-1,f=a.length,k=f-1;++d<f;k=d)(a[d][1]<=b[1]&&b[1]<a[k][1]||a[k][1]<=b[1]&&b[1]<a[d][1])&&b[0]<(a[k][0]-a[d][0])*(b[1]-a[d][1])/(a[k][1]-a[d][1])+a[d][0]&&(c=!c);return c}};t.BBox=g.BBox={center:0,halfsize:3,min:6,max:9,radius:12,data_length:13,corners:[vec3.fromValues(1,1,1),vec3.fromValues(1,1,-1),vec3.fromValues(1,-1,1),vec3.fromValues(1,-1,-1),vec3.fromValues(-1,1,1),vec3.fromValues(-1,1,-1),
vec3.fromValues(-1,-1,1),vec3.fromValues(-1,-1,-1)],create:function(){return new Float32Array(13)},clone:function(a){return new Float32Array(a)},copy:function(a,b){a.set(b);return a},fromPoint:function(a){var b=this.create();b.set(a,0);b.set(a,6);b.set(a,9);return b},fromMinMax:function(a,b){var c=this.create();this.setMinMax(c,a,b);return c},fromCenterHalfsize:function(a,b){var c=this.create();this.setCenterHalfsize(c,a,b);return c},fromPoints:function(a){var b=this.create();this.setFromPoints(b,
a);return b},setFromPoints:function(a,b){var c=a.subarray(6,9),d=a.subarray(9,12);c[0]=b[0];c[1]=b[1];c[2]=b[2];d.set(c);for(var f=3,k=b.length;f<k;f+=3){var g=b[f],l=b[f+1],m=b[f+2];g<c[0]?c[0]=g:g>d[0]&&(d[0]=g);l<c[1]?c[1]=l:l>d[1]&&(d[1]=l);m<c[2]?c[2]=m:m>d[2]&&(d[2]=m)}a[0]=0.5*(c[0]+d[0]);a[1]=0.5*(c[1]+d[1]);a[2]=0.5*(c[2]+d[2]);a[3]=d[0]-a[0];a[4]=d[1]-a[1];a[5]=d[2]-a[2];a[12]=Math.sqrt(a[3]*a[3]+a[4]*a[4]+a[5]*a[5]);return a},setMinMax:function(a,b,c){a[6]=b[0];a[7]=b[1];a[8]=b[2];a[9]=
c[0];a[10]=c[1];a[11]=c[2];var d=a.subarray(3,6);vec3.sub(d,c,b);vec3.scale(d,d,0.5);a[0]=c[0]-d[0];a[1]=c[1]-d[1];a[2]=c[2]-d[2];a[12]=vec3.length(a.subarray(3,6));return a},setCenterHalfsize:function(a,b,c,d){a[0]=b[0];a[1]=b[1];a[2]=b[2];a[3]=c[0];a[4]=c[1];a[5]=c[2];a[6]=a[0]-a[3];a[7]=a[1]-a[4];a[8]=a[2]-a[5];a[9]=a[0]+a[3];a[10]=a[1]+a[4];a[11]=a[2]+a[5];a[12]=d?d:vec3.length(c);return a},transformMat4:function(){for(var a=0,b=0,c=0,d=new Float32Array(24),f=[],k=0;24>k;k+=3)f.push(d.subarray(k,
k+3));return function(k,g,m){var n=g[0],p=g[1],r=g[2];a=g[3];b=g[4];c=g[5];g=this.corners;for(var v=0;8>v;++v){var s=g[v],u=f[v];u[0]=a*s[0]+n;u[1]=b*s[1]+p;u[2]=c*s[2]+r;mat4.multiplyVec3(u,m,u)}return this.setFromPoints(k,d)}}(),getCorners:function(a,b){var c=a.subarray(3,6),d=null;b?(b.set(this.corners),d=b):d=new Float32Array(this.corners);for(var f=0;8>f;++f){var k=d.subarray(3*f,3*f+3);vec3.multiply(k,c,k);vec3.add(k,k,a)}return d},merge:function(a,b,c){var d=a.subarray(6,9),f=a.subarray(9,
12);vec3.min(d,b.subarray(6,9),c.subarray(6,9));vec3.max(f,b.subarray(9,12),c.subarray(9,12));return BBox.setMinMax(a,d,f)},extendToPoint:function(a,b){b[0]<a[6]?a[6]=b[0]:b[0]>a[9]&&(a[9]=b[0]);b[1]<a[7]?a[7]=b[1]:b[1]>a[10]&&(a[10]=b[1]);b[2]<a[8]?a[8]=b[2]:b[2]>a[11]&&(a[11]=b[2]);var c=a.subarray(6,9),d=a.subarray(9,12),c=vec3.add(a.subarray(0,3),c,d);vec3.scale(c,c,0.5);vec3.subtract(a.subarray(3,6),d,c);a[12]=vec3.length(a.subarray(3,6));return a},clampPoint:function(a,b,c){a[0]=Math.clamp(c[0],
b[0]-b[3],b[0]+b[3]);a[1]=Math.clamp(c[1],b[1]-b[4],b[1]+b[4]);a[2]=Math.clamp(c[2],b[2]-b[5],b[2]+b[5])},isPointInside:function(a,b){return a[0]-a[3]>b[0]||a[1]-a[4]>b[1]||a[2]-a[5]>b[2]||a[0]+a[3]<b[0]||a[1]+a[4]<b[1]||a[2]+a[5]<b[2]?!1:!0},getCenter:function(a){return a.subarray(0,3)},getHalfsize:function(a){return a.subarray(3,6)},getMin:function(a){return a.subarray(6,9)},getMax:function(a){return a.subarray(9,12)},getRadius:function(a){return a[12]}};t.distanceToPlane=g.distanceToPlane=function(a,
b){return vec3.dot(a,b)+a[3]};t.planeBoxOverlap=g.planeBoxOverlap=function(a,b){var c=a[3],d=Math.abs(b[3]*a[0])+Math.abs(b[4]*a[1])+Math.abs(b[5]*a[2]),c=vec3.dot(a,b)+c;return c<=-d?CLIP_OUTSIDE:c<=d?CLIP_OVERLAP:CLIP_INSIDE};t.Octree=g.Octree=function(a,b,c){this.root=null;this.total_nodes=this.total_depth=0;a&&(this.buildFromMesh(a,b,c),this.total_nodes=this.trim())};Octree.MAX_NODE_TRIANGLES_RATIO=0.1;Octree.MAX_OCTREE_DEPTH=8;Octree.OCTREE_MARGIN_RATIO=0.01;Octree.OCTREE_MIN_MARGIN=0.1;Octree.NEAREST=
0;Octree.FIRST=1;Octree.ALL=2;Octree.prototype.buildFromMesh=function(a,b,c){this.total_nodes=this.total_depth=0;b=b||0;var d=a.getBuffer("vertices").data;if(a=a.getIndexBuffer("triangles"))a=a.data;c||(c=a?a.length:d.length/3);var f=null;this.root=f=a?this.computeAABBFromIndices(d,a,b,c):this.computeAABB(d);this.total_nodes=1;this.total_triangles=a?a.length/3:d.length/9;this.max_node_triangles=this.total_triangles*Octree.MAX_NODE_TRIANGLES_RATIO;var k=vec3.create();vec3.scale(k,f.size,Octree.OCTREE_MARGIN_RATIO);
k[0]<Octree.OCTREE_MIN_MARGIN&&(k[0]=Octree.OCTREE_MIN_MARGIN);k[1]<Octree.OCTREE_MIN_MARGIN&&(k[1]=Octree.OCTREE_MIN_MARGIN);k[2]<Octree.OCTREE_MIN_MARGIN&&(k[2]=Octree.OCTREE_MIN_MARGIN);vec3.sub(f.min,f.min,k);vec3.add(f.max,f.max,k);f.faces=[];f.inside=0;k=b+c;if(a)for(;b<k;b+=3){var g=new Float32Array([d[3*a[b]],d[3*a[b]+1],d[3*a[b]+2],d[3*a[b+1]],d[3*a[b+1]+1],d[3*a[b+1]+2],d[3*a[b+2]],d[3*a[b+2]+1],d[3*a[b+2]+2],b/3]);this.addToNode(g,f,0)}else for(b*=3;b<3*c;b+=9)g=new Float32Array(10),g.set(d.subarray(b,
b+9)),g[9]=b/9,this.addToNode(g,f,0);return f};Octree.prototype.addToNode=function(a,b,c){b.inside+=1;if(b.c){var d=this.computeAABB(a),f=!1,k;for(k in b.c){var g=b.c[k];if(Octree.isInsideAABB(d,g)){this.addToNode(a,g,c+1);f=!0;break}}f||(null==b.faces&&(b.faces=[]),b.faces.push(a))}else if(null==b.faces&&(b.faces=[]),b.faces.push(a),b.faces.length>this.max_node_triangles&&c<Octree.MAX_OCTREE_DEPTH){this.splitNode(b);this.total_depth<c+1&&(this.total_depth=c+1);var l=b.faces.concat();b.faces=null;
for(k in l){a=l[k];var d=this.computeAABB(a),f=!1,m;for(m in b.c)if(g=b.c[m],Octree.isInsideAABB(d,g)){this.addToNode(a,g,c+1);f=!0;break}f||(null==b.faces&&(b.faces=[]),b.faces.push(a))}}};Octree.prototype.octree_pos_ref=[[0,0,0],[0,0,1],[0,1,0],[0,1,1],[1,0,0],[1,0,1],[1,1,0],[1,1,1]];Octree.prototype.splitNode=function(a){a.c=[];var b=[0.5*(a.max[0]-a.min[0]),0.5*(a.max[1]-a.min[1]),0.5*(a.max[2]-a.min[2])],c;for(c in this.octree_pos_ref){var d=this.octree_pos_ref[c],f={};this.total_nodes+=1;f.min=
[a.min[0]+b[0]*d[0],a.min[1]+b[1]*d[1],a.min[2]+b[2]*d[2]];f.max=[f.min[0]+b[0],f.min[1]+b[1],f.min[2]+b[2]];f.faces=null;f.inside=0;a.c.push(f)}};Octree.prototype.computeAABB=function(a){for(var b=new Float32Array([a[0],a[1],a[2]]),c=new Float32Array([a[0],a[1],a[2]]),d=0;d<a.length;d+=3)for(var f=0;3>f;f++)b[f]>a[d+f]&&(b[f]=a[d+f]),c[f]<a[d+f]&&(c[f]=a[d+f]);return{min:b,max:c,size:vec3.sub(vec3.create(),c,b)}};Octree.prototype.computeAABBFromIndices=function(a,b,c,d){c=c||0;d=d||b.length;for(var f=
b[c],k=new Float32Array([a[3*f],a[3*f+1],a[3*f+2]]),g=new Float32Array([a[3*f],a[3*f+1],a[3*f+2]]),l=c+1;l<c+d;++l)for(var f=3*b[l],m=0;3>m;m++)k[m]>a[f+m]&&(k[m]=a[f+m]),g[m]<a[f+m]&&(g[m]=a[f+m]);return{min:k,max:g,size:vec3.sub(vec3.create(),g,k)}};Octree.prototype.trim=function(a){a=a||this.root;if(!a.c)return 1;for(var b=1,c=[],d=a.c,f=0;f<d.length;++f)d[f].inside&&(c.push(d[f]),b+=this.trim(d[f]));a.c=c;return b};Octree.prototype.testRay=function(){var a=vec3.create(),b=vec3.create(),c=vec3.create(),
d=vec3.create();return function(f,k,g,l,m,n){n=n||Octree.NEAREST;if(!this.root)throw"Error: octree not build";a.set(f);b.set(k);c.set(this.root.min);d.set(this.root.max);g=Octree.hitTestBox(a,b,c,d);if(!g)return null;g=Octree.testRayInNode(this.root,a,b,m,n);if(null==g)return null;if(n==Octree.ALL)return g;k=vec3.scale(vec3.create(),k,g.t);vec3.add(k,k,f);g.pos=k;return g}}();Octree.testRayInNode=function(a,b,c,d,f){var k=null,g=null;if(a.faces)for(var l=0,m=a.faces.length;l<m;++l){var n=a.faces[l],
k=Octree.hitTestTriangle(b,c,n.subarray(0,3),n.subarray(3,6),n.subarray(6,9),d);if(null!=k){if(f==Octree.FIRST)return k;f==Octree.ALL?(g||(g=[]),g.push(k)):(k.face=n,g?g.mergeWith(k):g=k)}}var m=vec3.create(),n=vec3.create(),p;if(a.c)for(l=0;l<a.c.length;++l)if(p=a.c[l],m.set(p.min),n.set(p.max),k=Octree.hitTestBox(b,c,m,n),null!=k&&!(f!=Octree.ALL&&g&&k.t>g.t)&&(k=Octree.testRayInNode(p,b,c,d,f),null!=k)){if(f==Octree.FIRST)return k;f==Octree.ALL?(g||(g=[]),g.push(k)):g?g.mergeWith(k):g=k}return g};
Octree.prototype.testSphere=function(a,b){a=vec3.clone(a);if(!this.root)throw"Error: octree not build";var c=b*b;return Octree.testSphereBox(a,c,vec3.clone(this.root.min),vec3.clone(this.root.max))?Octree.testSphereInNode(this.root,a,c):!1};Octree.testSphereInNode=function(a,b,c){if(a.faces)for(var d=0,f=a.faces.length;d<f;++d){var k=a.faces[d];if(Octree.testSphereTriangle(b,c,k.subarray(0,3),k.subarray(3,6),k.subarray(6,9)))return!0}var f=vec3.create(),k=vec3.create(),g;if(a.c)for(d=0;d<a.c.length;++d)if(g=
a.c[d],f.set(g.min),k.set(g.max),Octree.testSphereBox(b,c,f,k)&&Octree.testSphereInNode(g,b,c))return!0;return!1};Octree.isInsideAABB=function(a,b){return a.min[0]<b.min[0]||a.min[1]<b.min[1]||a.min[2]<b.min[2]||a.max[0]>b.max[0]||a.max[1]>b.max[1]||a.max[2]>b.max[2]?!1:!0};Octree.hitTestBox=function(){var a=vec3.create(),b=vec3.create(),c=vec3.create(),d=vec3.create(),f=vec3.create(),k=vec3.create(),g=vec3.fromValues(1E-6,1E-6,1E-6);return function(l,m,n,p){vec3.subtract(a,n,l);vec3.subtract(b,p,
l);if(0>vec3.maxValue(a)&&0<vec3.minValue(b))return new HitTest(0,l,m);c[0]=1/m[0];c[1]=1/m[1];c[2]=1/m[2];vec3.multiply(a,a,c);vec3.multiply(b,b,c);vec3.min(d,a,b);vec3.max(f,a,b);var r=vec3.maxValue(d),v=vec3.minValue(f);return 0<r&&r<v?(l=vec3.add(vec3.create(),vec3.scale(k,m,r),l),vec3.add(n,n,g),vec3.subtract(n,n,g),new HitTest(r,l,vec3.fromValues((l[0]>p[0])-(l[0]<n[0]),(l[1]>p[1])-(l[1]<n[1]),(l[2]>p[2])-(l[2]<n[2])))):null}}();Octree.hitTestTriangle=function(){var a=vec3.create(),b=vec3.create(),
c=vec3.create(),d=vec3.create();return function(f,k,g,l,m,n){vec3.subtract(a,l,g);vec3.subtract(b,m,g);l=vec3.cross(vec3.create(),a,b);vec3.normalize(l,l);if(!n&&0<vec3.dot(l,k))return null;n=vec3.dot(l,vec3.subtract(d,g,f))/vec3.dot(l,k);if(0<n){k=vec3.scale(vec3.create(),k,n);vec3.add(k,k,f);vec3.subtract(c,k,g);f=vec3.dot(b,b);g=vec3.dot(b,a);m=vec3.dot(b,c);var p=vec3.dot(a,a),r=vec3.dot(a,c),v=f*p-g*g,p=(p*m-g*r)/v;f=(f*r-g*m)/v;if(0<=p&&0<=f&&1>=p+f)return new HitTest(n,k,l)}return null}}();
Octree.testSphereTriangle=function(){var a=vec3.create(),b=vec3.create(),c=vec3.create(),d=vec3.create(),f=vec3.create(),k=vec3.create(),g=vec3.create(),l=vec3.create();return function(m,n,p,r,v){vec3.sub(a,p,m);vec3.sub(b,r,m);vec3.sub(c,v,m);vec3.sub(d,b,a);vec3.sub(f,c,a);vec3.cross(l,d,f);m=vec3.dot(a,l);p=vec3.dot(l,l);m=m*m>n*p;var s=vec3.dot(a,a),u=vec3.dot(a,b),t=vec3.dot(a,c),w=vec3.dot(b,b),y=vec3.dot(b,c),z=vec3.dot(c,c);p=s>n&u>s&t>s;r=w>n&u>w&y>w;v=z>n&t>z&y>z;var s=u-s,u=y-w,C=t-z;vec3.sub(k,
c,b);vec3.sub(g,a,c);w=vec3.dot(d,d);z=vec3.dot(k,k);t=vec3.dot(g,g);y=vec3.scale(vec3.create(),a,w);vec3.sub(y,y,vec3.scale(vec3.create(),d,s));s=vec3.scale(vec3.create(),b,z);vec3.sub(s,s,vec3.scale(vec3.create(),k,u));u=vec3.scale(vec3.create(),c,t);vec3.sub(u,u,vec3.scale(vec3.create(),g,C));var J=vec3.scale(vec3.create(),c,w),J=vec3.sub(J,J,y),B=vec3.scale(vec3.create(),a,z),B=vec3.sub(B,B,s),C=vec3.scale(vec3.create(),b,t),C=vec3.sub(C,C,u),w=vec3.dot(y,y)>n*w*w&0<vec3.dot(y,J),z=vec3.dot(s,
s)>n*z*z&0<vec3.dot(s,B);n=vec3.dot(u,u)>n*t*t&0<vec3.dot(u,C);return!(m|p|r|v|w|z|n)}}();Octree.testSphereBox=function(a,b,c,d){for(var f,k=0,g=0;3>g;++g)a[g]<c[g]?(f=a[g]-c[g],k+=f*f):a[g]>d[g]&&(f=a[g]-d[g],k+=f*f);return k<=b?!0:!1};t.HitTest=g.HitTest=function(a,b,c){this.t=arguments.length?a:Number.MAX_VALUE;this.hit=b;this.normal=c;this.face=null};HitTest.prototype={mergeWith:function(a){0<a.t&&a.t<this.t&&(this.t=a.t,this.hit=a.hit,this.normal=a.normal,this.face=a.face)}};t.Ray=g.Ray=function(a,
b){this.origin=vec3.create();this.direction=vec3.create();this.collision_point=vec3.create();this.t=-1;a&&this.origin.set(a);b&&this.direction.set(b)};Ray.prototype.testPlane=function(a,b){var c=geo.testRayPlane(this.origin,this.direction,a,b,this.collision_point);this.t=geo.last_t;return c};Ray.prototype.testSphere=function(a,b,c){a=geo.testRaySphere(this.origin,this.direction,a,b,this.collision_point,c);this.t=geo.last_t;return a};Ray.prototype.testBBox=function(a,b,c,d){a=geo.testRayBBox(this.origin,
this.direction,a,c,this.collision_point,b,d);this.t=geo.last_t;return a};t.Raytracer=g.Raytracer=function(a,b){this.viewport=vec4.create();this.ray00=vec3.create();this.ray10=vec3.create();this.ray01=vec3.create();this.ray11=vec3.create();this.eye=vec3.create();this.setup(a,b)};Raytracer.prototype.setup=function(a,b){b=b||gl.viewport_data;this.viewport.set(b);var c=b[0],d=c+b[2],f=b[1],g=f+b[3];vec3.set(this.ray00,c,f,1);vec3.set(this.ray10,d,f,1);vec3.set(this.ray01,c,g,1);vec3.set(this.ray11,d,
g,1);vec3.unproject(this.ray00,this.ray00,a,b);vec3.unproject(this.ray10,this.ray10,a,b);vec3.unproject(this.ray01,this.ray01,a,b);vec3.unproject(this.ray11,this.ray11,a,b);c=this.eye;vec3.unproject(c,c,a,b);vec3.subtract(this.ray00,this.ray00,c);vec3.subtract(this.ray10,this.ray10,c);vec3.subtract(this.ray01,this.ray01,c);vec3.subtract(this.ray11,this.ray11,c)};Raytracer.prototype.getRayForPixel=function(){var a=vec3.create(),b=vec3.create();return function(c,d,f){f=f||vec3.create();c=(c-this.viewport[0])/
this.viewport[2];d=1-(d-this.viewport[1])/this.viewport[3];vec3.lerp(a,this.ray00,this.ray10,c);vec3.lerp(b,this.ray01,this.ray11,c);vec3.lerp(f,a,b,d);return vec3.normalize(f,f)}}();var W=mat4.create();Raytracer.hitTestBox=function(a,b,c,d,f){var g=new Float32Array(30);f&&(f=mat4.invert(W,f),a=mat4.multiplyVec3(g.subarray(3,6),f,a),b=mat4.rotateVec3(g.subarray(6,9),f,b));var q=vec3.subtract(g.subarray(9,12),c,a);vec3.divide(q,q,b);var l=vec3.subtract(g.subarray(12,15),d,a);vec3.divide(l,l,b);f=vec3.min(g.subarray(15,
18),q,l);q=vec3.max(g.subarray(18,21),q,l);f=vec3.maxValue(f);q=vec3.minValue(q);return 0<f&&f<=q?(b=vec3.scale(g.subarray(21,24),b,f),vec3.add(b,a,b),vec3.addValue(g.subarray(24,27),c,1E-6),vec3.subValue(g.subarray(27,30),d,1E-6),new HitTest(f,b,vec3.fromValues((b[0]>d[0])-(b[0]<c[0]),(b[1]>d[1])-(b[1]<c[1]),(b[2]>d[2])-(b[2]<c[2])))):null};Raytracer.hitTestSphere=function(a,b,c,d){var f=vec3.subtract(vec3.create(),a,c),g=vec3.dot(b,b),q=2*vec3.dot(b,f),f=vec3.dot(f,f)-d*d,f=q*q-4*g*f;return 0<f?
(g=(-q-Math.sqrt(f))/(2*g),a=vec3.add(vec3.create(),a,vec3.scale(vec3.create(),b,g)),new HitTest(g,a,vec3.scale(vec3.create(),vec3.subtract(vec3.create(),a,c),1/d))):null};Raytracer.hitTestTriangle=function(a,b,c,d,f){var g=vec3.subtract(vec3.create(),d,c),q=vec3.subtract(vec3.create(),f,c);f=vec3.cross(vec3.create(),g,q);vec3.normalize(f,f);d=vec3.dot(f,vec3.subtract(vec3.create(),c,a))/vec3.dot(f,b);if(0<d){a=vec3.add(vec3.create(),a,vec3.scale(vec3.create(),b,d));var l=vec3.subtract(vec3.create(),
a,c);c=vec3.dot(q,q);b=vec3.dot(q,g);var q=vec3.dot(q,l),m=vec3.dot(g,g),g=vec3.dot(g,l),l=c*m-b*b,m=(m*q-b*g)/l,g=(c*g-b*q)/l;if(0<=m&&0<=g&&1>=m+g)return new HitTest(d,a,f)}return null};Mesh.parseOBJ=function(a,b){function c(a){var b,c,d,f=!1;if(-1==a.indexOf("-")){var g=w.get(a);if(void 0!==g)return g}else f=!0;d||(d=a.split("/"));if(1==d.length)d=c=b=parseInt(d[0]);else if(2==d.length)b=parseInt(d[0]),c=parseInt(d[1]),d=b;else if(3==d.length)b=parseInt(d[0]),c=parseInt(d[1]),d=parseInt(d[2]);
else return console.log("Problem parsing: unknown number of values per face"),-1;0>b&&(b=q.length/3+b+1);0>d&&(d=l.length/2+d+1);0>c&&(c=m.length/2+c+1);if(f&&(a=b+"/"+c+"/"+d,g=w.get(a),void 0!==g))return g;b-=1;c-=1;d-=1;n.push(q[3*b+0],q[3*b+1],q[3*b+2]);m.length&&r.push(m[2*c+0],m[2*c+1]);l.length&&p.push(l[3*d+0],l[3*d+1],l[3*d+2]);g=y;w.set(a,g);++y;return g}function d(a){a={name:a||"",material:"",start:-1,length:-1,indices:[]};v.push(a);return a}function f(a){if(!t.material)return t.material=
a+k,s[a]=t;var b=s[a];b||(b=d(u+"_"+a),b.material=a+k,s[a]=b);return t=b}b=b||{};var k=b.matextension||"",q=[],l=[],m=[],n=[],p=[],r=[],v=[],s={},u=null,t=d(),w=new Map,y=0,z=1;b.scale&&(z=b.scale);for(var C={v:1,vt:2,vn:3,f:4,g:5,o:6,usemtl:7,mtllib:8},B,F,K,P=a.split("\n"),R=P.length,I=0;I<R;++I){var A=P[I],A=A.replace(/[ \t]+/g," ").replace(/\s\s*$/,"");if("\\"==A[A.length-1])var I=I+1,D=P[I].replace(/[ \t]+/g," ").replace(/\s\s*$/,""),A=(A.substr(0,A.length-1)+D).replace(/[ \t]+/g," ").replace(/\s\s*$/,
"");if("#"!=A[0]&&""!=A)switch(D=A.split(" "),A=C[D[0]],3>=A&&(B=parseFloat(D[1]),F=parseFloat(D[2]),2!=A&&(K=parseFloat(D[3]))),A){case 1:B*=z;F*=z;K*=z;q.push(B,F,K);break;case 2:m.push(B,F);break;case 3:l.push(B,F,K);break;case 4:if(4>D.length)continue;for(var E=[],A=1;A<D.length;++A)E.push(c(D[A]));t.indices.push(E[0],E[1],E[2]);for(A=2;A<E.length-1;++A)t.indices.push(E[0],E[A],E[A+1]);break;case 5:case 6:u=A=D[1];t.name?(s={},t=d(A)):t.name=A;break;case 7:f(D[1])}}C=[];B=0;z=[];for(A=0;A<v.length;++A)t=
v[A],t.indices&&(t.start=B,t.length=t.indices.length,C=C.concat(t.indices),delete t.indices,B+=t.length,z.push(t));v=z;z={};if(!q.length)return console.error("mesh without vertices"),null;if(b.flip_normals&&p.length)for(l=p,A=0;A<l.length;++A)l[A]*=-1;z.vertices=new Float32Array(n);p.length&&(z.normals=new Float32Array(p));r.length&&(z.coords=new Float32Array(r));C&&0<C.length&&(z.triangles=new (65536<B?Uint32Array:Uint16Array)(C));z.bounding=g.Mesh.computeBoundingBox(z.vertices);C={};1<v.length&&
(C.groups=v);z.info=C;if(!z.bounding)return console.log("empty mesh"),null;if(b.only_data)return z;C=null;return C=Mesh.load(z,null,b.mesh)};Mesh.parsers.obj=Mesh.parseOBJ;Mesh.encoders.obj=function(a,b){var c=a.getBuffer("vertices");if(!c)return null;var d=[];d.push("# Generated with liteGL.js by Javi Agenjo\n");for(var f=c.data,g=0;g<f.length;g+=3)d.push("v "+f[g].toFixed(4)+" "+f[g+1].toFixed(4)+" "+f[g+2].toFixed(4));if(c=a.getBuffer("normals"))for(d.push(""),c=c.data,g=0;g<c.length;g+=3)d.push("vn "+
c[g].toFixed(4)+" "+c[g+1].toFixed(4)+" "+c[g+2].toFixed(4));if(c=a.getBuffer("coords"))for(d.push(""),c=c.data,g=0;g<c.length;g+=2)d.push("vt "+c[g].toFixed(4)+" "+c[g+1].toFixed(4)+"  0.0000");c=a.info.groups;if(g=a.getIndexBuffer("triangles")){f=g.data;c&&c.length||(c=[{start:0,length:f.length,name:"mesh"}]);for(var q=0;q<c.length;++q){g=c[q];d.push("g "+g.name);var l=g.material||"mat_"+q;-1!=l.indexOf(".json")&&(l=l.substr(0,l.indexOf(".json")));d.push("usemtl "+l);for(var l=g.start,m=l+g.length,
g=l;g<m;g+=3)d.push("f "+(f[g]+1)+"/"+(f[g]+1)+"/"+(f[g]+1)+" "+(f[g+1]+1)+"/"+(f[g+1]+1)+"/"+(f[g+1]+1)+" "+(f[g+2]+1)+"/"+(f[g+2]+1)+"/"+(f[g+2]+1))}}else for(c&&c.length||(c=[{start:0,length:f.length/3,name:"mesh"}]),q=0;q<c.length;++q)for(g=c[q],d.push("g "+g.name),d.push("usemtl "+(g.material||"mat_"+q)),l=g.start,m=l+g.length,g=l;g<m;g+=3)d.push("f "+(g+1)+"/"+(g+1)+"/"+(g+1)+" "+(g+2)+"/"+(g+2)+"/"+(g+2)+" "+(g+3)+"/"+(g+3)+"/"+(g+3));return d.join("\n")};Mesh.parsers.mesh=function(a,b){for(var c=
{},d=a.split("\n"),f=0;f<d.length;++f){var g=d[f],q=g[0],g=g.substr(1).split(","),l=g[0];if("-"==q){for(var m=new Float32Array(Number(g[1])),q=0;q<m.length;++q)m[q]=Number(g[q+2]);c[l]=m}else if("*"==q){m=65536<Number(g[1])?new Uint32Array(Number(g[1])):new Uint16Array(Number(g[1]));for(q=0;q<m.length;++q)m[q]=Number(g[q+2]);c[l]=m}else if("@"==q)if("bones"==l){l=[];m=Number(g[1]);for(q=0;q<m;++q){var n=g.slice(3+17*q,3+17*(q+1)-1).map(Number);l.push([g[2+17*q],n])}c.bones=l}else if("bind_matrix"==
l)c.bind_matrix=g.slice(1,17).map(Number);else{if("groups"==l)for(c.info={groups:[]},l=Number(g[1]),q=0;q<l;++q)c.info.groups.push({name:g[2+4*q],material:g[4*q+3],start:Number(g[4*q+4]),length:Number(g[4*q+5])})}else console.warn("type unknown: "+g[0])}if(b.only_data)return c;d=null;d=Mesh.load(c,null,b.mesh);d.updateBoundingBox();return d};Mesh.encoders.mesh=function(a,b){var c=[],d;for(d in a.vertexBuffers){var f=a.vertexBuffers[d],f=["-"+d,f.data.length,f.data,typedArrayToArray(f.data)];c.push(f.join(","))}for(d in a.indexBuffers)f=
a.indexBuffers[d],f=["*"+d,f.data.length,f.data,typedArrayToArray(f.data)],c.push(f.join(","));a.bounding&&c.push(["@bounding",typedArrayToArray(a.bounding.subarray(0,6))].join());if(a.info&&a.info.groups){d=[];for(f=0;f<a.info.groups.length;++f){var g=a.info.groups[f];d.push(g.name,g.material,g.start,g.length)}c.push(["@groups",a.info.groups.length].concat(d).join(","))}a.bones&&c.push(["@bones",a.bones.length,a.bones.flat()].join());a.bind_matrix&&c.push(["@bind_matrix",typedArrayToArray(a.bind_matrix)].join());
return c.join("\n")};t.WBin&&(t.WBin.classes.Mesh=Mesh);Mesh.binary_file_formats.wbin=!0;Mesh.parsers.wbin=Mesh.fromBinary=function(a,b){b=b||{};var c=null;if(a.constructor==ArrayBuffer){if(!t.WBin)throw"To use binary meshes you need to install WBin.js from https://github.com/jagenjo/litescene.js/blob/master/src/utils/wbin.js ";c=WBin.load(a,!0)}else c=a;c.info||console.warn("This WBin doesn't seem to contain a mesh. Classname: ",c["@classname"]);c.format&&g.Mesh.decompress(c);var d={};if(c.vertex_buffers)for(var f in c.vertex_buffers)d[c.vertex_buffers[f]]=
c[c.vertex_buffers[f]];else c.vertices&&(d.vertices=c.vertices),c.normals&&(d.normals=c.normals),c.coords&&(d.coords=c.coords),c.weights&&(d.weights=c.weights),c.bone_indices&&(d.bone_indices=c.bone_indices);var k={};if(c.index_buffers)for(f in c.index_buffers)k[c.index_buffers[f]]=c[c.index_buffers[f]];else c.triangles&&(k.triangles=c.triangles),c.wireframe&&(k.wireframe=c.wireframe);d={vertex_buffers:d,index_buffers:k,bounding:c.bounding,info:c.info};if(c.bones){d.bones=c.bones;for(f=0;f<d.bones.length;++f)d.bones[f][1]=
mat4.clone(d.bones[f][1]);c.bind_matrix&&(d.bind_matrix=mat4.clone(c.bind_matrix))}c.morph_targets&&(d.morph_targets=c.morph_targets);if(b.only_data)return d;c=b.mesh||new g.Mesh;c.configure(d);return c};Mesh.encoders.wbin=function(a,b){return a.toBinary(b)};Mesh.prototype.toBinary=function(a){if(!t.WBin)throw"to use Mesh.toBinary you need to have WBin included. Check the repository for wbin.js";this.info||(this.info={});a={object_class:"Mesh",info:this.info,groups:this.groups};if(this.bones){for(var b=
[],c=0;c<this.bones.length;++c)b.push([this.bones[c][0],mat4.toArray(this.bones[c][1])]);a.bones=b;this.bind_matrix&&(a.bind_matrix=this.bind_matrix)}this.bounding||this.updateBoundingBox();a.bounding=this.bounding;var b=[],d=[];for(c in this.vertexBuffers){var f=this.vertexBuffers[c];a[f.name]=f.data;b.push(f.name);"vertices"==f.name&&(a.info.num_vertices=f.data.length/3)}for(c in this.indexBuffers)f=this.indexBuffers[c],a[c]=f.data,d.push(c);a.vertex_buffers=b;a.index_buffers=d;g.Mesh.enable_wbin_compression&&
g.Mesh.compress(a);return WBin.create(a,"Mesh")};Mesh.compress=function(a,b){b=b||"bounding_compressed";a.format={type:b};var c=Mesh.compressors[b];if(!c)throw"compression format not supported:"+b;return c(a)};Mesh.decompress=function(a){if(a.format){var b=Mesh.decompressors[a.format.type];if(!b)throw"decompression format not supported:"+a.format.type;return b(a)}};Mesh.compressors.bounding_compressed=function(a){if(!a.vertex_buffers)throw"buffers not found";for(var b=BBox.getMin(a.bounding),c=BBox.getMax(a.bounding),
d=vec3.sub(vec3.create(),c,b),f=a.vertices,g=new Uint16Array(f.length),c=0;c<f.length;c+=3)g[c]=(f[c]-b[0])/d[0]*65535,g[c+1]=(f[c+1]-b[1])/d[1]*65535,g[c+2]=(f[c+2]-b[2])/d[2]*65535;a.vertices=g;if(a.normals){d=a.normals;b=new Uint8Array(d.length);f=b.constructor==Uint8Array?255:65535;for(c=0;c<d.length;c+=3)b[c]=(0.5*d[c]+0.5)*f,b[c+1]=(0.5*d[c+1]+0.5)*f,b[c+2]=(0.5*d[c+2]+0.5)*f;a.normals=b}if(a.coords){b=a.coords;f=[1E4,1E4,-1E4,-1E4];for(c=0;c<b.length;c+=2)d=b[c],f[0]>d?f[0]=d:f[2]<d&&(f[2]=
d),d=b[c+1],f[1]>d?f[1]=d:f[3]<d&&(f[3]=d);a.format.uvs_bounding=f;g=new Uint16Array(b.length);d=[f[2]-f[0],f[3]-f[1]];for(c=0;c<b.length;c+=2)g[c]=(b[c]-f[0])/d[0]*65535,g[c+1]=(b[c+1]-f[1])/d[1]*65535;a.coords=g}if(a.weights){d=a.weights;b=new Uint16Array(d.length);f=b.constructor==Uint8Array?255:65535;for(c=0;c<d.length;c+=4)b[c]=d[c]*f,b[c+1]=d[c+1]*f,b[c+2]=d[c+2]*f,b[c+3]=d[c+3]*f;a.weights=b}};Mesh.decompressors.bounding_compressed=function(a){var b=a.bounding;if(!b)throw"error in mesh decompressing data: bounding not found, cannot use the bounding decompression.";
for(var c=BBox.getMin(b),b=BBox.getMax(b),d=vec3.sub(vec3.create(),b,c),f=a.format,g=1/255,q=1/65535,l=a.vertices,m=new Float32Array(l.length),b=0,n=l.length;b<n;b+=3)m[b]=l[b]*q*d[0]+c[0],m[b+1]=l[b+1]*q*d[1]+c[1],m[b+2]=l[b+2]*q*d[2]+c[2];a.vertices=m;if(a.normals&&a.normals.constructor!=Float32Array){d=a.normals;c=new Float32Array(d.length);l=d.constructor==Uint8Array?g:q;b=0;for(n=d.length;b<n;b+=3)c[b]=d[b]*l*2-1,c[b+1]=d[b+1]*l*2-1,c[b+2]=d[b+2]*l*2-1,m=c.subarray(b,b+3),vec3.normalize(m,m);
a.normals=c}if(a.coords&&f.uvs_bounding&&a.coords.constructor!=Float32Array){c=a.coords;f=f.uvs_bounding;d=[f[2]-f[0],f[3]-f[1]];l=new Float32Array(c.length);b=0;for(n=c.length;b<n;b+=2)l[b]=c[b]*q*d[0]+f[0],l[b+1]=c[b+1]*q*d[1]+f[1];a.coords=l}if(a.weights&&a.weights.constructor!=Float32Array){f=a.weights;d=new Float32Array(f.length);g=f.constructor==Uint8Array?g:q;b=0;for(n=f.length;b<n;b+=4)d[b]=f[b]*g,d[b+1]=f[b+1]*g,d[b+2]=f[b+2]*g,d[b+3]=f[b+3]*g;a.weights=d}}})("undefined"!=typeof window?window:
"undefined"!=typeof self?self:global);
