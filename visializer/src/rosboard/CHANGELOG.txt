1.3.0
-----
* Fixed issues with "'Unknown QoS history policy, at ./src/qos.cpp:61'"

1.2.1
-----
 * Fix for bug "Viewer not found" when rosboard is installed to system
   (setup.py did not catch all the client-side js files).

1.2.0
-----
* Experimental support for PointCloud2 with WebGL, and simple compression of point cloud data
* Binary compression of LaserScan range and intensity data
* Support for Pose, PoseStamped, PoseWithCovariance, PoseWithCovarianceStamped
* Support for Point, Point32, Odometry
* Support for DiagnosticArray with aggregation
* Fixed some connection drop rosout logspam issues
* Panning of LaserScan and other 2D types (use 2 fingers to pan/zoom on mobile to avoid conflict with scrolling)
* Remember last viewed topics and bring them back up when page is reloaded
* Allow switching between multiple supported viewers (e.g. 2D spatial view or raw data)
* Pause button

1.1.3
-----
* Display hostname of robot in top bar
* Support for sensor_msgs/LaserScan, geometry_msgs/Polygon
* Support for viewing process list ("top")
* Detect lagging connections and disconnect them
* Base64-encode binary messages
* Display error message when custom ROS msg file is not found, instead of hanging without feedback

1.1.2
-----
* Resubscribe bug fix
* Sort tree paths alphabetically
* Fix dismissed cards re-appearing bug
* Support for 16-bit greyscale images
* Corrected display colors for bgr8 images
* OccupancyGrid support
* Fix exceptions being thrown when ROS message contain NaN/Inf/-Inf

1.1.1
-----
* Tree view of topics
* Cards appear immediately with spinner instead of waiting for next ROS message before appearing
* Bug fix in ROS node subscriber
