<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>nav2_common</name>
  <version>1.3.1</version>
  <description>Common support functionality used throughout the navigation 2 stack</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache-2.0</license>

  <depend>launch</depend>
  <depend>launch_ros</depend>
  <depend>osrf_pycommon</depend>
  <depend>rclpy</depend>
  <depend>python3-yaml</depend>
  <depend>python3-types-pyyaml</depend>

  <buildtool_depend>ament_cmake_core</buildtool_depend>

  <build_depend>ament_cmake_python</build_depend>

  <buildtool_export_depend>ament_cmake_core</buildtool_export_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
