# Indoor Navigation
As a Robot user I want my robot to autonomously navigate to a given location on a given map so that it can help me at that location

## More details
- Why is this needed?
   - This is needed for indoor robot navigation in most (all?) cases
   - Example: a courier robot in a logistics warehouse

- What is the expected user interaction?
   - The user should be able to specify a map to use and a location on that map for the robot to move to.

- Are there any non-functional requirements? (build system, tools, performance, etc)
