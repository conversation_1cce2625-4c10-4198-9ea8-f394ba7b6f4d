# Use Case Title
As a \<Developer, Researcher, Technician, etc.> I want the robot to \<action> so that \<I, the robot, etc.> can \<do something important>

## More details
- Why is this needed?
- What is the expected user interaction?
- Are there any non-functional requirements? (build system, tools, performance, etc)

# Example:

# Collision Avoidance
As a robot user, I want the robot to navigate without colliding into people or objects so that it doesn't hurt anyone or damage anything

## More details
 - Why is this needed?
   - I want this so that I know the robot won't damage itself, damage property or hurt anyone
   - Example: a logistics robot in a warehouse must avoid shelves, people, forklifts, and other robots
 - What is the expected user interaction?
   - I shouldn't have to interact with the robot to prevent it from crashing into people or things
- Are there any non-functional requirements? (build system, tools, performance, etc)
   - The performance needs to be fast enough to avoid moving objects such as people walking or other moving robots
