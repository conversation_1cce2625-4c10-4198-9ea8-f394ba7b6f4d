# Multi-story Building Navigation (2D+)
As a Robot user I want my robot to be able to navigate stairways, ramps or elevators to move to another portion of the multi-story building so that it can do something useful

## More details
- Why is this needed?
   - Example: a delivery robot in an office building

- What is the expected user interaction?
   - The user should be able to specify stairways, ramps and elevators on a map for a robot to use or not use
     - via a GUI
     - via a config file or API so that it can be done by another program

- Are there any non-functional requirements? (build system, tools, performance, etc)
