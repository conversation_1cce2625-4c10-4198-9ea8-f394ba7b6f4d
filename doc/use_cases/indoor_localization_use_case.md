# Indoor Localization
As a Robot user I want my robot to know its location on a given map of an indoor area so that it can move around the area

## More details
- Why is this needed?
   - This is needed for indoor robot navigation in most (all?) cases
   - Example: a courier robot in a logistics warehouse

- What is the expected user interaction?
   - The user should be able to specify a map to use and a location on that map for the robot
   - The robot should be able to deduce it's own position on a map autonomously

- Are there any non-functional requirements? (build system, tools, performance, etc)
