# Requirement Title
The \<navigation system> should be able to \<shall> \<do something>

## More details
- Why is this needed?
- What is the expected user interaction?
- What use case does this map to?
- Are there any non-functional requirements (build system, tools, performance, etc)


# Example:

# Warehouse Navigation
The navigation system should include a modular collision avoidance algorithm that can be replaced with a new algorithm at run time

## More details
 - I want to be able to write or use my own collision avoidance algorithm without having to re-compile the entire stack from source
 - Ideally I can just change out a node using a custom launch file
 - This maps to the "Collision Avoidance" use case
