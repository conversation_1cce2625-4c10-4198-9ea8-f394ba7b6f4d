# Copyright (c) 2018 Intel Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# 文件说明: 这是Nav2的主要启动文件，用于启动完整的Nav2导航堆栈。
# 它会根据配置启动SLAM或定位模块，以及导航相关的所有节点。

import os

# 导入必要的ROS 2启动相关模块
from ament_index_python.packages import get_package_share_directory  # 用于获取包的安装路径
from launch import LaunchDescription  # 启动描述类，定义了启动过程
from launch.actions import (DeclareLaunchArgument, GroupAction, IncludeLaunchDescription,
                            SetEnvironmentVariable)  # 启动相关的动作
from launch.conditions import IfCondition  # 条件判断，用于有条件地启动节点
from launch.launch_description_sources import PythonLaunchDescriptionSource  # 用于包含其他启动文件
from launch.substitutions import LaunchConfiguration, PythonExpression  # 用于参数替换和表达式计算
from launch_ros.actions import Node, PushROSNamespace  # ROS特定的启动动作
from launch_ros.descriptions import ParameterFile  # 参数文件描述
from nav2_common.launch import RewrittenYaml  # Nav2特定的YAML文件处理工具


def generate_launch_description() -> LaunchDescription:
    # 获取启动目录
    # nav2_bringup包包含了启动Nav2所需的所有文件
    bringup_dir = get_package_share_directory('nav2_bringup')
    launch_dir = os.path.join(bringup_dir, 'launch')  # 启动文件目录

    # 创建启动配置变量
    # 这些变量可以在命令行中覆盖，例如: ros2 launch nav2_bringup bringup_launch.py slam:=True
    namespace = LaunchConfiguration('namespace')  # ROS命名空间
    slam = LaunchConfiguration('slam')  # 是否使用SLAM（同时定位与地图构建）
    map_yaml_file = LaunchConfiguration('map')  # 地图文件路径
    use_sim_time = LaunchConfiguration('use_sim_time')  # 是否使用仿真时间
    params_file = LaunchConfiguration('params_file')  # 参数文件路径
    autostart = LaunchConfiguration('autostart')  # 是否自动启动导航堆栈
    use_composition = LaunchConfiguration('use_composition')  # 是否使用组件化启动
    use_respawn = LaunchConfiguration('use_respawn')  # 节点崩溃时是否自动重启
    log_level = LaunchConfiguration('log_level')  # 日志级别
    use_localization = LaunchConfiguration('use_localization')  # 是否使用定位模块

    # 将完全限定的名称映射为相对名称，以便可以在前面加上节点的命名空间
    # 对于变换(tf)，目前似乎没有更好的替代方案
    # 相关问题请参考：
    # https://github.com/ros/geometry2/issues/32
    # https://github.com/ros/robot_state_publisher/pull/30
    # TODO(orduno) 使用 `PushNodeRemapping` 替代
    #              https://github.com/ros2/launch_ros/issues/56
    # 这里将 /tf 和 /tf_static 话题重新映射为 tf 和 tf_static
    remappings = [('/tf', 'tf'), ('/tf_static', 'tf_static')]

    # 配置参数文件
    # RewrittenYaml用于处理YAML参数文件，允许在命名空间下重写参数
    configured_params = ParameterFile(
        RewrittenYaml(
            source_file=params_file,  # 源参数文件
            root_key=namespace,  # 命名空间作为根键
            param_rewrites={},  # 参数重写规则，这里为空
            convert_types=True,  # 转换YAML中的类型为ROS参数类型
        ),
        allow_substs=True,  # 允许参数替换
    )

    # 设置环境变量，使日志输出缓冲
    # 这样可以避免日志输出被分成多行
    stdout_linebuf_envvar = SetEnvironmentVariable(
        'RCUTILS_LOGGING_BUFFERED_STREAM', '1'  # 启用日志缓冲
    )

    # 声明命名空间参数，用于设置所有节点的命名空间
    declare_namespace_cmd = DeclareLaunchArgument(
        'namespace', default_value='', description='顶级命名空间'
    )

    # 声明SLAM参数，决定是否使用SLAM进行同时定位与地图构建
    declare_slam_cmd = DeclareLaunchArgument(
        'slam', default_value='False', description='是否运行SLAM'
    )

    # 声明地图文件参数，指定要加载的地图文件路径
    declare_map_yaml_cmd = DeclareLaunchArgument(
        'map', default_value='', description='要加载的地图文件的完整路径'
    )

    # 声明是否使用定位模块的参数
    declare_use_localization_cmd = DeclareLaunchArgument(
        'use_localization', default_value='True',
        description='是否启用定位模块'
    )

    # 声明是否使用仿真时间的参数，在仿真环境中应设置为true
    declare_use_sim_time_cmd = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='如果为true，使用仿真（Gazebo）时钟',
    )

    # 声明参数文件路径，指定包含所有节点参数的YAML文件
    declare_params_file_cmd = DeclareLaunchArgument(
        'params_file',
        default_value=os.path.join(bringup_dir, 'params', 'nav2_params.yaml'),
        description='用于所有启动节点的ROS2参数文件的完整路径',
    )

    # 声明是否自动启动导航堆栈的参数
    declare_autostart_cmd = DeclareLaunchArgument(
        'autostart',
        default_value='true',
        description='自动启动Nav2堆栈',
    )

    # 声明是否使用组件化启动的参数，组件化可以提高效率
    declare_use_composition_cmd = DeclareLaunchArgument(
        'use_composition',
        default_value='True',
        description='是否使用组件化启动',
    )

    # 声明是否在节点崩溃时自动重启的参数
    declare_use_respawn_cmd = DeclareLaunchArgument(
        'use_respawn',
        default_value='False',
        description='如果节点崩溃，是否重新启动。在组件化禁用时应用。',
    )

    # 声明日志级别参数
    declare_log_level_cmd = DeclareLaunchArgument(
        'log_level', default_value='info', description='日志级别'
    )

    # 指定启动动作
    # 这里定义了一组动作，包括命名空间、组件容器和各种启动文件
    bringup_cmd_group = GroupAction(
        [
            # 将所有节点放入指定的命名空间
            PushROSNamespace(namespace),

            # 创建组件容器节点，只在使用组件化启动时创建
            Node(
                condition=IfCondition(use_composition),  # 条件：只在use_composition为True时创建
                name='nav2_container',  # 节点名称
                package='rclcpp_components',  # 包名
                executable='component_container_isolated',  # 可执行文件
                parameters=[configured_params, {'autostart': autostart}],  # 参数
                arguments=['--ros-args', '--log-level', log_level],  # 命令行参数
                remappings=remappings,  # 话题重映射
                output='screen',  # 输出到屏幕
            ),
            # 包含 SLAM 启动文件，只在 slam=True 且 use_localization=True 时启动
            # SLAM（同时定位与地图构建）用于在未知环境中创建地图
            IncludeLaunchDescription(
                PythonLaunchDescriptionSource(
                    os.path.join(launch_dir, 'slam_launch.py')  # SLAM启动文件路径
                ),
                condition=IfCondition(PythonExpression([slam, ' and ', use_localization])),  # 条件判断
                launch_arguments={
                    'namespace': namespace,  # 命名空间
                    'use_sim_time': use_sim_time,  # 是否使用仿真时间
                    'autostart': autostart,  # 是否自动启动
                    'use_respawn': use_respawn,  # 是否在崩溃时重启
                    'params_file': params_file,  # 参数文件
                }.items(),
            ),
            # 包含定位启动文件，只在 slam=False 且 use_localization=True 时启动
            # 定位模块用于在已知地图中确定机器人的位置
            IncludeLaunchDescription(
                PythonLaunchDescriptionSource(
                    os.path.join(launch_dir, 'localization_launch.py')  # 定位启动文件路径
                ),
                condition=IfCondition(PythonExpression(['not ', slam, ' and ', use_localization])),  # 条件判断
                launch_arguments={
                    'namespace': namespace,  # 命名空间
                    'map': map_yaml_file,  # 地图文件
                    'use_sim_time': use_sim_time,  # 是否使用仿真时间
                    'autostart': autostart,  # 是否自动启动
                    'params_file': params_file,  # 参数文件
                    'use_composition': use_composition,  # 是否使用组件化
                    'use_respawn': use_respawn,  # 是否在崩溃时重启
                    'container_name': 'nav2_container',  # 容器名称
                }.items(),
            ),
            # 包含导航启动文件，启动所有导航相关的节点
            # 导航模块包括路径规划、控制器、行为树、恢复行为等
            IncludeLaunchDescription(
                PythonLaunchDescriptionSource(
                    os.path.join(launch_dir, 'navigation_launch.py')  # 导航启动文件路径
                ),
                launch_arguments={
                    'namespace': namespace,  # 命名空间
                    'use_sim_time': use_sim_time,  # 是否使用仿真时间
                    'autostart': autostart,  # 是否自动启动
                    'params_file': params_file,  # 参数文件
                    'use_composition': use_composition,  # 是否使用组件化
                    'use_respawn': use_respawn,  # 是否在崩溃时重启
                    'container_name': 'nav2_container',  # 容器名称
                }.items(),
            ),
        ]
    )

    # 创建启动描述并填充
    # LaunchDescription是ROS 2启动系统的核心类，定义了整个启动过程
    ld = LaunchDescription()

    # 设置环境变量
    ld.add_action(stdout_linebuf_envvar)  # 添加日志缓冲环境变量

    # 声明启动选项
    # 添加所有的参数声明，这样用户可以在命令行中覆盖这些参数
    ld.add_action(declare_namespace_cmd)  # 命名空间
    ld.add_action(declare_slam_cmd)  # 是否使用SLAM
    ld.add_action(declare_map_yaml_cmd)  # 地图文件
    ld.add_action(declare_use_sim_time_cmd)  # 是否使用仿真时间
    ld.add_action(declare_params_file_cmd)  # 参数文件
    ld.add_action(declare_autostart_cmd)  # 是否自动启动
    ld.add_action(declare_use_composition_cmd)  # 是否使用组件化
    ld.add_action(declare_use_respawn_cmd)  # 是否在崩溃时重启
    ld.add_action(declare_log_level_cmd)  # 日志级别
    ld.add_action(declare_use_localization_cmd)  # 是否使用定位

    # 添加启动所有导航节点的操作
    # 这一步将所有前面定义的动作组添加到启动描述中
    ld.add_action(bringup_cmd_group)

    # 返回启动描述对象
    return ld
