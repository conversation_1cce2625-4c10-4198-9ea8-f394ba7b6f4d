# Copyright (C) 2023 Open Source Robotics Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""This is all-in-one launch script intended for use by nav2 developers.
这是一个面向Nav2开发者的一体化启动脚本，用于启动完整的Nav2仿真环境。"""

# 文件说明: 这个文件用于启动TurtleBot3机器人的仿真环境，包括以下组件：
# 1. Gazebo仿真器
# 2. TurtleBot3机器人模型
# 3. RViz可视化工具
# 4. Nav2导航堆栈

import os
import tempfile  # 用于创建临时文件

# 导入必要的ROS 2启动相关模块
from ament_index_python.packages import get_package_share_directory  # 用于获取包的安装路径
from launch import LaunchDescription  # 启动描述类，定义了启动过程
from launch.actions import (DeclareLaunchArgument, ExecuteProcess, IncludeLaunchDescription,
                            OpaqueFunction, RegisterEventHandler)  # 启动相关的动作
from launch.conditions import IfCondition  # 条件判断，用于有条件地启动节点
from launch.event_handlers import OnShutdown  # 关闭事件处理器
from launch.launch_description_sources import PythonLaunchDescriptionSource  # 用于包含其他启动文件
from launch.substitutions import LaunchConfiguration, PythonExpression  # 用于参数替换和表达式计算
from launch_ros.actions import Node  # ROS特定的节点启动动作


def generate_launch_description() -> LaunchDescription:
    # 获取启动目录
    # 这些目录用于定位各种资源文件，如启动文件、地图、参数文件等
    bringup_dir = get_package_share_directory('nav2_bringup')  # Nav2启动包目录
    launch_dir = os.path.join(bringup_dir, 'launch')  # 启动文件目录
    sim_dir = get_package_share_directory('nav2_minimal_tb3_sim')  # TurtleBot3仿真包目录
    # 添加turtlebot3_gazebo包目录，用于获取house地图和世界模型
    turtlebot3_gazebo_dir = os.path.join('/opt/overlay_ws/src/navigation2', 'turtlebot3_gazebo')

    # 创建启动配置变量
    # 这些变量可以在命令行中覆盖，例如: ros2 launch nav2_bringup tb3_simulation_launch.py slam:=True
    slam = LaunchConfiguration('slam')  # 是否使用SLAM（同时定位与地图构建）
    namespace = LaunchConfiguration('namespace')  # ROS命名空间
    map_yaml_file = LaunchConfiguration('map')  # 地图文件路径
    use_sim_time = LaunchConfiguration('use_sim_time')  # 是否使用仿真时间
    params_file = LaunchConfiguration('params_file')  # 参数文件路径
    autostart = LaunchConfiguration('autostart')  # 是否自动启动导航堆栈
    use_composition = LaunchConfiguration('use_composition')  # 是否使用组件化启动
    use_respawn = LaunchConfiguration('use_respawn')  # 节点崩溃时是否自动重启

    # 仿真特定的启动配置变量
    # 这些变量专门用于控制仿真环境的行为
    rviz_config_file = LaunchConfiguration('rviz_config_file')  # RViz配置文件
    use_simulator = LaunchConfiguration('use_simulator')  # 是否使用仿真器
    use_robot_state_pub = LaunchConfiguration('use_robot_state_pub')  # 是否使用机器人状态发布器
    use_rviz = LaunchConfiguration('use_rviz')  # 是否使用RViz可视化
    headless = LaunchConfiguration('headless')  # 是否在无头模式下运行（无GUI）
    world = LaunchConfiguration('world')  # 世界模型文件
    pose = {  # 机器人的初始位姿
        'x': LaunchConfiguration('x_pose', default='1.17'),  # x坐标 (适合house地图的初始位置)
        'y': LaunchConfiguration('y_pose', default='-1.5'),  # y坐标
        'z': LaunchConfiguration('z_pose', default='0.01'),  # z坐标
        'R': LaunchConfiguration('roll', default='0.00'),  # 滚转角
        'P': LaunchConfiguration('pitch', default='0.00'),  # 俯仰角
        'Y': LaunchConfiguration('yaw', default='0.00'),  # 偏航角
    }
    robot_name = LaunchConfiguration('robot_name')  # 机器人名称
    robot_sdf = LaunchConfiguration('robot_sdf')  # 机器人 SDF 模型文件

    # 话题重映射，将全局话题映射为相对话题
    remappings = [('/tf', 'tf'), ('/tf_static', 'tf_static')]

    # 声明启动参数
    # 这些参数可以在命令行中被覆盖，例如: ros2 launch nav2_bringup tb3_simulation_launch.py slam:=True
    declare_namespace_cmd = DeclareLaunchArgument(
        'namespace', default_value='', description='顶级命名空间'
    )

    # 声明SLAM参数，决定是否使用SLAM进行同时定位与地图构建
    declare_slam_cmd = DeclareLaunchArgument(
        'slam', default_value='False', description='是否运行SLAM'
    )

    # 声明地图文件参数，指定要加载的地图文件路径
    # 默认使用house.yaml地图，这是一个房屋环境
    declare_map_yaml_cmd = DeclareLaunchArgument(
        'map',
        default_value=os.path.join(sim_dir, 'launch', 'house.yaml'),
        description='要加载的地图文件的完整路径'
    )

    # 声明是否使用仿真时间的参数，在仿真环境中应设置为true
    declare_use_sim_time_cmd = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='如果为true，使用仿真（Gazebo）时钟',
    )

    # 声明参数文件路径，指定包含所有节点参数的YAML文件
    declare_params_file_cmd = DeclareLaunchArgument(
        'params_file',
        default_value=os.path.join(bringup_dir, 'params', 'nav2_params.yaml'),
        description='用于所有启动节点的ROS2参数文件的完整路径',
    )

    # 声明是否自动启动导航堆栈的参数
    declare_autostart_cmd = DeclareLaunchArgument(
        'autostart',
        default_value='true',
        description='自动启动Nav2堆栈',
    )

    # 声明是否使用组件化启动的参数，组件化可以提高效率
    declare_use_composition_cmd = DeclareLaunchArgument(
        'use_composition',
        default_value='True',
        description='是否使用组件化启动',
    )

    # 声明是否在节点崩溃时自动重启的参数
    declare_use_respawn_cmd = DeclareLaunchArgument(
        'use_respawn',
        default_value='False',
        description='如果节点崩溃，是否重新启动。在组件化禁用时应用。',
    )

    # 声明RViz配置文件参数，指定要使用的RViz配置文件
    declare_rviz_config_file_cmd = DeclareLaunchArgument(
        'rviz_config_file',
        default_value=os.path.join(bringup_dir, 'rviz', 'nav2_default_view.rviz'),
        description='RViz配置文件的完整路径',
    )

    # 声明是否启动仿真器的参数
    declare_use_simulator_cmd = DeclareLaunchArgument(
        'use_simulator',
        default_value='True',
        description='是否启动仿真器',
    )

    # 声明是否启动机器人状态发布器的参数
    # 机器人状态发布器用于发布机器人的TF变换和关节状态
    declare_use_robot_state_pub_cmd = DeclareLaunchArgument(
        'use_robot_state_pub',
        default_value='True',
        description='是否启动机器人状态发布器',
    )

    # 声明是否启动RViz的参数
    declare_use_rviz_cmd = DeclareLaunchArgument(
        'use_rviz', default_value='True', description='是否启动RViz'
    )

    # 声明是否在无头模式下运行的参数（无GUI）
    declare_simulator_cmd = DeclareLaunchArgument(
        'headless', default_value='True', description='是否在无头模式下运行（不启动Gazebo客户端）'
    )

    # 声明世界模型文件参数，指定要加载的仿真世界模型
    declare_world_cmd = DeclareLaunchArgument(
        'world',
        default_value=os.path.join(sim_dir, 'worlds', 'turtlebot3_house.sdf.xacro'),
        description='要加载的世界模型文件的完整路径',
    )

    # 声明机器人名称参数
    declare_robot_name_cmd = DeclareLaunchArgument(
        'robot_name', default_value='turtlebot3_waffle', description='机器人的名称'
    )

    # 声明机器人 SDF 模型文件参数
    declare_robot_sdf_cmd = DeclareLaunchArgument(
        'robot_sdf',
        default_value=os.path.join(sim_dir, 'urdf', 'gz_waffle.sdf.xacro'),
        description='用于在Gazebo中生成机器人的SDF文件的完整路径',
    )

    # 加载TurtleBot3 Waffle机器人的URDF模型文件
    # URDF（统一机器人描述格式）文件定义了机器人的几何形状、关节和物理属性
    urdf = os.path.join(sim_dir, 'urdf', 'turtlebot3_waffle.urdf')  # URDF文件路径
    with open(urdf, 'r') as infp:  # 打开并读取URDF文件
        robot_description = infp.read()  # 读取URDF内容

    # 创建机器人状态发布器节点
    # 该节点负责将URDF中的机器人模型转换为TF变换和关节状态消息
    start_robot_state_publisher_cmd = Node(
        condition=IfCondition(use_robot_state_pub),  # 条件：只在use_robot_state_pub为True时创建
        package='robot_state_publisher',  # 包名
        executable='robot_state_publisher',  # 可执行文件
        name='robot_state_publisher',  # 节点名称
        namespace=namespace,  # 命名空间
        output='screen',  # 输出到屏幕
        parameters=[  # 参数
            {'use_sim_time': use_sim_time, 'robot_description': robot_description}  # 使用仿真时间和机器人描述
        ],
        remappings=remappings,  # 话题重映射
    )

    # 包含RViz启动文件，用于启动RViz可视化工具
    # RViz是ROS的可视化工具，用于显示机器人、传感器数据和导航信息
    rviz_cmd = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(launch_dir, 'rviz_launch.py')),  # RViz启动文件
        condition=IfCondition(use_rviz),  # 条件：只在use_rviz为True时启动
        launch_arguments={  # 启动参数
            'namespace': namespace,  # 命名空间
            'use_sim_time': use_sim_time,  # 是否使用仿真时间
            'rviz_config': rviz_config_file,  # RViz配置文件
        }.items(),
    )

    # 包含Nav2导航堆栈启动文件
    # 这个启动文件会启动所有导航相关的节点，包括定位、规划和控制
    bringup_cmd = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(launch_dir, 'bringup_launch.py')),  # Nav2启动文件
        launch_arguments={  # 启动参数
            'namespace': namespace,  # 命名空间
            'slam': slam,  # 是否使用SLAM
            'map': map_yaml_file,  # 地图文件
            'use_sim_time': use_sim_time,  # 是否使用仿真时间
            'params_file': params_file,  # 参数文件
            'autostart': autostart,  # 是否自动启动
            'use_composition': use_composition,  # 是否使用组件化
            'use_respawn': use_respawn,  # 是否在崩溃时重启
        }.items(),
    )
    # 世界模型文件是xacro文件，因为我们想根据是否在无头模式下运行来有条件地加载SceneBroadcaster插件
    # 但是目前，Gazebo命令行不接受世界的SDF字符串，所以xacro的输出需要保存到临时文件并传递给Gazebo
    # 创建临时SDF文件
    world_sdf = tempfile.mktemp(prefix='nav2_', suffix='.sdf')  # 创建临时文件

    # 使用xacro处理世界模型文件
    world_sdf_xacro = ExecuteProcess(
        cmd=['xacro', '-o', world_sdf, ['headless:=', headless], world]  # 调用xacro处理文件
    )

    # 启动Gazebo服务器
    gazebo_server = ExecuteProcess(
        cmd=['gz', 'sim', '-r', '-s', world_sdf],  # 启动Gazebo仿真器
        output='screen',  # 输出到屏幕
        condition=IfCondition(use_simulator),  # 条件：只在use_simulator为True时启动
        additional_env={'GAZEBO_MODEL_PATH': sim_dir + '/models:' + turtlebot3_gazebo_dir + '/models:' + os.environ.get('GAZEBO_MODEL_PATH', '')}  # 添加模型路径
    )

    # 注册关闭事件处理器，在关闭时删除临时SDF文件
    remove_temp_sdf_file = RegisterEventHandler(event_handler=OnShutdown(
        on_shutdown=[
            OpaqueFunction(function=lambda _: os.remove(world_sdf))  # 删除临时文件
        ]))

    # 启动Gazebo客户端（只在非无头模式下启动）
    gazebo_client = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(get_package_share_directory('ros_gz_sim'),
                         'launch',
                         'gz_sim.launch.py')  # Gazebo客户端启动文件
        ),
        condition=IfCondition(PythonExpression(
            [use_simulator, ' and not ', headless])),  # 条件：只在use_simulator为True且headless为False时启动
        launch_arguments={
            'gz_args': ['-v4 -g '],
            'additional_env': {'GAZEBO_MODEL_PATH': sim_dir + '/models:' + turtlebot3_gazebo_dir + '/models:' + os.environ.get('GAZEBO_MODEL_PATH', '')}
        }.items(),  # Gazebo参数
    )

    # 在Gazebo中生成TurtleBot3机器人
    gz_robot = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(sim_dir, 'launch', 'spawn_tb3.launch.py')),  # 生成机器人的启动文件
        launch_arguments={'namespace': namespace,  # 命名空间
                          'use_sim_time': use_sim_time,  # 是否使用仿真时间
                          'robot_name': robot_name,  # 机器人名称
                          'robot_sdf': robot_sdf,  # 机器人 SDF 模型文件
                          'x_pose': pose['x'],  # x坐标
                          'y_pose': pose['y'],  # y坐标
                          'z_pose': pose['z'],  # z坐标
                          'roll': pose['R'],  # 滚转角
                          'pitch': pose['P'],  # 俯仰角
                          'yaw': pose['Y'],  # 偏航角
                          'additional_env': {'GAZEBO_MODEL_PATH': sim_dir + '/models:' + turtlebot3_gazebo_dir + '/models:' + os.environ.get('GAZEBO_MODEL_PATH', '')}}.items())

    # 创建启动描述并填充
    # LaunchDescription是ROS 2启动系统的核心类，定义了整个启动过程
    ld = LaunchDescription()

    # 声明启动选项
    # 添加所有的参数声明，这样用户可以在命令行中覆盖这些参数
    # Nav2相关参数
    ld.add_action(declare_namespace_cmd)  # 命名空间
    ld.add_action(declare_slam_cmd)  # 是否使用SLAM
    ld.add_action(declare_map_yaml_cmd)  # 地图文件
    ld.add_action(declare_use_sim_time_cmd)  # 是否使用仿真时间
    ld.add_action(declare_params_file_cmd)  # 参数文件
    ld.add_action(declare_autostart_cmd)  # 是否自动启动
    ld.add_action(declare_use_composition_cmd)  # 是否使用组件化

    # 仿真相关参数
    ld.add_action(declare_rviz_config_file_cmd)  # RViz配置文件
    ld.add_action(declare_use_simulator_cmd)  # 是否使用仿真器
    ld.add_action(declare_use_robot_state_pub_cmd)  # 是否使用机器人状态发布器
    ld.add_action(declare_use_rviz_cmd)  # 是否使用RViz
    ld.add_action(declare_simulator_cmd)  # 是否在无头模式下运行
    ld.add_action(declare_world_cmd)  # 世界模型文件
    ld.add_action(declare_robot_name_cmd)  # 机器人名称
    ld.add_action(declare_robot_sdf_cmd)  # 机器人 SDF 模型文件
    ld.add_action(declare_use_respawn_cmd)  # 是否在崩溃时重启

    # 添加Gazebo仿真器相关的动作
    ld.add_action(world_sdf_xacro)  # 处理世界模型文件
    ld.add_action(remove_temp_sdf_file)  # 删除临时SDF文件
    ld.add_action(gz_robot)  # 生成机器人
    ld.add_action(gazebo_server)  # 启动Gazebo服务器
    ld.add_action(gazebo_client)  # 启动Gazebo客户端

    # 添加启动所有导航节点的动作
    ld.add_action(start_robot_state_publisher_cmd)  # 启动机器人状态发布器
    ld.add_action(rviz_cmd)  # 启动RViz
    ld.add_action(bringup_cmd)  # 启动Nav2导航堆栈

    # 返回启动描述对象
    return ld
