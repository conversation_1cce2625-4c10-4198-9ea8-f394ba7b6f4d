#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
顺序生命周期控制器

这个节点监听指定节点的生命周期状态，当依赖节点达到激活状态时，
自动启动下一个节点。实现基于状态的顺序启动，而不是基于时间的延迟启动。
"""

import rclpy
from rclpy.node import Node
from rclpy.executors import MultiThreadedExecutor
from rclpy.callback_groups import ReentrantCallbackGroup
from lifecycle_msgs.msg import State
from lifecycle_msgs.srv import GetState, ChangeState
from nav2_msgs.srv import ManageLifecycleNodes
import time
from typing import List, Dict, Optional
import threading


class SequentialLifecycleController(Node):
    """
    顺序生命周期控制器
    
    监听依赖节点的状态变化，当依赖节点激活后自动启动下一个节点。
    """

    def __init__(self):
        super().__init__('sequential_lifecycle_controller')
        
        # 使用可重入回调组以支持并发服务调用
        self.callback_group = ReentrantCallbackGroup()
        
        # 声明参数
        self.declare_parameter('dependency_node', 'amcl')
        self.declare_parameter('target_node', 'indoor_ekf_lifecycle_wrapper')
        self.declare_parameter('target_lifecycle_manager', 'lifecycle_manager_indoor_ekf')
        self.declare_parameter('check_interval', 1.0)  # 检查间隔（秒）
        self.declare_parameter('max_wait_time', 60.0)  # 最大等待时间（秒）
        
        # 获取参数
        self.dependency_node = self.get_parameter('dependency_node').get_parameter_value().string_value
        self.target_node = self.get_parameter('target_node').get_parameter_value().string_value
        self.target_lifecycle_manager = self.get_parameter('target_lifecycle_manager').get_parameter_value().string_value
        self.check_interval = self.get_parameter('check_interval').get_parameter_value().double_value
        self.max_wait_time = self.get_parameter('max_wait_time').get_parameter_value().double_value
        
        # 状态跟踪
        self.dependency_active = False
        self.target_started = False
        self.start_time = time.time()
        
        # 创建服务客户端
        self.dependency_get_state_client = self.create_client(
            GetState,
            f'/{self.dependency_node}/get_state',
            callback_group=self.callback_group
        )
        
        self.target_get_state_client = self.create_client(
            GetState,
            f'/{self.target_node}/get_state',
            callback_group=self.callback_group
        )
        
        self.lifecycle_manager_client = self.create_client(
            ManageLifecycleNodes,
            f'/{self.target_lifecycle_manager}/manage_nodes',
            callback_group=self.callback_group
        )
        
        # 创建定时器来检查依赖节点状态
        self.check_timer = self.create_timer(
            self.check_interval,
            self.check_dependency_status,
            callback_group=self.callback_group
        )
        
        self.get_logger().info(
            f'Sequential lifecycle controller initialized. '
            f'Waiting for {self.dependency_node} to become active, '
            f'then will start {self.target_node}'
        )

    def check_dependency_status(self):
        """检查依赖节点的状态"""
        # 检查是否超时
        if time.time() - self.start_time > self.max_wait_time:
            self.get_logger().error(
                f'Timeout waiting for {self.dependency_node} to become active. '
                f'Max wait time {self.max_wait_time}s exceeded.'
            )
            self.check_timer.cancel()
            return
        
        # 如果目标节点已经启动，停止检查
        if self.target_started:
            self.check_timer.cancel()
            return
        
        # 检查依赖节点状态
        if not self.dependency_get_state_client.service_is_ready():
            self.get_logger().debug(f'Waiting for {self.dependency_node} get_state service...')
            return
        
        # 异步调用获取状态服务
        request = GetState.Request()
        future = self.dependency_get_state_client.call_async(request)
        future.add_done_callback(self.handle_dependency_state_response)

    def handle_dependency_state_response(self, future):
        """处理依赖节点状态响应"""
        try:
            response = future.result()
            current_state = response.current_state.id
            
            self.get_logger().debug(
                f'{self.dependency_node} current state: {response.current_state.label} (id: {current_state})'
            )
            
            # 检查是否为激活状态 (State.PRIMARY_STATE_ACTIVE = 3)
            if current_state == State.PRIMARY_STATE_ACTIVE and not self.dependency_active:
                self.dependency_active = True
                self.get_logger().info(
                    f'{self.dependency_node} is now active. Starting {self.target_node}...'
                )
                self.start_target_node()
                
        except Exception as e:
            self.get_logger().error(f'Failed to get {self.dependency_node} state: {str(e)}')

    def start_target_node(self):
        """启动目标节点"""
        if self.target_started:
            return
        
        # 首先检查目标节点是否已经存在
        if not self.target_get_state_client.service_is_ready():
            self.get_logger().error(f'{self.target_node} get_state service is not available')
            return
        
        # 检查目标节点当前状态
        request = GetState.Request()
        future = self.target_get_state_client.call_async(request)
        future.add_done_callback(self.handle_target_state_check)

    def handle_target_state_check(self, future):
        """处理目标节点状态检查"""
        try:
            response = future.result()
            current_state = response.current_state.id
            
            self.get_logger().info(
                f'{self.target_node} current state: {response.current_state.label} (id: {current_state})'
            )
            
            # 如果目标节点已经是激活状态，不需要再启动
            if current_state == State.PRIMARY_STATE_ACTIVE:
                self.get_logger().info(f'{self.target_node} is already active')
                self.target_started = True
                return
            
            # 如果目标节点处于未配置状态，通过生命周期管理器启动它
            if current_state == State.PRIMARY_STATE_UNCONFIGURED:
                self.startup_target_via_lifecycle_manager()
            else:
                self.get_logger().warn(
                    f'{self.target_node} is in unexpected state: {response.current_state.label}. '
                    'Attempting to startup via lifecycle manager anyway.'
                )
                self.startup_target_via_lifecycle_manager()
                
        except Exception as e:
            self.get_logger().error(f'Failed to check {self.target_node} state: {str(e)}')

    def startup_target_via_lifecycle_manager(self):
        """通过生命周期管理器启动目标节点"""
        if not self.lifecycle_manager_client.service_is_ready():
            self.get_logger().error(f'{self.target_lifecycle_manager} service is not available')
            return
        
        # 发送启动请求
        request = ManageLifecycleNodes.Request()
        request.command = ManageLifecycleNodes.Request.STARTUP
        
        self.get_logger().info(f'Sending startup command to {self.target_lifecycle_manager}')
        
        future = self.lifecycle_manager_client.call_async(request)
        future.add_done_callback(self.handle_startup_response)

    def handle_startup_response(self, future):
        """处理启动响应"""
        try:
            response = future.result()
            if response.success:
                self.get_logger().info(
                    f'Successfully started {self.target_node} via {self.target_lifecycle_manager}'
                )
                self.target_started = True
            else:
                self.get_logger().error(
                    f'Failed to start {self.target_node} via {self.target_lifecycle_manager}'
                )
        except Exception as e:
            self.get_logger().error(f'Error in startup response: {str(e)}')


def main(args=None):
    rclpy.init(args=args)
    
    controller = SequentialLifecycleController()
    
    # 使用多线程执行器以支持并发服务调用
    executor = MultiThreadedExecutor()
    
    try:
        rclpy.spin(controller, executor=executor)
    except KeyboardInterrupt:
        pass
    finally:
        controller.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
