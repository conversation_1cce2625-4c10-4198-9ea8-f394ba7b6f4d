#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
纯事件驱动的启动控制

使用Launch事件系统实现启动依赖关系，不使用额外的智能控制器。
避免多个控制器之间的冲突。
"""

import os
from pathlib import Path

from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import (AppendEnvironmentVariable, ExecuteProcess, IncludeLaunchDescription,
                            SetEnvironmentVariable, DeclareLaunchArgument, TimerAction, 
                            RegisterEventHandler, EmitEvent, LogInfo)
from launch.substitutions import LaunchConfiguration
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.event_handlers import OnProcessStart, OnExecutionComplete
from launch_ros.actions import Node
from launch_ros.events.lifecycle import ChangeState
from lifecycle_msgs.msg import Transition
from nav2_common.launch import RewrittenYaml


def generate_launch_description():
    # 获取包目录和文件路径
    sim_dir = get_package_share_directory('nav2_minimal_tb3_sim')
    launch_dir = os.path.dirname(os.path.realpath(__file__))
    
    world_sdf_xacro = os.path.join(sim_dir, 'worlds', 'turtlebot3_house.sdf.xacro')
    robot_sdf = os.path.join(sim_dir, 'urdf', 'gz_waffle_gps.sdf.xacro')
    urdf = os.path.join(sim_dir, 'urdf', 'turtlebot3_waffle_gps.urdf')
    
    # 读取URDF
    with open(urdf, 'r') as infp:
        robot_description = infp.read()
    
    # 参数文件配置
    params_file = os.path.join(launch_dir, 'nav2_no_map_params.yaml')
    message_filter_params_file = os.path.join(launch_dir, 'message_filter_params.yaml')
    indoor_ekf_params_file = os.path.join(launch_dir, 'indoor_ekf_params.yaml')
    dual_ekf_params_file = os.path.join(launch_dir, 'dual_ekf_navsat_params.yaml')
    
    configured_params = RewrittenYaml(source_file=params_file, root_key='', param_rewrites='', convert_types=True)
    configured_message_filter_params = RewrittenYaml(source_file=message_filter_params_file, root_key='', param_rewrites='', convert_types=True)
    configured_indoor_ekf_params = RewrittenYaml(source_file=indoor_ekf_params_file, root_key='', param_rewrites='', convert_types=True)
    
    # 启动参数
    declare_use_sim_time_cmd = DeclareLaunchArgument('use_sim_time', default_value='True')

    # ============================================================================
    # 阶段1: 基础设施层
    # ============================================================================
    
    # Gazebo仿真器
    gazebo_server = ExecuteProcess(
        cmd=['gz', 'sim', '-r', '-s', world_sdf_xacro],
        output='screen',
        name='gazebo_server'
    )
    
    # 机器人生成 (当Gazebo启动后)
    robot_spawn = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(sim_dir, 'launch', 'spawn_tb3_gps.launch.py')),
        launch_arguments={
            'use_sim_time': 'True',
            'robot_sdf': robot_sdf,
            'x_pose': '1.17',
            'y_pose': '-1.5',
            'z_pose': '0.01',
        }.items(),
    )
    
    # 机器人状态发布器
    robot_state_publisher = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{'use_sim_time': True, 'robot_description': robot_description}],
    )

    # ============================================================================
    # 阶段2: 传感器和定位基础
    # ============================================================================
    
    # Robot Localization包装器
    robot_localization_wrapper = Node(
        package='nav2_system_tests',
        executable='robot_localization_lifecycle_wrapper.py',
        name='robot_localization_lifecycle_wrapper',
        output='screen',
        parameters=[
            {'params_file': dual_ekf_params_file},
            {'use_sim_time': True}
        ],
    )
    
    # 地图服务器
    map_server = Node(
        package='nav2_map_server',
        executable='map_server',
        name='map_server',
        output='screen',
        parameters=[configured_params, configured_message_filter_params, {'use_sim_time': True}],
        remappings=[('map', 'map'), ('map_metadata', 'map_metadata')],
    )

    # ============================================================================
    # 阶段3: 主要定位
    # ============================================================================
    
    # AMCL定位
    amcl = Node(
        package='nav2_amcl',
        executable='amcl',
        name='amcl',
        output='screen',
        parameters=[configured_params, configured_message_filter_params, {'use_sim_time': True}],
        remappings=[('scan', 'scan'), ('map', 'map'), ('map_metadata', 'map_metadata')],
    )
    
    # 主要定位的生命周期管理器
    lifecycle_manager_localization = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_localization',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'autostart': False},  # 手动控制启动
            {'node_names': ['robot_localization_lifecycle_wrapper', 'map_server', 'amcl']}
        ]
    )

    # ============================================================================
    # 阶段4: 室内定位
    # ============================================================================
    
    # 室内EKF包装器
    indoor_ekf_wrapper = Node(
        package='nav2_system_tests',
        executable='indoor_ekf_lifecycle_wrapper.py',
        name='indoor_ekf_lifecycle_wrapper',
        output='screen',
        parameters=[
            {'params_file': configured_indoor_ekf_params},
            {'use_sim_time': True}
        ],
    )
    
    # 室内EKF生命周期管理器
    lifecycle_manager_indoor_ekf = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_indoor_ekf',
        output='screen',
        parameters=[
            {'use_sim_time': True},
            {'autostart': False},  # 手动控制启动
            {'node_names': ['indoor_ekf_lifecycle_wrapper']}
        ]
    )

    # ============================================================================
    # 事件处理器 - 定义启动顺序
    # ============================================================================
    
    # 当Gazebo启动后，启动机器人相关组件
    start_robot_after_gazebo = RegisterEventHandler(
        OnProcessStart(
            target_action=gazebo_server,
            on_start=[
                LogInfo(msg="[EVENT] Gazebo started, launching robot components..."),
                TimerAction(
                    period=2.0,  # 等待2秒让Gazebo稳定
                    actions=[robot_spawn, robot_state_publisher]
                )
            ]
        )
    )
    
    # 当机器人生成后，启动传感器和定位基础
    start_localization_base_after_robot = RegisterEventHandler(
        OnExecutionComplete(
            target_action=robot_spawn,
            on_completion=[
                LogInfo(msg="[EVENT] Robot spawned, starting localization base..."),
                TimerAction(
                    period=3.0,  # 等待传感器数据稳定
                    actions=[
                        robot_localization_wrapper,
                        map_server,
                        amcl,
                        lifecycle_manager_localization,
                    ]
                )
            ]
        )
    )
    
    # 启动主要定位管理器 (在定位基础启动后)
    start_main_localization_manager = RegisterEventHandler(
        OnExecutionComplete(
            target_action=robot_spawn,
            on_completion=[
                TimerAction(
                    period=8.0,  # 等待定位基础稳定
                    actions=[
                        LogInfo(msg="[EVENT] Starting main localization manager..."),
                        EmitEvent(event=ChangeState(
                            lifecycle_node_matcher=lambda node: node.node_name == 'lifecycle_manager_localization',
                            transition_id=Transition.TRANSITION_CONFIGURE
                        )),
                        TimerAction(
                            period=1.0,
                            actions=[
                                EmitEvent(event=ChangeState(
                                    lifecycle_node_matcher=lambda node: node.node_name == 'lifecycle_manager_localization',
                                    transition_id=Transition.TRANSITION_ACTIVATE
                                ))
                            ]
                        )
                    ]
                )
            ]
        )
    )
    
    # 启动室内定位 (在主要定位启动后)
    start_indoor_localization = RegisterEventHandler(
        OnExecutionComplete(
            target_action=robot_spawn,
            on_completion=[
                TimerAction(
                    period=15.0,  # 等待主要定位稳定
                    actions=[
                        LogInfo(msg="[EVENT] Starting indoor localization..."),
                        indoor_ekf_wrapper,
                        lifecycle_manager_indoor_ekf,
                        TimerAction(
                            period=2.0,
                            actions=[
                                EmitEvent(event=ChangeState(
                                    lifecycle_node_matcher=lambda node: node.node_name == 'lifecycle_manager_indoor_ekf',
                                    transition_id=Transition.TRANSITION_CONFIGURE
                                )),
                                TimerAction(
                                    period=1.0,
                                    actions=[
                                        EmitEvent(event=ChangeState(
                                            lifecycle_node_matcher=lambda node: node.node_name == 'lifecycle_manager_indoor_ekf',
                                            transition_id=Transition.TRANSITION_ACTIVATE
                                        ))
                                    ]
                                )
                            ]
                        )
                    ]
                )
            ]
        )
    )

    # ============================================================================
    # 返回Launch描述
    # ============================================================================
    
    return LaunchDescription([
        # 参数声明
        declare_use_sim_time_cmd,
        
        # 环境变量设置
        SetEnvironmentVariable('RCUTILS_LOGGING_BUFFERED_STREAM', '1'),
        SetEnvironmentVariable('RCUTILS_LOGGING_USE_STDOUT', '1'),
        AppendEnvironmentVariable('GZ_SIM_RESOURCE_PATH', os.path.join(sim_dir, 'models')),
        AppendEnvironmentVariable('GZ_SIM_RESOURCE_PATH', str(Path(os.path.join(sim_dir)).parent.resolve())),
        
        # 基础设施
        gazebo_server,
        
        # 事件处理器 - 纯事件驱动，无额外控制器
        start_robot_after_gazebo,
        start_localization_base_after_robot,
        start_main_localization_manager,
        start_indoor_localization,
    ])


if __name__ == '__main__':
    generate_launch_description()
