#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
测试顺序启动的简化launch文件

这个launch文件用于测试基于生命周期节点依赖关系的顺序启动功能，
不包含仿真环境，只测试节点启动顺序。
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node
from nav2_common.launch import RewrittenYaml


def generate_launch_description():
    # 获取当前文件所在目录
    launch_dir = os.path.dirname(os.path.realpath(__file__))
    
    # 参数文件路径
    params_file = os.path.join(launch_dir, 'nav2_no_map_params.yaml')
    message_filter_params_file = os.path.join(launch_dir, 'message_filter_params.yaml')
    indoor_ekf_params_file = os.path.join(launch_dir, 'indoor_ekf_params.yaml')
    
    # 配置参数文件
    configured_params = RewrittenYaml(
        source_file=params_file,
        root_key='',
        param_rewrites='',
        convert_types=True,
    )
    
    configured_message_filter_params = RewrittenYaml(
        source_file=message_filter_params_file,
        root_key='',
        param_rewrites='',
        convert_types=True,
    )
    
    configured_indoor_ekf_params = RewrittenYaml(
        source_file=indoor_ekf_params_file,
        root_key='',
        param_rewrites='',
        convert_types=True,
    )
    
    # 声明参数
    declare_use_sim_time_cmd = DeclareLaunchArgument(
        'use_sim_time',
        default_value='False',
        description='使用仿真时间'
    )
    
    use_sim_time = LaunchConfiguration('use_sim_time')
    
    return LaunchDescription([
        # 声明参数
        declare_use_sim_time_cmd,
        
        # 地图服务器节点
        Node(
            package='nav2_map_server',
            executable='map_server',
            name='map_server',
            output='screen',
            parameters=[configured_params, configured_message_filter_params, {'use_sim_time': use_sim_time}],
        ),
        
        # AMCL节点
        Node(
            package='nav2_amcl',
            executable='amcl',
            name='amcl',
            output='screen',
            parameters=[configured_params, configured_message_filter_params, {'use_sim_time': use_sim_time}],
        ),
        
        # 室内EKF生命周期包装器节点
        Node(
            package='nav2_system_tests',
            executable='indoor_ekf_lifecycle_wrapper.py',
            name='indoor_ekf_lifecycle_wrapper',
            output='screen',
            parameters=[
                {'params_file': configured_indoor_ekf_params},
                {'use_sim_time': use_sim_time}
            ],
        ),
        
        # 主要生命周期管理器（管理map_server和amcl）
        Node(
            package='nav2_lifecycle_manager',
            executable='lifecycle_manager',
            name='lifecycle_manager_localization',
            output='screen',
            parameters=[
                {'use_sim_time': use_sim_time},
                {'autostart': True},
                {'node_names': ['map_server', 'amcl']}
            ]
        ),
        
        # 室内EKF生命周期管理器（不自动启动）
        Node(
            package='nav2_lifecycle_manager',
            executable='lifecycle_manager',
            name='lifecycle_manager_indoor_ekf',
            output='screen',
            parameters=[
                {'use_sim_time': use_sim_time},
                {'autostart': False},
                {'node_names': ['indoor_ekf_lifecycle_wrapper']}
            ]
        ),
        
        # 顺序生命周期控制器
        Node(
            package='nav2_system_tests',
            executable='sequential_lifecycle_controller.py',
            name='sequential_lifecycle_controller',
            output='screen',
            parameters=[
                {'dependency_node': 'amcl'},
                {'target_node': 'indoor_ekf_lifecycle_wrapper'},
                {'target_lifecycle_manager': 'lifecycle_manager_indoor_ekf'},
                {'check_interval': 1.0},
                {'max_wait_time': 30.0},
                {'use_sim_time': use_sim_time}
            ]
        ),
        
        # 测试器节点（可选）
        Node(
            package='nav2_system_tests',
            executable='test_sequential_startup.py',
            name='sequential_startup_tester',
            output='screen',
            parameters=[{'use_sim_time': use_sim_time}]
        ),
    ])


if __name__ == '__main__':
    generate_launch_description()
