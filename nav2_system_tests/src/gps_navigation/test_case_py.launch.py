#! /usr/bin/env python3

import os
from pathlib import Path
import sys

# 导入必要的ROS2包和模块
from ament_index_python.packages import get_package_share_directory  # 用于获取包的共享目录
from launch import LaunchDescription, LaunchService  # 用于创建启动描述和启动服务
from launch.actions import (AppendEnvironmentVariable, ExecuteProcess, IncludeLaunchDescription,
                            SetEnvironmentVariable, DeclareLaunchArgument, TimerAction)  # 启动动作
from launch.conditions import IfCondition  # 条件判断
from launch.substitutions import LaunchConfiguration  # 启动配置
from launch.launch_description_sources import PythonLaunchDescriptionSource  # Python启动描述源
from launch_ros.actions import Node  # ROS节点
from launch.actions import OpaqueFunction  # 用于创建不透明函数
from nav2_common.launch import RewrittenYaml  # 用于重写YAML参数文件


def generate_launch_description() -> LaunchDescription:
    # 生成启动描述，定义如何启动所有必要的节点和进程
    # 获取必要的包目录路径
    sim_dir = get_package_share_directory('nav2_minimal_tb3_sim')  # 仿真包目录
    nav2_bringup_dir = get_package_share_directory('nav2_bringup')  # Nav2启动包目录

    # 定义仿真世界和机器人模型文件路径
    world_sdf_xacro = os.path.join(sim_dir, 'worlds', 'turtlebot3_house.sdf.xacro')  # 房屋世界模型
    robot_sdf = os.path.join(sim_dir, 'urdf', 'gz_waffle_gps.sdf.xacro')  # 带GPS的Waffle机器人模型

    # 机器人URDF文件路径
    urdf = os.path.join(sim_dir, 'urdf', 'turtlebot3_waffle_gps.urdf')  # 带GPS的TurtleBot3 Waffle URDF

    # 读取机器人URDF文件内容
    with open(urdf, 'r') as infp:
        robot_description = infp.read()  # 读取URDF内容用于robot_state_publisher

    # 使用本地参数文件
    launch_dir = os.path.dirname(os.path.realpath(__file__))  # 获取当前文件所在目录
    params_file = os.path.join(launch_dir, 'nav2_no_map_params.yaml')  # 无地图导航参数文件
    message_filter_params_file = os.path.join(launch_dir, 'message_filter_params.yaml')  # 消息过滤器参数文件
    indoor_ekf_params_file = os.path.join(launch_dir, 'indoor_ekf_params.yaml')  # 室内EKF参数文件

    # 配置参数文件，使用RewrittenYaml处理参数
    configured_params = RewrittenYaml(
        source_file=params_file,  # 源参数文件
        root_key='',  # 根键
        param_rewrites='',  # 参数重写
        convert_types=True,  # 转换类型
    )

    # 配置消息过滤器参数文件
    configured_message_filter_params = RewrittenYaml(
        source_file=message_filter_params_file,  # 源参数文件
        root_key='',  # 根键
        param_rewrites='',  # 参数重写
        convert_types=True,  # 转换类型
    )

    # 配置室内EKF参数文件
    configured_indoor_ekf_params = RewrittenYaml(
        source_file=indoor_ekf_params_file,  # 源参数文件
        root_key='',  # 根键
        param_rewrites='',  # 参数重写
        convert_types=True,  # 转换类型
    )

    # 创建启动配置变量
    use_rviz = LaunchConfiguration('use_rviz')
    namespace = LaunchConfiguration('namespace')
    rviz_config_file = LaunchConfiguration('rviz_config')
    use_sim_time = LaunchConfiguration('use_sim_time')
    rviz_delay = LaunchConfiguration('rviz_delay')

    # 声明启动参数
    declare_use_rviz_cmd = DeclareLaunchArgument(
        'use_rviz',
        default_value='True',
        description='是否启动RViz可视化工具'
    )

    declare_namespace_cmd = DeclareLaunchArgument(
        'namespace',
        default_value='',
        description='顶级命名空间'
    )

    declare_rviz_config_file_cmd = DeclareLaunchArgument(
        'rviz_config',
        default_value=os.path.join(launch_dir, 'rviz', 'gps_nav_view.rviz'),
        description='RViz配置文件的完整路径'
    )

    # 声明RViz延迟启动时间参数
    declare_rviz_delay_cmd = DeclareLaunchArgument(
        'rviz_delay',
        default_value='25.0',
        description='RViz启动延迟时间（秒）'
    )

    declare_use_sim_time_cmd = DeclareLaunchArgument(
        'use_sim_time',
        default_value='True',
        description='使用仿真时间'
    )

    # 返回启动描述，包含所有需要启动的组件
    return LaunchDescription(
        [
            # 声明启动参数
            declare_use_rviz_cmd,
            declare_namespace_cmd,
            declare_rviz_config_file_cmd,
            declare_rviz_delay_cmd,
            declare_use_sim_time_cmd,

            # 设置日志环境变量
            SetEnvironmentVariable('RCUTILS_LOGGING_BUFFERED_STREAM', '1'),  # 启用缓冲流
            SetEnvironmentVariable('RCUTILS_LOGGING_USE_STDOUT', '1'),  # 使用标准输出
            # 添加Gazebo仿真资源路径
            AppendEnvironmentVariable(
                'GZ_SIM_RESOURCE_PATH', os.path.join(sim_dir, 'models')  # 添加模型路径
            ),
            AppendEnvironmentVariable(
                'GZ_SIM_RESOURCE_PATH',
                str(Path(os.path.join(sim_dir)).parent.resolve()),  # 添加父目录路径
            ),
            # 添加turtlebot3_house模型路径
            AppendEnvironmentVariable(
                'GZ_SIM_RESOURCE_PATH',
                os.path.join(sim_dir, 'models', 'turtlebot3_house'),  # 添加house模型路径
            ),
            # 添加纹理路径
            AppendEnvironmentVariable(
                'GZ_SIM_RESOURCE_PATH',
                os.path.join(sim_dir, 'models', 'turtlebot3_house', 'cafe_table', 'materials', 'textures'),  # 添加纹理路径
            ),
            # 启动Gazebo仿真器服务器（无GUI）
            ExecuteProcess(
                cmd=['gz', 'sim', '-r', '-s', world_sdf_xacro],  # 运行Gazebo仿真服务器，-r表示实时，-s表示仅启动服务器
                output='screen',  # 输出到屏幕
            ),
            # 启动Gazebo仿真器客户端（GUI）
            # IncludeLaunchDescription(
            #     PythonLaunchDescriptionSource(
            #         os.path.join(get_package_share_directory('ros_gz_sim'),
            #                      'launch',
            #                      'gz_sim.launch.py')
            #     ),
            #     launch_arguments={'gz_args': ['-v4 -g ']}.items(),
            # ),
            # 包含生成TurtleBot3机器人的启动文件
            IncludeLaunchDescription(
                PythonLaunchDescriptionSource(
                    os.path.join(sim_dir, 'launch', 'spawn_tb3_gps.launch.py')  # 生成带GPS的TB3机器人
                ),
                launch_arguments={
                    'use_sim_time': 'True',  # 使用仿真时间
                    'robot_sdf': robot_sdf,  # 机器人SDF文件
                    'x_pose': '1.17',  # 初始X坐标
                    'y_pose': '-1.5',  # 初始Y坐标
                    'z_pose': '0.01',  # 初始Z坐标
                    'roll': '0.0',    # 初始横滚角
                    'pitch': '0.0',   # 初始俯仰角
                    'yaw': '0.0',     # 初始偏航角
                }.items(),
            ),
            # 启动机器人状态发布节点
            Node(
                package='robot_state_publisher',  # 包名
                executable='robot_state_publisher',  # 可执行文件
                name='robot_state_publisher',  # 节点名
                output='screen',  # 输出到屏幕
                parameters=[
                    {'use_sim_time': True, 'robot_description': robot_description}  # 参数设置
                ],
            ),

            # 包含Nav2导航系统启动文件
            # IncludeLaunchDescription(
            #     PythonLaunchDescriptionSource(
            #         os.path.join(nav2_bringup_dir, 'launch', 'navigation_launch.py')  # Nav2导航启动文件
            #     ),
            #     launch_arguments={
            #         'namespace': '',  # 命名空间
            #         'use_sim_time': 'True',  # 使用仿真时间
            #         'params_file': configured_params,  # 配置参数文件
            #         'message_filter_params_file': configured_message_filter_params,  # 消息过滤器参数文件
            #         'use_composition': 'False',  # False不使用组合节点
            #         'autostart': 'true',
            #     }.items(),
            # ),

            # 它负责启动和管理robot_localization包中的EKF和navsat_transform节点
            Node(
                package='nav2_system_tests',  # 包名
                executable='robot_localization_lifecycle_wrapper.py',  # 可执行文件
                name='robot_localization_lifecycle_wrapper',  # 节点名
                output='screen',  # 输出到屏幕
                parameters=[
                    {'params_file': os.path.join(launch_dir, 'dual_ekf_navsat_params.yaml')},  # 参数文件路径
                    {'use_sim_time': True}  # 使用仿真时间
                ],
            ),

            # 添加地图服务器节点
            Node(
                package='nav2_map_server',
                executable='map_server',
                name='map_server',
                output='screen',
                parameters=[configured_params, configured_message_filter_params],
                remappings=[
                    ('map', 'map'),
                    ('map_metadata', 'map_metadata'),
                ],
            ),

            # 添加AMCL节点（立即启动）
            Node(
                package='nav2_amcl',
                executable='amcl',
                name='amcl',
                output='screen',
                parameters=[configured_params, configured_message_filter_params],
                remappings=[
                    ('scan', 'scan'),
                    ('map', 'map'),
                    ('map_metadata', 'map_metadata'),
                ],
            ),

            # 添加室内EKF生命周期包装器节点（立即启动但不激活）
            # 负责发布odom-baselink变换，补充AMCL只发布map-odom的不足
            Node(
                package='nav2_system_tests',
                executable='indoor_ekf_lifecycle_wrapper.py',
                name='indoor_ekf_lifecycle_wrapper',
                output='screen',
                parameters=[
                    {'params_file': configured_indoor_ekf_params},
                    {'use_sim_time': True}
                ],
            ),

            # 添加生命周期管理器节点，用于管理地图服务器和定位相关节点
            # 只管理立即启动的节点，室内EKF延迟启动后需要单独管理
            Node(
                package='nav2_lifecycle_manager',
                executable='lifecycle_manager',
                name='lifecycle_manager_localization',
                output='screen',
                parameters=[
                    {'use_sim_time': True},
                    {'autostart': True},
                    # 只包含立即启动的节点
                    {'node_names': ['map_server', 'amcl']}
                ]
            ),

            # 为室内EKF添加单独的生命周期管理器（不自动启动）
            Node(
                package='nav2_lifecycle_manager',
                executable='lifecycle_manager',
                name='lifecycle_manager_indoor_ekf',
                output='screen',
                parameters=[
                    {'use_sim_time': True},
                    {'autostart': False},  # 不自动启动，由顺序控制器控制
                    {'node_names': ['indoor_ekf_lifecycle_wrapper']}
                ]
            ),

            # 添加顺序生命周期控制器
            # 监听AMCL状态，当AMCL激活后自动启动室内EKF
            Node(
                package='nav2_system_tests',
                executable='sequential_lifecycle_controller.py',
                name='sequential_lifecycle_controller',
                output='screen',
                parameters=[
                    {'dependency_node': 'amcl'},
                    {'target_node': 'indoor_ekf_lifecycle_wrapper'},
                    {'target_lifecycle_manager': 'lifecycle_manager_indoor_ekf'},
                    {'check_interval': 1.0},
                    {'max_wait_time': 60.0},
                    {'use_sim_time': True}
                ]
            ),

            # 添加环境检测节点
            # Node(
            #     package='nav2_system_tests',
            #     executable='environment_detector_node.py',
            #     name='environment_detector',
            #     output='screen',
            #     parameters=[
            #         configured_params,  # 使用配置好的参数文件
            #         configured_message_filter_params  # 使用消息过滤器参数
            #     ],
            # ),

            # 添加地图管理节点
            # Node(
            #     package='nav2_system_tests',
            #     executable='map_manager_node.py',
            #     name='map_manager',
            #     output='screen',
            #     parameters=[
            #         configured_params,  # 使用配置好的参数文件
            #         {'use_sim_time': True}
            #     ],
            # ),

            # 启动RViz2可视化工具（延迟启动）
            # TimerAction(
            #     period=rviz_delay,
            #     actions=[
            #         Node(
            #             package='rviz2',
            #             executable='rviz2',
            #             name='rviz2',
            #             arguments=['-d', rviz_config_file],
            #             output='screen',
            #             parameters=[{'use_sim_time': True}],
            #             remappings=[
            #                 ('/tf', 'tf'),
            #                 ('/tf_static', 'tf_static'),
            #             ],
            #             condition=IfCondition(use_rviz),
            #         ),
            #     ]
            # ),
        ]
    )


# 主函数，只运行launch文件
def main(argv: list[str] = sys.argv[1:]):  # type: ignore[no-untyped-def]
    # 生成启动描述
    ld = generate_launch_description()

    # 创建启动服务
    ls = LaunchService(argv=argv)  # 创建启动服务
    ls.include_launch_description(ld)  # 包含启动描述

    # 运行启动服务
    print("启动所有节点，按Ctrl+C退出...")
    ls.run()

    return 0  # 总是返回成功


# 主程序入口
if __name__ == '__main__':
    sys.exit(main())  # 执行主函数并以其返回值退出
