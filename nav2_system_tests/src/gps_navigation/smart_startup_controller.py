#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
智能启动控制器

基于节点状态和话题数据的智能启动控制，实现真正的依赖关系管理。
"""

import rclpy
from rclpy.node import Node
from rclpy.executors import MultiThreadedExecutor
from rclpy.callback_groups import ReentrantCallbackGroup
from lifecycle_msgs.msg import State
from lifecycle_msgs.srv import GetState
from nav2_msgs.srv import ManageLifecycleNodes
from sensor_msgs.msg import LaserScan, Imu
from nav_msgs.msg import Odometry, OccupancyGrid
import time
from typing import Dict, List, Callable, Optional
from enum import Enum


class StartupStage(Enum):
    """启动阶段枚举"""
    INFRASTRUCTURE = "infrastructure"
    SENSORS = "sensors"
    LOCALIZATION_BASE = "localization_base"
    MAIN_LOCALIZATION = "main_localization"
    INDOOR_LOCALIZATION = "indoor_localization"
    ADVANCED_FEATURES = "advanced_features"
    COMPLETED = "completed"


class SmartStartupController(Node):
    """
    智能启动控制器
    
    监听系统状态，基于实际的节点状态和数据流来控制启动顺序。
    """

    def __init__(self):
        super().__init__('smart_startup_controller')
        
        self.callback_group = ReentrantCallbackGroup()
        
        # 当前启动阶段
        self.current_stage = StartupStage.INFRASTRUCTURE
        self.stage_start_time = time.time()
        
        # 节点状态跟踪
        self.node_states: Dict[str, int] = {}
        self.topic_data_received: Dict[str, bool] = {}
        
        # 启动阶段配置
        self.stage_config = {
            StartupStage.INFRASTRUCTURE: {
                'duration': 3.0,  # 最大等待时间
                'required_topics': [],
                'required_nodes': [],
                'next_stage_trigger': self._check_infrastructure_ready,
            },
            StartupStage.SENSORS: {
                'duration': 5.0,
                'required_topics': ['/odom', '/imu/data', '/scan'],
                'required_nodes': [],
                'next_stage_trigger': self._check_sensors_ready,
            },
            StartupStage.LOCALIZATION_BASE: {
                'duration': 3.0,
                'required_topics': ['/map'],
                'required_nodes': ['robot_localization_lifecycle_wrapper', 'map_server'],
                'next_stage_trigger': self._check_localization_base_ready,
            },
            StartupStage.MAIN_LOCALIZATION: {
                'duration': 5.0,
                'required_topics': [],
                'required_nodes': ['amcl'],
                'next_stage_trigger': self._check_main_localization_ready,
            },
            StartupStage.INDOOR_LOCALIZATION: {
                'duration': 3.0,
                'required_topics': [],
                'required_nodes': ['indoor_ekf_lifecycle_wrapper'],
                'next_stage_trigger': self._check_indoor_localization_ready,
            },
        }
        
        # 生命周期管理器客户端
        self.lifecycle_managers = {
            'localization': 'lifecycle_manager_localization',
            'indoor_ekf': 'lifecycle_manager_indoor_ekf',
        }
        
        self.lifecycle_clients = {}
        for name, manager_name in self.lifecycle_managers.items():
            self.lifecycle_clients[name] = self.create_client(
                ManageLifecycleNodes,
                f'/{manager_name}/manage_nodes',
                callback_group=self.callback_group
            )
        
        # 话题订阅器（用于检测数据流）
        self.topic_subscribers = {}
        self._setup_topic_subscribers()
        
        # 节点状态客户端
        self.state_clients = {}
        
        # 主控制定时器
        self.control_timer = self.create_timer(
            1.0,  # 每秒检查一次
            self.control_loop,
            callback_group=self.callback_group
        )
        
        self.get_logger().info(f'Smart startup controller initialized. Starting stage: {self.current_stage.value}')

    def _setup_topic_subscribers(self):
        """设置话题订阅器来监听数据流"""
        topic_configs = [
            ('/odom', Odometry),
            ('/imu/data', Imu),
            ('/scan', LaserScan),
            ('/map', OccupancyGrid),
        ]
        
        for topic_name, msg_type in topic_configs:
            self.topic_data_received[topic_name] = False
            self.topic_subscribers[topic_name] = self.create_subscription(
                msg_type,
                topic_name,
                lambda msg, topic=topic_name: self._on_topic_data_received(topic),
                10,
                callback_group=self.callback_group
            )

    def _on_topic_data_received(self, topic_name: str):
        """话题数据接收回调"""
        if not self.topic_data_received[topic_name]:
            self.topic_data_received[topic_name] = True
            self.get_logger().info(f'First data received on topic: {topic_name}')

    def control_loop(self):
        """主控制循环"""
        if self.current_stage == StartupStage.COMPLETED:
            return
        
        stage_config = self.stage_config.get(self.current_stage)
        if not stage_config:
            return
        
        # 检查是否超时
        elapsed_time = time.time() - self.stage_start_time
        if elapsed_time > stage_config['duration']:
            self.get_logger().warn(
                f'Stage {self.current_stage.value} timeout after {elapsed_time:.1f}s, '
                'proceeding to next stage anyway'
            )
            self._advance_to_next_stage()
            return
        
        # 检查是否可以进入下一阶段
        if stage_config['next_stage_trigger']():
            self._advance_to_next_stage()

    def _check_infrastructure_ready(self) -> bool:
        """检查基础设施是否就绪"""
        # 简单的时间检查，等待Gazebo启动
        return time.time() - self.stage_start_time > 2.0

    def _check_sensors_ready(self) -> bool:
        """检查传感器数据是否就绪"""
        required_topics = self.stage_config[StartupStage.SENSORS]['required_topics']
        for topic in required_topics:
            if not self.topic_data_received.get(topic, False):
                return False
        return True

    def _check_localization_base_ready(self) -> bool:
        """检查定位基础是否就绪"""
        # 检查必需的话题
        if not self.topic_data_received.get('/map', False):
            return False
        
        # 检查必需的节点状态
        required_nodes = self.stage_config[StartupStage.LOCALIZATION_BASE]['required_nodes']
        return self._check_nodes_active(required_nodes)

    def _check_main_localization_ready(self) -> bool:
        """检查主要定位是否就绪"""
        required_nodes = self.stage_config[StartupStage.MAIN_LOCALIZATION]['required_nodes']
        return self._check_nodes_active(required_nodes)

    def _check_indoor_localization_ready(self) -> bool:
        """检查室内定位是否就绪"""
        required_nodes = self.stage_config[StartupStage.INDOOR_LOCALIZATION]['required_nodes']
        return self._check_nodes_active(required_nodes)

    def _check_nodes_active(self, node_names: List[str]) -> bool:
        """检查指定节点是否都已激活"""
        for node_name in node_names:
            if not self._is_node_active(node_name):
                return False
        return True

    def _is_node_active(self, node_name: str) -> bool:
        """检查单个节点是否激活"""
        if node_name not in self.state_clients:
            self.state_clients[node_name] = self.create_client(
                GetState,
                f'/{node_name}/get_state',
                callback_group=self.callback_group
            )
        
        client = self.state_clients[node_name]
        if not client.service_is_ready():
            return False
        
        try:
            request = GetState.Request()
            future = client.call_async(request)
            # 非阻塞检查
            if future.done():
                response = future.result()
                is_active = response.current_state.id == State.PRIMARY_STATE_ACTIVE
                self.node_states[node_name] = response.current_state.id
                return is_active
        except Exception as e:
            self.get_logger().debug(f'Failed to check state of {node_name}: {e}')
        
        return False

    def _advance_to_next_stage(self):
        """进入下一阶段"""
        old_stage = self.current_stage
        
        # 确定下一阶段
        stage_order = list(StartupStage)
        current_index = stage_order.index(self.current_stage)
        
        if current_index < len(stage_order) - 1:
            self.current_stage = stage_order[current_index + 1]
            self.stage_start_time = time.time()
            
            self.get_logger().info(
                f'Advancing from {old_stage.value} to {self.current_stage.value}'
            )
            
            # 触发相应的启动动作
            self._trigger_stage_actions()
        else:
            self.current_stage = StartupStage.COMPLETED
            self.get_logger().info('All startup stages completed!')

    def _trigger_stage_actions(self):
        """触发当前阶段的启动动作"""
        if self.current_stage == StartupStage.LOCALIZATION_BASE:
            self._start_localization_base()
        elif self.current_stage == StartupStage.MAIN_LOCALIZATION:
            self._start_main_localization()
        elif self.current_stage == StartupStage.INDOOR_LOCALIZATION:
            self._start_indoor_localization()

    def _start_localization_base(self):
        """启动定位基础组件"""
        self.get_logger().info('Starting localization base components...')
        # 这里可以发送启动命令给相应的生命周期管理器
        # 或者发布启动事件给其他节点

    def _start_main_localization(self):
        """启动主要定位组件"""
        self.get_logger().info('Starting main localization components...')
        if 'localization' in self.lifecycle_clients:
            self._call_lifecycle_manager('localization', ManageLifecycleNodes.Request.STARTUP)

    def _start_indoor_localization(self):
        """启动室内定位组件"""
        self.get_logger().info('Starting indoor localization components...')
        if 'indoor_ekf' in self.lifecycle_clients:
            self._call_lifecycle_manager('indoor_ekf', ManageLifecycleNodes.Request.STARTUP)

    def _call_lifecycle_manager(self, manager_name: str, command: int):
        """调用生命周期管理器"""
        client = self.lifecycle_clients.get(manager_name)
        if not client or not client.service_is_ready():
            self.get_logger().warn(f'Lifecycle manager {manager_name} not ready')
            return
        
        request = ManageLifecycleNodes.Request()
        request.command = command
        
        future = client.call_async(request)
        future.add_done_callback(
            lambda f, name=manager_name: self._lifecycle_manager_callback(f, name)
        )

    def _lifecycle_manager_callback(self, future, manager_name: str):
        """生命周期管理器调用回调"""
        try:
            response = future.result()
            if response.success:
                self.get_logger().info(f'Successfully started {manager_name}')
            else:
                self.get_logger().error(f'Failed to start {manager_name}')
        except Exception as e:
            self.get_logger().error(f'Error calling {manager_name}: {e}')


def main(args=None):
    rclpy.init(args=args)
    
    controller = SmartStartupController()
    executor = MultiThreadedExecutor()
    
    try:
        rclpy.spin(controller, executor=executor)
    except KeyboardInterrupt:
        pass
    finally:
        controller.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
