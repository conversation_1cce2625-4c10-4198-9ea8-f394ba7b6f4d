#!/usr/bin/env python3

"""
AMCL自动初始化器
当AMCL激活时，自动根据当前odom位置设置初始位置
"""

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, DurabilityPolicy
from geometry_msgs.msg import PoseWithCovarianceStamped
from nav_msgs.msg import Odometry
from lifecycle_msgs.msg import TransitionEvent
import tf2_ros
import tf2_geometry_msgs
from tf2_geometry_msgs import do_transform_pose
import math


class AMCLAutoInitializer(Node):
    """AMCL自动初始化器节点"""
    
    def __init__(self):
        super().__init__('amcl_auto_initializer')
        
        # 参数
        self.declare_parameter('odom_topic', '/odom')
        self.declare_parameter('initialpose_topic', '/initialpose')
        self.declare_parameter('amcl_node_name', 'amcl')
        self.declare_parameter('auto_init_timeout', 5.0)  # 秒
        self.declare_parameter('position_tolerance', 0.1)  # 米
        
        self.odom_topic = self.get_parameter('odom_topic').value
        self.initialpose_topic = self.get_parameter('initialpose_topic').value
        self.amcl_node_name = self.get_parameter('amcl_node_name').value
        self.auto_init_timeout = self.get_parameter('auto_init_timeout').value
        self.position_tolerance = self.get_parameter('position_tolerance').value
        
        # 状态变量
        self.amcl_active = False
        self.last_odom = None
        self.initialization_pending = False
        
        # TF相关
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer, self)
        
        # QoS配置
        qos_profile = QoSProfile(
            reliability=ReliabilityPolicy.RELIABLE,
            durability=DurabilityPolicy.TRANSIENT_LOCAL,
            depth=1
        )
        
        # 订阅者
        self.odom_sub = self.create_subscription(
            Odometry,
            self.odom_topic,
            self.odom_callback,
            10
        )
        
        self.transition_sub = self.create_subscription(
            TransitionEvent,
            f'/{self.amcl_node_name}/transition_event',
            self.transition_callback,
            qos_profile
        )
        
        # 发布者
        self.initialpose_pub = self.create_publisher(
            PoseWithCovarianceStamped,
            self.initialpose_topic,
            qos_profile
        )
        
        # 定时器
        self.init_timer = None
        
        self.get_logger().info('AMCL自动初始化器已启动')
        
    def odom_callback(self, msg):
        """里程计回调"""
        self.last_odom = msg
        
    def transition_callback(self, msg):
        """AMCL状态转换回调"""
        if msg.goal_state.label == 'active' and msg.result_code == 0:
            self.get_logger().info('检测到AMCL激活，准备自动初始化')
            self.amcl_active = True
            self.schedule_initialization()
        elif msg.start_state.label == 'active':
            self.get_logger().info('检测到AMCL停用')
            self.amcl_active = False
            self.cancel_initialization()
            
    def schedule_initialization(self):
        """安排初始化"""
        if self.initialization_pending:
            return
            
        self.initialization_pending = True
        
        # 延迟一段时间后执行初始化，给AMCL时间完全启动
        if self.init_timer:
            self.init_timer.cancel()
            
        self.init_timer = self.create_timer(
            2.0,  # 2秒延迟
            self.perform_initialization
        )
        
        self.get_logger().info('已安排AMCL自动初始化（2秒后执行）')
        
    def cancel_initialization(self):
        """取消初始化"""
        self.initialization_pending = False
        if self.init_timer:
            self.init_timer.cancel()
            self.init_timer = None
            
    def perform_initialization(self):
        """执行初始化"""
        try:
            # 取消定时器
            if self.init_timer:
                self.init_timer.cancel()
                self.init_timer = None
                
            self.initialization_pending = False
            
            if not self.amcl_active:
                self.get_logger().warning('AMCL未激活，跳过自动初始化')
                return
                
            if not self.last_odom:
                self.get_logger().warning('未收到里程计数据，跳过自动初始化')
                return
                
            # 获取当前odom位置
            odom_pose = self.last_odom.pose.pose
            
            # 创建初始位置消息
            initial_pose = PoseWithCovarianceStamped()
            initial_pose.header.stamp = self.get_clock().now().to_msg()
            initial_pose.header.frame_id = 'map'
            
            # 设置位置（假设map和odom坐标系重合）
            initial_pose.pose.pose.position.x = odom_pose.position.x
            initial_pose.pose.pose.position.y = odom_pose.position.y
            initial_pose.pose.pose.position.z = 0.0
            initial_pose.pose.pose.orientation = odom_pose.orientation
            
            # 设置协方差矩阵（较大的不确定性）
            initial_pose.pose.covariance = [
                0.5, 0.0, 0.0, 0.0, 0.0, 0.0,   # x
                0.0, 0.5, 0.0, 0.0, 0.0, 0.0,   # y
                0.0, 0.0, 0.0, 0.0, 0.0, 0.0,   # z
                0.0, 0.0, 0.0, 0.0, 0.0, 0.0,   # roll
                0.0, 0.0, 0.0, 0.0, 0.0, 0.0,   # pitch
                0.0, 0.0, 0.0, 0.0, 0.0, 0.1    # yaw
            ]
            
            # 发布初始位置
            self.initialpose_pub.publish(initial_pose)
            
            self.get_logger().info(
                f'已自动设置AMCL初始位置: '
                f'x={odom_pose.position.x:.3f}, '
                f'y={odom_pose.position.y:.3f}, '
                f'yaw={self.get_yaw_from_quaternion(odom_pose.orientation):.3f}'
            )
            
        except Exception as e:
            self.get_logger().error(f'自动初始化AMCL时发生错误: {e}')
            import traceback
            self.get_logger().error(traceback.format_exc())
            
    def get_yaw_from_quaternion(self, quaternion):
        """从四元数获取yaw角"""
        import math
        
        # 转换四元数到欧拉角
        siny_cosp = 2 * (quaternion.w * quaternion.z + quaternion.x * quaternion.y)
        cosy_cosp = 1 - 2 * (quaternion.y * quaternion.y + quaternion.z * quaternion.z)
        yaw = math.atan2(siny_cosp, cosy_cosp)
        
        return yaw


def main(args=None):
    rclpy.init(args=args)
    
    node = AMCLAutoInitializer()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
