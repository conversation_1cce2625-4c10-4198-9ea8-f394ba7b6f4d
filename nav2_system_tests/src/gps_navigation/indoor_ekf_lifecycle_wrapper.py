#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
室内EKF生命周期包装器节点。

这个包装器节点是一个生命周期节点，可以被lifecycle_manager管理，
它负责启动和停止室内EKF节点，用于在室内环境下发布odom-baselink变换。
"""

import os
import subprocess
import signal
from typing import Optional

import rclpy
from rclpy.lifecycle import LifecycleNode, State, TransitionCallbackReturn


class IndoorEkfLifecycleWrapper(LifecycleNode):
    """
    室内EKF节点的生命周期包装器。

    这个节点作为生命周期节点，可以被Nav2的lifecycle_manager管理，
    负责启动和停止室内EKF节点。
    """

    def __init__(self):
        super().__init__('indoor_ekf_lifecycle_wrapper')

        # 存储子进程
        self.ekf_process: Optional[subprocess.Popen] = None

        # 声明参数
        try:
            self.declare_parameter('params_file', '')
        except:
            pass  # 参数已存在
        try:
            self.declare_parameter('use_sim_time', True)
        except:
            pass  # 参数已存在

        self.get_logger().info('Indoor EKF lifecycle wrapper initialized')

    def on_configure(self, state: State) -> TransitionCallbackReturn:
        """配置状态回调"""
        self.get_logger().info('Configuring indoor EKF wrapper...')

        # 获取参数
        params_file_param = self.get_parameter('params_file').get_parameter_value()
        if hasattr(params_file_param, 'string_value'):
            self.params_file = params_file_param.string_value
        else:
            # 如果是RewrittenYaml对象，需要特殊处理
            self.params_file = str(params_file_param)
            
        self.use_sim_time = self.get_parameter('use_sim_time').get_parameter_value().bool_value

        if not self.params_file:
            self.get_logger().error('params_file parameter is required')
            return TransitionCallbackReturn.FAILURE

        # 检查参数文件是否存在（如果是文件路径）
        if isinstance(self.params_file, str) and self.params_file.endswith('.yaml'):
            if not os.path.exists(self.params_file):
                self.get_logger().error(f'Parameters file does not exist: {self.params_file}')
                return TransitionCallbackReturn.FAILURE

        self.get_logger().info('Indoor EKF wrapper configured successfully')
        return TransitionCallbackReturn.SUCCESS

    def on_activate(self, state: State) -> TransitionCallbackReturn:
        """激活状态回调 - 启动室内EKF节点"""
        self.get_logger().info('Activating indoor EKF wrapper...')

        try:
            self._start_indoor_ekf()
            self.get_logger().info('Indoor EKF node started successfully')
            return TransitionCallbackReturn.SUCCESS

        except Exception as e:
            self.get_logger().error(f'Failed to start indoor EKF node: {str(e)}')
            self._stop_indoor_ekf()
            return TransitionCallbackReturn.FAILURE

    def on_deactivate(self, state: State) -> TransitionCallbackReturn:
        """停用状态回调 - 停止室内EKF节点"""
        self.get_logger().info('Deactivating indoor EKF wrapper...')

        self._stop_indoor_ekf()

        self.get_logger().info('Indoor EKF wrapper deactivated')
        return TransitionCallbackReturn.SUCCESS

    def on_cleanup(self, state: State) -> TransitionCallbackReturn:
        """清理状态回调"""
        self.get_logger().info('Cleaning up indoor EKF wrapper...')

        # 确保进程已停止
        self._stop_indoor_ekf()

        self.get_logger().info('Indoor EKF wrapper cleaned up')
        return TransitionCallbackReturn.SUCCESS

    def on_shutdown(self, state: State) -> TransitionCallbackReturn:
        """关闭状态回调"""
        self.get_logger().info('Shutting down indoor EKF wrapper...')

        self._stop_indoor_ekf()

        self.get_logger().info('Indoor EKF wrapper shut down')
        return TransitionCallbackReturn.SUCCESS

    def _start_indoor_ekf(self) -> None:
        """启动室内EKF节点"""
        if self.ekf_process is not None:
            self.get_logger().warn('Indoor EKF node is already running')
            return

        # 构建ros2 run命令
        cmd = [
            'ros2', 'run',
            'robot_localization',
            'ekf_node',
            '--ros-args',
            '--params-file', str(self.params_file),
            '-p', f'use_sim_time:={self.use_sim_time}',
            '-r', '__node:=ekf_filter_node_indoor',
            '-r', 'odometry/filtered:=odometry/indoor'
        ]

        self.get_logger().info(f'Starting indoor EKF node with command: {" ".join(cmd)}')

        try:
            self.ekf_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid  # 创建新的进程组
            )
            self.get_logger().info(f'Indoor EKF node started with PID {self.ekf_process.pid}')

        except Exception as e:
            self.get_logger().error(f'Failed to start indoor EKF node: {str(e)}')
            raise

    def _stop_indoor_ekf(self) -> None:
        """停止室内EKF节点"""
        if self.ekf_process is None:
            return

        if self.ekf_process.poll() is None:  # 进程仍在运行
            self.get_logger().info(f'Stopping indoor EKF node (PID: {self.ekf_process.pid})')
            try:
                # 发送SIGTERM信号给整个进程组
                os.killpg(os.getpgid(self.ekf_process.pid), signal.SIGTERM)

                # 等待进程结束
                try:
                    self.ekf_process.wait(timeout=5.0)
                    self.get_logger().info('Indoor EKF node stopped gracefully')
                except subprocess.TimeoutExpired:
                    # 如果5秒后还没结束，强制杀死
                    self.get_logger().warn('Indoor EKF node did not stop gracefully, force killing')
                    os.killpg(os.getpgid(self.ekf_process.pid), signal.SIGKILL)
                    self.ekf_process.wait()

            except ProcessLookupError:
                # 进程已经不存在
                self.get_logger().info('Indoor EKF node process already terminated')
            except Exception as e:
                self.get_logger().error(f'Error stopping indoor EKF node: {str(e)}')
        else:
            self.get_logger().info('Indoor EKF node was already stopped')

        self.ekf_process = None


def main(args=None):
    """主函数"""
    rclpy.init(args=args)

    try:
        node = IndoorEkfLifecycleWrapper()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f'Error: {e}')
    finally:
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
