# 基于生命周期节点依赖关系的顺序启动

这个实现提供了一种基于实际节点状态而不是固定时间延迟的顺序启动方法。

## 核心组件

### 1. SequentialLifecycleController (sequential_lifecycle_controller.py)

这是核心控制器，负责：
- 监听依赖节点的生命周期状态
- 当依赖节点达到激活状态时，自动启动目标节点
- 通过生命周期管理器控制目标节点的启动

**参数：**
- `dependency_node`: 依赖节点名称（如 'amcl'）
- `target_node`: 目标节点名称（如 'indoor_ekf_lifecycle_wrapper'）
- `target_lifecycle_manager`: 目标节点的生命周期管理器名称
- `check_interval`: 状态检查间隔（秒）
- `max_wait_time`: 最大等待时间（秒）

### 2. 测试工具

#### test_sequential_startup.py
监听多个节点的状态变化，记录启动顺序和时间，验证启动顺序是否正确。

#### test_sequential_launch.py
简化的launch文件，用于测试顺序启动功能，不包含仿真环境。

## 使用方法

### 在主launch文件中使用

1. **创建目标节点**（不自动激活）：
```python
Node(
    package='nav2_system_tests',
    executable='indoor_ekf_lifecycle_wrapper.py',
    name='indoor_ekf_lifecycle_wrapper',
    output='screen',
    parameters=[...],
),
```

2. **创建目标节点的生命周期管理器**（不自动启动）：
```python
Node(
    package='nav2_lifecycle_manager',
    executable='lifecycle_manager',
    name='lifecycle_manager_indoor_ekf',
    output='screen',
    parameters=[
        {'autostart': False},  # 关键：不自动启动
        {'node_names': ['indoor_ekf_lifecycle_wrapper']}
    ]
),
```

3. **添加顺序生命周期控制器**：
```python
Node(
    package='nav2_system_tests',
    executable='sequential_lifecycle_controller.py',
    name='sequential_lifecycle_controller',
    output='screen',
    parameters=[
        {'dependency_node': 'amcl'},
        {'target_node': 'indoor_ekf_lifecycle_wrapper'},
        {'target_lifecycle_manager': 'lifecycle_manager_indoor_ekf'},
        {'check_interval': 1.0},
        {'max_wait_time': 60.0}
    ]
),
```

### 测试顺序启动

1. **运行测试launch文件**：
```bash
ros2 launch nav2_system_tests test_sequential_launch.py
```

2. **查看启动日志**，应该看到类似输出：
```
[2.15s] map_server: active (id: 3)
*** map_server ACTIVATED at 2.15s ***
[3.42s] amcl: active (id: 3)
*** amcl ACTIVATED at 3.42s ***
[4.58s] indoor_ekf_lifecycle_wrapper: active (id: 3)
*** indoor_ekf_lifecycle_wrapper ACTIVATED at 4.58s ***
```

## 工作原理

1. **初始状态**：
   - 依赖节点（如AMCL）通过主生命周期管理器正常启动
   - 目标节点（如室内EKF）创建但不激活
   - 目标节点的生命周期管理器创建但不自动启动

2. **监听阶段**：
   - 顺序控制器定期检查依赖节点的状态
   - 使用ROS2生命周期服务获取实时状态

3. **触发启动**：
   - 当依赖节点达到激活状态时
   - 控制器调用目标节点的生命周期管理器
   - 发送启动命令激活目标节点

## 优势

1. **精确控制**：基于实际节点状态，不依赖固定时间
2. **可靠性高**：避免了时间延迟可能导致的启动失败
3. **可扩展**：可以轻松添加更多依赖关系
4. **调试友好**：提供详细的状态日志

## 扩展使用

### 多级依赖

可以创建多个顺序控制器实现多级依赖：

```python
# 第一级：AMCL -> 室内EKF
Node(
    package='nav2_system_tests',
    executable='sequential_lifecycle_controller.py',
    name='sequential_controller_1',
    parameters=[
        {'dependency_node': 'amcl'},
        {'target_node': 'indoor_ekf_lifecycle_wrapper'},
        ...
    ]
),

# 第二级：室内EKF -> 其他节点
Node(
    package='nav2_system_tests',
    executable='sequential_lifecycle_controller.py',
    name='sequential_controller_2',
    parameters=[
        {'dependency_node': 'indoor_ekf_lifecycle_wrapper'},
        {'target_node': 'another_node'},
        ...
    ]
),
```

### 自定义状态检查

可以修改控制器代码来检查特定的状态或条件，而不仅仅是激活状态。

## 故障排除

1. **节点未启动**：检查生命周期管理器的`autostart`参数是否设置为`False`
2. **启动超时**：增加`max_wait_time`参数值
3. **状态检查失败**：确保依赖节点名称正确，服务可用
