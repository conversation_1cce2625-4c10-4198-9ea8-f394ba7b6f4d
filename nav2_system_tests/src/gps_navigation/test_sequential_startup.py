#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
测试顺序启动功能

这个脚本用于测试基于生命周期节点依赖关系的顺序启动功能。
它会监听节点状态变化并记录启动顺序。
"""

import rclpy
from rclpy.node import Node
from lifecycle_msgs.msg import State
from lifecycle_msgs.srv import GetState
import time
from typing import Dict, List


class SequentialStartupTester(Node):
    """
    顺序启动测试器
    
    监听指定节点的状态变化，记录启动顺序和时间。
    """

    def __init__(self):
        super().__init__('sequential_startup_tester')
        
        # 要监听的节点列表
        self.nodes_to_monitor = [
            'map_server',
            'amcl', 
            'indoor_ekf_lifecycle_wrapper'
        ]
        
        # 状态跟踪
        self.node_states: Dict[str, int] = {}
        self.activation_times: Dict[str, float] = {}
        self.start_time = time.time()
        
        # 创建服务客户端
        self.state_clients = {}
        for node_name in self.nodes_to_monitor:
            self.state_clients[node_name] = self.create_client(
                GetState,
                f'/{node_name}/get_state'
            )
        
        # 创建定时器来定期检查状态
        self.check_timer = self.create_timer(2.0, self.check_all_nodes_status)
        
        self.get_logger().info('Sequential startup tester initialized')
        self.get_logger().info(f'Monitoring nodes: {self.nodes_to_monitor}')

    def check_all_nodes_status(self):
        """检查所有节点的状态"""
        for node_name in self.nodes_to_monitor:
            self.check_node_status(node_name)

    def check_node_status(self, node_name: str):
        """检查单个节点的状态"""
        client = self.state_clients[node_name]
        
        if not client.service_is_ready():
            return
        
        request = GetState.Request()
        future = client.call_async(request)
        future.add_done_callback(lambda f, name=node_name: self.handle_state_response(f, name))

    def handle_state_response(self, future, node_name: str):
        """处理状态响应"""
        try:
            response = future.result()
            current_state = response.current_state.id
            previous_state = self.node_states.get(node_name, -1)
            
            # 如果状态发生变化，记录日志
            if current_state != previous_state:
                elapsed_time = time.time() - self.start_time
                state_label = response.current_state.label
                
                self.get_logger().info(
                    f'[{elapsed_time:.2f}s] {node_name}: {state_label} (id: {current_state})'
                )
                
                # 如果节点变为激活状态，记录激活时间
                if current_state == State.PRIMARY_STATE_ACTIVE and node_name not in self.activation_times:
                    self.activation_times[node_name] = elapsed_time
                    self.get_logger().info(
                        f'*** {node_name} ACTIVATED at {elapsed_time:.2f}s ***'
                    )
                    
                    # 检查是否所有节点都已激活
                    if len(self.activation_times) == len(self.nodes_to_monitor):
                        self.print_activation_summary()
                
                self.node_states[node_name] = current_state
                
        except Exception as e:
            # 节点可能还没有启动，这是正常的
            pass

    def print_activation_summary(self):
        """打印激活总结"""
        self.get_logger().info('=' * 60)
        self.get_logger().info('ACTIVATION SUMMARY:')
        self.get_logger().info('=' * 60)
        
        # 按激活时间排序
        sorted_activations = sorted(self.activation_times.items(), key=lambda x: x[1])
        
        for i, (node_name, activation_time) in enumerate(sorted_activations, 1):
            self.get_logger().info(f'{i}. {node_name}: {activation_time:.2f}s')
        
        # 检查启动顺序是否正确
        expected_order = ['map_server', 'amcl', 'indoor_ekf_lifecycle_wrapper']
        actual_order = [item[0] for item in sorted_activations]
        
        if actual_order == expected_order:
            self.get_logger().info('✓ STARTUP ORDER IS CORRECT!')
        else:
            self.get_logger().warn(f'✗ UNEXPECTED STARTUP ORDER!')
            self.get_logger().warn(f'Expected: {expected_order}')
            self.get_logger().warn(f'Actual:   {actual_order}')
        
        self.get_logger().info('=' * 60)


def main(args=None):
    rclpy.init(args=args)
    
    tester = SequentialStartupTester()
    
    try:
        rclpy.spin(tester)
    except KeyboardInterrupt:
        pass
    finally:
        tester.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
