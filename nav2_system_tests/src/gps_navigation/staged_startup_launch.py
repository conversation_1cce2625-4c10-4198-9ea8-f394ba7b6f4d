#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
分阶段启动的GPS导航系统Launch文件

实现基于时间的分阶段启动顺序：
阶段1 (0-2s): 基础设施层
阶段2 (2-5s): 传感器和定位基础
阶段3 (5-8s): 主要定位
阶段4 (8-10s): 室内定位
阶段5 (10+s): 高级功能
"""

import os
from pathlib import Path

from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import (AppendEnvironmentVariable, ExecuteProcess, IncludeLaunchDescription,
                            SetEnvironmentVariable, DeclareLaunchArgument, TimerAction)
from launch.conditions import IfCondition
from launch.substitutions import LaunchConfiguration
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.actions import Node
from nav2_common.launch import RewrittenYaml


def generate_launch_description():
    # 获取包目录
    sim_dir = get_package_share_directory('nav2_minimal_tb3_sim')
    nav2_bringup_dir = get_package_share_directory('nav2_bringup')
    launch_dir = os.path.dirname(os.path.realpath(__file__))

    # 文件路径
    world_sdf_xacro = os.path.join(sim_dir, 'worlds', 'turtlebot3_house.sdf.xacro')
    robot_sdf = os.path.join(sim_dir, 'urdf', 'gz_waffle_gps.sdf.xacro')
    urdf = os.path.join(sim_dir, 'urdf', 'turtlebot3_waffle_gps.urdf')

    # 读取URDF
    with open(urdf, 'r') as infp:
        robot_description = infp.read()

    # 参数文件
    params_file = os.path.join(launch_dir, 'nav2_no_map_params.yaml')
    message_filter_params_file = os.path.join(launch_dir, 'message_filter_params.yaml')
    indoor_ekf_params_file = os.path.join(launch_dir, 'indoor_ekf_params.yaml')
    dual_ekf_params_file = os.path.join(launch_dir, 'dual_ekf_navsat_params.yaml')

    # 配置参数
    configured_params = RewrittenYaml(source_file=params_file, root_key='', param_rewrites='', convert_types=True)
    configured_message_filter_params = RewrittenYaml(source_file=message_filter_params_file, root_key='', param_rewrites='', convert_types=True)
    configured_indoor_ekf_params = RewrittenYaml(source_file=indoor_ekf_params_file, root_key='', param_rewrites='', convert_types=True)

    # 启动参数
    declare_use_sim_time_cmd = DeclareLaunchArgument('use_sim_time', default_value='True', description='使用仿真时间')
    declare_enable_rviz_cmd = DeclareLaunchArgument('enable_rviz', default_value='False', description='启用RViz')
    declare_enable_nav2_cmd = DeclareLaunchArgument('enable_nav2', default_value='False', description='启用Nav2导航')

    use_sim_time = LaunchConfiguration('use_sim_time')
    enable_rviz = LaunchConfiguration('enable_rviz')
    enable_nav2 = LaunchConfiguration('enable_nav2')

    return LaunchDescription([
        # 声明参数
        declare_use_sim_time_cmd,
        declare_enable_rviz_cmd,
        declare_enable_nav2_cmd,

        # ============================================================================
        # 阶段1: 基础设施层 (0-2秒) - 立即启动
        # ============================================================================
        
        # 环境变量设置
        SetEnvironmentVariable('RCUTILS_LOGGING_BUFFERED_STREAM', '1'),
        SetEnvironmentVariable('RCUTILS_LOGGING_USE_STDOUT', '1'),
        AppendEnvironmentVariable('GZ_SIM_RESOURCE_PATH', os.path.join(sim_dir, 'models')),
        AppendEnvironmentVariable('GZ_SIM_RESOURCE_PATH', str(Path(os.path.join(sim_dir)).parent.resolve())),
        AppendEnvironmentVariable('GZ_SIM_RESOURCE_PATH', os.path.join(sim_dir, 'models', 'turtlebot3_house')),

        # Gazebo仿真器
        ExecuteProcess(
            cmd=['gz', 'sim', '-r', '-s', world_sdf_xacro],
            output='screen',
        ),

        # 机器人生成和桥接 (延迟2秒等待Gazebo稳定)
        TimerAction(
            period=2.0,
            actions=[
                IncludeLaunchDescription(
                    PythonLaunchDescriptionSource(os.path.join(sim_dir, 'launch', 'spawn_tb3_gps.launch.py')),
                    launch_arguments={
                        'use_sim_time': 'True',
                        'robot_sdf': robot_sdf,
                        'x_pose': '1.17',
                        'y_pose': '-1.5',
                        'z_pose': '0.01',
                        'roll': '0.0',
                        'pitch': '0.0',
                        'yaw': '0.0',
                    }.items(),
                ),
                
                # 机器人状态发布器
                Node(
                    package='robot_state_publisher',
                    executable='robot_state_publisher',
                    name='robot_state_publisher',
                    output='screen',
                    parameters=[{'use_sim_time': True, 'robot_description': robot_description}],
                ),
            ]
        ),

        # ============================================================================
        # 阶段2: 传感器和定位基础 (2-5秒)
        # ============================================================================
        
        TimerAction(
            period=5.0,
            actions=[
                # Robot Localization包装器
                Node(
                    package='nav2_system_tests',
                    executable='robot_localization_lifecycle_wrapper.py',
                    name='robot_localization_lifecycle_wrapper',
                    output='screen',
                    parameters=[
                        {'params_file': dual_ekf_params_file},
                        {'use_sim_time': True}
                    ],
                ),
                
                # 地图服务器
                Node(
                    package='nav2_map_server',
                    executable='map_server',
                    name='map_server',
                    output='screen',
                    parameters=[configured_params, configured_message_filter_params, {'use_sim_time': True}],
                    remappings=[('map', 'map'), ('map_metadata', 'map_metadata')],
                ),
            ]
        ),

        # ============================================================================
        # 阶段3: 主要定位 (5-8秒)
        # ============================================================================
        
        TimerAction(
            period=8.0,
            actions=[
                # AMCL定位
                Node(
                    package='nav2_amcl',
                    executable='amcl',
                    name='amcl',
                    output='screen',
                    parameters=[configured_params, configured_message_filter_params, {'use_sim_time': True}],
                    remappings=[('scan', 'scan'), ('map', 'map'), ('map_metadata', 'map_metadata')],
                ),
                
                # 主要定位的生命周期管理器
                Node(
                    package='nav2_lifecycle_manager',
                    executable='lifecycle_manager',
                    name='lifecycle_manager_localization',
                    output='screen',
                    parameters=[
                        {'use_sim_time': True},
                        {'autostart': True},
                        {'node_names': ['robot_localization_lifecycle_wrapper', 'map_server', 'amcl']}
                    ]
                ),
            ]
        ),

        # ============================================================================
        # 阶段4: 室内定位 (8-10秒)
        # ============================================================================
        
        TimerAction(
            period=10.0,
            actions=[
                # 室内EKF包装器
                Node(
                    package='nav2_system_tests',
                    executable='indoor_ekf_lifecycle_wrapper.py',
                    name='indoor_ekf_lifecycle_wrapper',
                    output='screen',
                    parameters=[
                        {'params_file': configured_indoor_ekf_params},
                        {'use_sim_time': True}
                    ],
                ),
                
                # 室内EKF生命周期管理器
                Node(
                    package='nav2_lifecycle_manager',
                    executable='lifecycle_manager',
                    name='lifecycle_manager_indoor_ekf',
                    output='screen',
                    parameters=[
                        {'use_sim_time': True},
                        {'autostart': True},
                        {'node_names': ['indoor_ekf_lifecycle_wrapper']}
                    ]
                ),
            ]
        ),

        # ============================================================================
        # 阶段5: 高级功能 (10+秒) - 可选
        # ============================================================================
        
        # Nav2导航系统 (如果启用)
        TimerAction(
            period=12.0,
            actions=[
                IncludeLaunchDescription(
                    PythonLaunchDescriptionSource(os.path.join(nav2_bringup_dir, 'launch', 'navigation_launch.py')),
                    launch_arguments={
                        'namespace': '',
                        'use_sim_time': 'True',
                        'params_file': configured_params,
                        'use_composition': 'False',
                        'autostart': 'true',
                    }.items(),
                    condition=IfCondition(enable_nav2),
                ),
            ]
        ),

        # RViz可视化 (如果启用)
        TimerAction(
            period=15.0,
            actions=[
                Node(
                    package='rviz2',
                    executable='rviz2',
                    name='rviz2',
                    arguments=['-d', os.path.join(launch_dir, 'rviz', 'gps_nav_view.rviz')],
                    output='screen',
                    parameters=[{'use_sim_time': True}],
                    condition=IfCondition(enable_rviz),
                ),
            ]
        ),
    ])


if __name__ == '__main__':
    generate_launch_description()
