# 室内EKF定位节点参数配置
# 专门用于室内环境下发布odom-baselink变换
# 参考shuangekf中的odom发布节点配置

ekf_filter_node_indoor:
  ros__parameters:
    frequency: 20.0  # EKF频率20Hz
    two_d_mode: true  # 使用2D模式，适合nav2的2D世界表示
    print_diagnostics: true
    debug: false
    publish_tf: true # 默认启用，这样在代码中只需要deactive，不需要修改参数了

    # 坐标系配置
    map_frame: map
    odom_frame: odom
    base_link_frame: base_link
    world_frame: odom  # 世界坐标系设为odom，发布odom-baselink变换

    # 里程计输入配置
    odom0: odom
    odom0_config: [true,  true,  false,    # x, y, z位置 - 使用x,y位置
                  false, false, true,      # roll, pitch, yaw角度 - 使用yaw角度
                  true,  true,  true,      # x, y, z速度 - 使用
                  false, false, true,      # roll, pitch, yaw角速度 - 只使用yaw角速度
                  false, false, false]     # x, y, z加速度 - 不使用
    odom0_queue_size: 20  # 增加队列大小
    odom0_differential: false  # 不使用差分模式
    odom0_relative: false  # 不使用相对模式
    odom0_pose_rejection_threshold: 5.0  # 位置拒绝阈值
    odom0_twist_rejection_threshold: 1.0  # 速度拒绝阈值
    odom0_nodelay: true  # 禁用延迟处理

    # IMU输入配置（如果有的话）
    imu0: imu/data
    imu0_config: [false, false, false,     # x, y, z位置 - 不使用
                  false, false, true,      # roll, pitch, yaw角度 - 只使用yaw
                  false, false, false,     # x, y, z速度 - 不使用
                  false, false, false,     # roll, pitch, yaw角速度 - 不使用
                  false, false, false]     # x, y, z加速度 - 不使用
    imu0_differential: false  # 不使用差分模式
    imu0_relative: false  # 不使用相对模式
    imu0_queue_size: 10
    imu0_remove_gravitational_acceleration: true  # 移除重力加速度

    # 不使用控制输入
    use_control: false

    # 过程噪声协方差矩阵（对角线元素）
    process_noise_covariance: [0.05, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
                              0.0,  0.05, 0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
                              0.0,  0.0,  0.06, 0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
                              0.0,  0.0,  0.0,  0.03, 0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
                              0.0,  0.0,  0.0,  0.0,  0.03, 0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
                              0.0,  0.0,  0.0,  0.0,  0.0,  0.06, 0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
                              0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.025, 0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
                              0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.025, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
                              0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.04, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,
                              0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.01, 0.0,  0.0,  0.0,  0.0,  0.0,
                              0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.01, 0.0,  0.0,  0.0,  0.0,
                              0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.02, 0.0,  0.0,  0.0,
                              0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.01, 0.0,  0.0,
                              0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.01, 0.0,
                              0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.015]

    # 初始估计误差协方差矩阵（对角线元素）
    initial_estimate_covariance: [1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,
                                 0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,
                                 0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,
                                 0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,
                                 0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,
                                 0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,
                                 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,
                                 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  0.0,
                                 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9, 0.0,   0.0,   0.0,   0.0,  0.0,  0.0,
                                 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  1e-9,  0.0,   0.0,   0.0,  0.0,  0.0,
                                 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   1e-9,  0.0,   0.0,  0.0,  0.0,
                                 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   1e-9,  0.0,  0.0,  0.0,
                                 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   1e-9, 0.0,  0.0,
                                 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  1e-9, 0.0,
                                 0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,  0.0,   0.0,   0.0,   0.0,  0.0,  1e-9]
