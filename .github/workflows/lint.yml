name: Lint
on:
  pull_request:

jobs:
  ament_lint_general:
    name: ament_${{ matrix.linter }}
    runs-on: ubuntu-latest
    container:
      image: rostooling/setup-ros-docker:ubuntu-noble-ros-rolling-ros-base-latest
    strategy:
      fail-fast: false
      matrix:
          linter: [xmllint, cpplint, uncrustify, pep257, flake8, mypy]
    steps:
    - uses: actions/checkout@v4

    - name: Install typeshed for mypy
      if: matrix.linter == 'mypy'
      run: sudo apt update && sudo apt install -y python3-typeshed

    - uses: ros-tooling/action-ros-lint@v0.1
      with:
        linter: ${{ matrix.linter }}
        distribution: rolling
        package-name: "*"
        arguments: ${{ matrix.linter == 'mypy' && '--config tools/pyproject.toml' || '' }}

  pre-commit:
    name: pre-commit
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
      - uses: pre-commit/action@v3.0.1
        env:
          SKIP: >-
            ament_lint_cmake,
            ament_cpplint,
            ament_uncrustify,
            ament_xmllint,
            ament_flake8,
            ament_pep257,
            ament_mypy
