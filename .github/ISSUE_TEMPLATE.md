<!--
For general questions, please ask on Robotics Stack Exchange: https://robotics.stackexchange.com/, make sure to include at least the `ros2` and `nav2` tags and the rosdistro version you are running, e.g. `ardent`.
For general design discussions, please post on discourse: https://discourse.ros.org/c/ng-ros
Not sure if this is the right repository? Open an issue on https://github.com/ros-navigation/navigation2
For Bug report or feature requests, please fill out the relevant category below
-->

## Bug report

**Required Info:**

- Operating System:
  - <!-- OS and version (e.g. Windows 10, Ubuntu 16.04...) -->
- Computer:
  - <!-- <PERSON><PERSON><PERSON>, 13th Gen Intel NUC, Ryzen 9 7940-HS -->
- ROS2 Version:
  - <!-- ROS2 distribution and install method (e.g. Foxy binaries, Dashing source...) -->
- Version or commit hash:
  - <!-- from source: output of `git -C navigation2 rev-parse HEAD
         apt binaries: output of: dpkg-query --show "ros-$ROS_DISTRO-navigation2"
                              or: dpkg-query --show "ros-$ROS_DISTRO-nav2-*" -->
- DDS implementation:
  - <!-- rmw_implementation used (e.g. Fast-RTPS, RTI Connext, etc.) -->

#### Steps to reproduce issue
<!-- Detailed instructions on how to reliably reproduce this issue http://sscce.org/
``` code that can be copy-pasted is preferred ``` -->
```

```

#### Expected behavior

#### Actual behavior

#### Additional information

<!-- If you are reporting a bug delete everything below
     If you are requesting a feature deleted everything above this line & Configuration & Tuning Help -->
----
## Feature request

#### Feature description
<!-- Description in a few sentences what the feature consists of and what problem it will solve -->

#### Implementation considerations
<!-- Relevant information on how the feature could be implemented and pros and cons of the different solutions -->

<!-- If you are reporting a bug or feature delete everything above this line -->
----
## Configuration or Tuning Help

**Required Info:**

- Operating System:
  - <!-- OS and version (e.g. Windows 10, Ubuntu 16.04...) -->
- Computer:
  - <!-- Nvidia Jetson Orin, 13th Gen Intel NUC, Ryzen 9 7940-HS -->
- ROS2 Version:
  - <!-- ROS2 distribution and install method (e.g. Foxy binaries, Dashing source...) -->
- Version or commit hash:
  - <!-- from source: output of `git -C navigation2 rev-parse HEAD
         apt binaries: output of: dpkg-query --show "ros-$ROS_DISTRO-navigation2"
                              or: dpkg-query --show "ros-$ROS_DISTRO-nav2-*" -->
- DDS implementation:
  - <!-- rmw_implementation used (e.g. Fast-RTPS, RTI Connext, etc.) -->
- Nav2 Package:
  - <!-- MPPI, DWB, Smac, etc-->

#### Tuning / Configuration Goal
<!-- Description in a few sentences what the aim is that you're trying to tune / configure -->

#### What I've Tried
<!-- Description **in detail** what you've tried, methodology, graphs or figures, and progress -->
