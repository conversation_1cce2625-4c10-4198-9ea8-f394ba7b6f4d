#!/bin/bash

# 立即捕获所有错误，确保脚本在出错时立即停止
# -e: 如果命令返回非零状态，立即退出
# -o pipefail: 管道中任何命令失败都会导致脚本退出
set -eo pipefail

# 可以取消注释以启用调试模式
# set -x  # 显示执行的每一行命令
# env     # 显示所有环境变量

# 为用户启用自动补全功能，复制默认的.bashrc文件
cp /etc/skel/.bashrc ~/

# 检测ROS2版本
ROS_VERSION=""
if [ -d "/opt/ros/rolling" ]; then
  ROS_VERSION="rolling"
elif [ -d "/opt/ros/iron" ]; then
  ROS_VERSION="iron"
elif [ -d "/opt/ros/humble" ]; then
  ROS_VERSION="humble"
else
  echo "未检测到支持的ROS2版本(rolling, iron, humble)，将尝试使用rolling"
  ROS_VERSION="rolling"
fi

echo "检测到ROS2版本: $ROS_VERSION"

# 添加ROS 2环境变量到.bashrc文件，这样每次打开新终端时都会自动加载这些环境
# 使用检测到的ROS2版本
echo "source /opt/ros/$ROS_VERSION/setup.bash" >> ~/.bashrc

# 只有在这些目录存在时才添加它们
if [ -d "/opt/underlay_ws/install" ]; then
  echo 'source /opt/underlay_ws/install/setup.bash' >> ~/.bashrc
fi

if [ -d "/opt/overlay_ws/install" ]; then
  echo 'source /opt/overlay_ws/install/setup.bash' >> ~/.bashrc
fi

# 设置当前会话的环境变量，这样在当前脚本中就可以使用ROS 2命令
# 按照从基础到高级的顺序加载，这样高级包可以覆盖基础包的设置
if [ -f "/opt/ros/$ROS_VERSION/setup.bash" ]; then
  source /opt/ros/$ROS_VERSION/setup.bash
else
  echo "警告: /opt/ros/$ROS_VERSION/setup.bash 不存在，跳过加载"
fi

if [ -f "/opt/underlay_ws/install/setup.bash" ]; then
  source /opt/underlay_ws/install/setup.bash
else
  echo "警告: /opt/underlay_ws/install/setup.bash 不存在，跳过加载"
fi

if [ -f "/opt/overlay_ws/install/setup.bash" ]; then
  source /opt/overlay_ws/install/setup.bash
else
  echo "警告: /opt/overlay_ws/install/setup.bash 不存在，跳过加载"
fi

# 检查srv文件夹是否存在（用于网页应用）
if [ -n "$ROOT_SRV" ] && [ -d "$ROOT_SRV" ]; then
    # 设置Nav2网页应用，创建符号链接
    # 这部分代码将caddy/srv目录下的所有子目录链接到ROOT_SRV目录
    # 这样可以通过网页浏览器访问Nav2的可视化界面
    if [ -d "$OVERLAY_WS/src/navigation2/.devcontainer/caddy/srv" ]; then
        for dir in $OVERLAY_WS/src/navigation2/.devcontainer/caddy/srv/*; \
            do if [ -d "$dir" ]; then ln -s "$dir" $ROOT_SRV; fi done
    else
        echo "警告: caddy/srv目录不存在，跳过网页应用设置"
    fi
else
    echo "警告: ROOT_SRV环境变量未定义或目录不存在，跳过网页应用设置"
fi

# 检查并安装transforms3d库
echo "检查transforms3d库是否已安装..."
if python3 -c "import transforms3d" 2>/dev/null; then
    echo "transforms3d库已安装，无需操作。"
else
    echo "transforms3d库未安装，正在尝试安装..."

    # 尝试使用apt安装
    if sudo apt-get update && sudo apt-get install -y python3-transforms3d; then
        echo "transforms3d库安装成功！"
    else
        echo "使用apt安装失败，尝试使用pip安装..."

        # 尝试使用pip安装
        if sudo pip3 install transforms3d --break-system-packages; then
            echo "transforms3d库安装成功！"
        else
            echo "安装失败。请手动安装transforms3d库："
            echo "sudo apt-get install -y python3-transforms3d"
            echo "或"
            echo "sudo pip3 install transforms3d --break-system-packages"
        fi
    fi
fi

# 输出安装完成的提示信息和使用指南
echo "环境已经设置完成，可以开始使用Nav2了。"
echo "已尝试安装RViz2、RQT和相关插件，可以使用以下命令启动Nav2模拟环境："

# 启动Nav2模拟环境的命令
# 这个命令会启动Gazebo仿真器、TurtleBot3机器人模型和Nav2导航堆栈
echo "ros2 launch nav2_bringup tb3_simulation_launch.py"

# 如果不使用集成的启动文件，可以单独启动RViz2进行可视化
echo "在另一个终端中启动RViz2："
echo "ros2 launch nav2_bringup rviz_launch.py"

# RQT提供了多种图形化工具，如节点图、话题监控、参数设置等
echo "启动RQT工具："
echo "rqt"

# 常用的RQT插件推荐
echo "常用RQT插件推荐："
echo "- rqt_graph: 显示ROS节点和话题的连接关系"
echo "- rqt_topic: 监控和发布ROS话题"
echo "- rqt_console: 查看日志消息"
echo "- rqt_reconfigure: 动态调整ROS参数"
