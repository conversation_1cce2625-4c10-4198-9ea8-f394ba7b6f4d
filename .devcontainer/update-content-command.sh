#!/bin/bash

# Immediately catch all errors
set -eo pipefail

# Uncomment for debugging
# set -x
# env

# 检查OVERLAY_WS环境变量是否设置
if [ -z "$OVERLAY_WS" ]; then
  echo "警告: OVERLAY_WS环境变量未设置，使用默认值/opt/overlay_ws"
  export OVERLAY_WS=/opt/overlay_ws
fi

# 如果目录不存在，则创建它
if [ ! -d "$OVERLAY_WS" ]; then
  echo "创建目录: $OVERLAY_WS"
  mkdir -p $OVERLAY_WS
fi

cd $OVERLAY_WS

# 我们已经构建了Nav2，所以不需要再次构建
echo "Nav2已经构建完成，使用预构建的镜像。"

# 设置环境变量，只在文件存在时才加载
if [ -n "$UNDERLAY_WS" ] && [ -f "$UNDERLAY_WS/install/setup.sh" ]; then
  echo "加载底层工作空间: $UNDERLAY_WS/install/setup.sh"
  . $UNDERLAY_WS/install/setup.sh
else
  echo "底层工作空间setup.sh不存在，跳过加载"
fi

if [ -f "$OVERLAY_WS/install/setup.sh" ]; then
  echo "加载上层工作空间: $OVERLAY_WS/install/setup.sh"
  . $OVERLAY_WS/install/setup.sh
else
  echo "上层工作空间setup.sh不存在，跳过加载"
fi

# 设置成功标志
echo "环境设置完成"
