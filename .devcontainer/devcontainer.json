{
    // Devcontainer 名称
    "name": "Nav2 Development",
    // 使用预先构建好的镜像
    "image": "nav2:built",
    // 容器初始化时执行的命令，允许本地 docker 访问 X11
    "initializeCommand": "xhost +local:docker",
    // 容器运行参数
    "runArgs": [
        "--name=nav2-vscode", // 容器名称
        "--network=host", // 使用主机网络
        "--privileged", // 赋予容器特权权限
        "--cap-add=SYS_PTRACE", // 添加调试能力
        "--security-opt=seccomp:unconfined", // 关闭 seccomp 安全限制
        "--security-opt=apparmor:unconfined", // 关闭 apparmor 安全限制
        "--volume=/tmp/.X11-unix:/tmp/.X11-unix", // 显示转发
        "--volume=/dev:/dev", // 设备映射
        "--device=/dev/dri:/dev/dri" // 显卡直通
    ],
    // 容器环境变量
    "containerEnv": {
        "DISPLAY": "${localEnv:DISPLAY}", // X11 显示变量
        "QT_X11_NO_MITSHM": "1", // 解决 Qt X11 共享内存问题
        "ROS_DOMAIN_ID": "11" // ROS2 通信域 ID
    },
    // 工作区目录
    "workspaceFolder": "/opt/overlay_ws/src/navigation2",
    // 工作区挂载配置
    "workspaceMount": "source=${localWorkspaceFolder},target=${containerWorkspaceFolder},type=bind",
    // 容器创建时执行的脚本
    "onCreateCommand": ".devcontainer/on-create-command.sh",
    // 更新内容时执行的脚本
    "updateContentCommand": ".devcontainer/update-content-command.sh",
    // 容器创建后执行的脚本
    "postCreateCommand": ".devcontainer/post-create-command.sh",
    // 远程环境变量
    "remoteEnv": {
        "OVERLAY_MIXINS": "release ccache lld", // 构建混合参数
        "CCACHE_DIR": "/tmp/.ccache" // ccache 缓存目录
    },
    // 额外挂载卷
    "mounts": [
        {
            "source": "ccache-${devcontainerId}", // ccache 卷名
            "target": "/tmp/.ccache", // 容器内挂载点
            "type": "volume" // 类型为卷
        },
        {
            "source": "overlay-${devcontainerId}", // overlay 卷名
            "target": "/opt/overlay_ws", // 容器内挂载点
            "type": "volume" // 类型为卷
        }
    ],
    // VSCode 个性化设置
    "customizations": {
        "vscode": {
            // 推荐安装的插件列表
            "extensions": [
                "althack.ament-task-provider", // ROS2 构建工具
                "eamodio.gitlens", // Git 工具
                "esbenp.prettier-vscode", // 代码格式化
                "ms-iot.vscode-ros", // ROS 支持
                "ms-vscode.cpptools", // C++ 支持
                "ms-python.python", // Python 支持
                "twxs.cmake", // CMake 支持
                "ms-vscode.cmake-tools", // CMake 工具
                "redhat.vscode-yaml", // YAML 支持
                "streetsidesoftware.code-spell-checker" // 拼写检查
            ],
            // VSCode 设置
            "settings": {
                "terminal.integrated.defaultProfile.linux": "bash", // 默认终端为 bash
                "terminal.integrated.profiles.linux": {
                    "bash": {
                        "path": "/bin/bash" // bash 路径
                    }
                },
                "python.defaultInterpreterPath": "/usr/bin/python3", // Python 解释器路径
                "python.linting.enabled": true, // 启用 Python 代码检查
                "python.linting.pylintEnabled": true, // 启用 pylint
                "editor.formatOnSave": true, // 保存时自动格式化
                "cmake.configureOnOpen": true // 打开时自动 CMake 配置
            }
        }
    }
}
