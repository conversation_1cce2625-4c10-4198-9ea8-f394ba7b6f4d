version: 2.1

_commands:
  common_commands: &common_commands
    ccache_stats:
      description: "CCache Stats"
      parameters:
        workspace:
          type: string
        when:
          type: string
          default: on_success
      steps:
        - run:
            name: CCache Stats
            working_directory: << parameters.workspace >>
            environment:
              CCACHE_DIR: << parameters.workspace >>/.ccache
            command: |
              ccache -s # show stats
              ccache -z # zero stats
              ccache -V # show version
              ccache -p # show config
            when: << parameters.when >>
    restore_from_cache:
      description: "Restore From Cache"
      parameters:
        key:
          type: string
        workspace:
          type: string
      steps:
        - restore_cache:
            name: Restore Cache << parameters.key >>
            keys:
              - "<< parameters.key >>-v33\
                -{{ arch }}\
                -{{ .Branch }}\
                -{{ .Environment.CIRCLE_PR_NUMBER }}\
                -{{ checksum  \"<< parameters.workspace >>/lockfile.txt\" }}"
              - "<< parameters.key >>-v33\
                -{{ arch }}\
                -main\
                -<no value>\
                -{{ checksum  \"<< parameters.workspace >>/lockfile.txt\" }}"
    save_to_cache:
      description: "Save To Cache"
      parameters:
        key:
          type: string
        workspace:
          type: string
        path:
          type: string
        when:
          type: string
          default: on_success
      steps:
        - save_cache:
            name: Save Cache << parameters.key >>
            key: "<< parameters.key >>-v33\
              -{{ arch }}\
              -{{ .Branch }}\
              -{{ .Environment.CIRCLE_PR_NUMBER }}\
              -{{ checksum  \"<< parameters.workspace >>/lockfile.txt\" }}\
              -{{ epoch }}"
            paths:
              - << parameters.path >>/.ccache
              - << parameters.path >>/build
              - << parameters.path >>/install
              - << parameters.path >>/log
              - << parameters.path >>/test_results
            when: << parameters.when >>
    install_dependencies:
      description: "Install Dependencies"
      parameters:
        underlay:
          type: string
        workspace:
          type: string
      steps:
        - run:
            name: Install Dependencies | << parameters.workspace >>
            working_directory: << parameters.workspace >>
            command: |
              . << parameters.underlay >>/install/setup.sh
              AMENT_PREFIX_PATH=$(echo "$AMENT_PREFIX_PATH" | \
                sed -e 's|:/opt/ros/'$ROS_DISTRO'$||')
              if [ "$AMENT_PREFIX_PATH" == "/opt/ros/$ROS_DISTRO" ]
              then
                unset AMENT_PREFIX_PATH
              fi

              cat << parameters.underlay >>/lockfile.txt > lockfile.txt
              vcs export --exact << parameters.underlay >>/src | \
                (echo vcs_export && cat) >> lockfile.txt
              sha256sum $PWD/lockfile.txt >> lockfile.txt

              apt-get update
              rosdep update --rosdistro $ROS_DISTRO
              dependencies=$(
                rosdep install -q -y \
                  --from-paths src \
                  --ignore-src \
                  --skip-keys " \
                    slam_toolbox \
                    " \
                  --verbose | \
                awk '$1 ~ /^resolution\:/' | \
                awk -F'[][]' '{print $2}' | \
                tr -d \, | xargs -n1 | sort -u | xargs)
              dpkg --list dpkg $dependencies | \
                (echo workspace_dependencies && cat) >> lockfile.txt
              sha256sum $PWD/lockfile.txt >> lockfile.txt
    setup_workspace:
      description: "Setup Workspace"
      parameters:
        underlay:
          type: string
        key:
          type: string
        workspace:
          type: string
        mixins:
          type: string
        packages_skip_regex:
          default: ""
          type: string
        restore:
          default: true
          type: boolean
        build:
          default: true
          type: boolean
      steps:
        - store_artifacts:
            path: << parameters.workspace >>/lockfile.txt
        - when:
            condition: << parameters.restore >>
            steps:
              - restore_from_cache:
                  key: << parameters.key >>
                  workspace: << parameters.workspace >>
        - when:
            condition: << parameters.build >>
            steps:
              - ccache_stats:
                  workspace: << parameters.workspace >>
                  when: always
              - run:
                  name: Build Workspace | << parameters.workspace >>
                  working_directory: << parameters.workspace >>
                  environment:
                    CCACHE_DIR: << parameters.workspace >>/.ccache
                  command: |
                    colcon cache lock

                    BUILD_UNFINISHED=$(
                      colcon list \
                        --names-only \
                        --packages-skip-build-finished \
                      | xargs)
                    echo BUILD_UNFINISHED: $BUILD_UNFINISHED

                    BUILD_FAILED=$(
                      colcon list \
                        --names-only \
                        --packages-select-build-failed \
                      | xargs)
                    echo BUILD_FAILED: $BUILD_FAILED

                    BUILD_INVALID=$(
                      colcon list \
                        --names-only \
                        --packages-select-cache-invalid \
                        --packages-select-cache-key build \
                      | xargs)
                    echo BUILD_INVALID: $BUILD_INVALID

                    BUILD_PACKAGES=""
                    if [ -n "$BUILD_UNFINISHED" ] || \
                       [ -n "$BUILD_FAILED" ] || \
                       [ -n "$BUILD_INVALID" ]
                    then
                      BUILD_PACKAGES=$(
                        colcon list \
                          --names-only \
                          --packages-skip-regex << parameters.packages_skip_regex >> \
                          --packages-above \
                            $BUILD_UNFINISHED \
                            $BUILD_FAILED \
                            $BUILD_INVALID \
                        | xargs)
                    fi
                    echo BUILD_PACKAGES: $BUILD_PACKAGES

                    colcon clean packages --yes \
                      --packages-select ${BUILD_PACKAGES} \
                      --base-select install

                    . << parameters.underlay >>/install/setup.sh
                    colcon build \
                      --packages-select ${BUILD_PACKAGES} \
                      --mixin << parameters.mixins >>
              - ccache_stats:
                  workspace: << parameters.workspace >>
                  when: always
              - save_to_cache:
                  key: << parameters.key >>
                  path: << parameters.workspace >>
                  workspace: << parameters.workspace >>
                  when: always
              - run:
                  name: Copy Build Logs
                  working_directory: << parameters.workspace >>
                  command: cp -rH log/latest_build log/build
                  when: always
              - store_artifacts:
                    path: << parameters.workspace >>/log/build
    test_workspace:
      description: "Test Workspace"
      parameters:
        key:
          type: string
        workspace:
          type: string
        cache_test:
          type: boolean
        packages_skip_regex:
          type: string
          default: ""
      steps:
        - run:
            name: Test Workspace | << parameters.workspace >>
            working_directory: << parameters.workspace >>
            command: |
              TEST_UNPASSED=$(
                colcon list \
                  --names-only \
                  --packages-skip-test-passed \
                | xargs)
              echo TEST_UNPASSED: $TEST_UNPASSED

              TEST_FAILURES=$(
                colcon list \
                  --names-only \
                  --packages-select-test-failures \
                | xargs)
              echo TEST_FAILURES: $TEST_FAILURES

              TEST_INVALID=$(
                colcon list \
                  --names-only \
                  --packages-select-cache-invalid \
                  --packages-select-cache-key test \
                | xargs)
              echo TEST_INVALID: $TEST_INVALID

              TEST_PACKAGES=""
              if [ -n "$TEST_UNPASSED" ] || \
                 [ -n "$TEST_FAILURES" ] || \
                 [ -n "$TEST_INVALID" ]
              then
                TEST_PACKAGES=$(
                  colcon list \
                    --names-only \
                    --packages-skip-regex << parameters.packages_skip_regex >> \
                    --packages-above \
                      $TEST_UNPASSED \
                      $TEST_FAILURES \
                      $TEST_INVALID)
              fi
              if ( ! << parameters.cache_test >> )
              then
                TEST_PACKAGES=$(
                  colcon list \
                    --names-only \
                    --packages-skip-regex << parameters.packages_skip_regex >> )
              fi
              TEST_PACKAGES=$(
                echo $TEST_PACKAGES \
                | circleci tests split \
                  --split-by=timings \
                  --timings-type=classname \
                  --show-counts \
                | xargs)
              echo TEST_PACKAGES: $TEST_PACKAGES

              colcon clean packages --yes \
                --packages-select ${TEST_PACKAGES} \
                --base-select test_result
              colcon clean packages --yes \
                --packages-select ${TEST_PACKAGES} \
                --base-select build \
                --clean-match \
                  "*.gcda"

              . install/setup.sh
              set -o xtrace
              colcon test \
                --packages-select ${TEST_PACKAGES}
              colcon test-result \
                --verbose
        - when:
            condition: << parameters.cache_test >>
            steps:
              - save_to_cache:
                  key: << parameters.key >>
                  path: << parameters.workspace >>
                  workspace: << parameters.workspace >>
                  when: always
        - run:
            name: Copy Test Logs
            working_directory: << parameters.workspace >>
            command: cp -rH log/latest_test log/test
            when: always
        - store_artifacts:
            path: << parameters.workspace >>/log/test
        - store_artifacts:
            path: << parameters.workspace >>/test_results
        - run:
            name: Prepare Test Results
            working_directory: << parameters.workspace >>
            command: find test_results -name "Test.xml" -type f -delete
            when: always
        - store_test_results:
            path: << parameters.workspace >>/test_results

_steps:
  pre_checkout: &pre_checkout
    run:
      name: Pre Checkout
      command: |
        mkdir -p $ROS_WS/src && cd $ROS_WS
        ln -s /opt/ros/$ROS_DISTRO install

        echo $CACHE_NONCE | \
          (echo cache_nonce && cat) >> lockfile.txt
        sha256sum $PWD/lockfile.txt >> lockfile.txt

        TZ=utc stat -c '%y' /ros_entrypoint.sh | \
          (echo ros_entrypoint && cat) >> lockfile.txt
        sha256sum $PWD/lockfile.txt >> lockfile.txt

        rm -rf $OVERLAY_WS/*
  on_checkout: &on_checkout
    checkout:
      path: src/navigation2
  post_checkout: &post_checkout
    run:
      name: Post Checkout
      command: |
        cp $OVERLAY_WS/src/navigation2/.circleci/defaults.yaml $COLCON_DEFAULTS_FILE
        if ! cmp \
          $OVERLAY_WS/src/navigation2/tools/underlay.repos \
          $UNDERLAY_WS/underlay.repos >/dev/null 2>&1
        then
          echo "Importing Underlay"
          cp $OVERLAY_WS/src/navigation2/tools/underlay.repos \
            $UNDERLAY_WS/underlay.repos
          vcs import $UNDERLAY_WS/src \
            < $UNDERLAY_WS/underlay.repos
        fi
  install_underlay_dependencies: &install_underlay_dependencies
    install_dependencies:
      underlay: /opt/ros_ws
      workspace: /opt/underlay_ws
  setup_underlay_workspace: &setup_underlay_workspace
    setup_workspace: &setup_workspace_underlay
      key: underlay_ws
      underlay: /opt/ros_ws
      workspace: /opt/underlay_ws
      mixins: ${UNDERLAY_MIXINS}
  restore_underlay_workspace: &restore_underlay_workspace
    setup_workspace:
      <<: *setup_workspace_underlay
      build: false
  install_overlay_dependencies: &install_overlay_dependencies
    install_dependencies:
      underlay: /opt/underlay_ws
      workspace: /opt/overlay_ws
  setup_overlay_workspace: &setup_overlay_workspace
    setup_workspace: &setup_workspace_overlay
      key: overlay_ws
      underlay: /opt/underlay_ws
      workspace: /opt/overlay_ws
      mixins: ${OVERLAY_MIXINS}
  build_workspace_overlay: &build_workspace_overlay
    setup_workspace:
      <<: *setup_workspace_overlay
      packages_skip_regex: << parameters.packages_skip_regex >>
  restore_overlay_workspace: &restore_overlay_workspace
    setup_workspace:
      <<: *setup_workspace_overlay
      build: false
  test_overlay_workspace: &test_overlay_workspace
    test_workspace:
      key: overlay_ws
      workspace: /opt/overlay_ws
      cache_test: << parameters.cache_test >>
      packages_skip_regex: << parameters.packages_skip_regex >>
  collect_overlay_coverage: &collect_overlay_coverage
    run:
      name: Collect Code Coverage
      working_directory: /opt/overlay_ws
      command: src/navigation2/tools/code_coverage_report.bash ci
      when: always
  upload_overlay_coverage: &upload_overlay_coverage
    run:
      name: Upload Code Coverage
      working_directory: /opt/overlay_ws
      command: |
        curl -s https://codecov.io/bash > codecov
        codecov_version=$(grep -o 'VERSION=\"[0-9\.]*\"' codecov | cut -d'"' -f2)
        shasum -a 512 -c <(curl -s "https://raw.githubusercontent.com/codecov/codecov-bash/${codecov_version}/SHA512SUM" | grep -w "codecov")
        bash codecov \
          -f "lcov/total_coverage.info" \
          -R "src/navigation2" \
          -n "$RMW_IMPLEMENTATION" \
          -Z || echo 'Codecov upload failed'
      when: always

commands:
  <<: *common_commands
  checkout_source:
    description: "Checkout Source"
    steps:
      - *pre_checkout
      - *on_checkout
      - *post_checkout
  setup_dependencies:
    description: "Setup Dependencies"
    steps:
      - *install_underlay_dependencies
      - *setup_underlay_workspace
      - *install_overlay_dependencies
  build_source:
    description: "Build Source"
    parameters:
      packages_skip_regex:
        type: string
    steps:
      - *build_workspace_overlay
  restore_build:
    description: "Restore Build"
    steps:
      - checkout_source
      - *install_underlay_dependencies
      - *restore_underlay_workspace
      - *install_overlay_dependencies
      - *restore_overlay_workspace
  test_build:
    description: "Test Build"
    parameters:
      cache_test:
        type: boolean
      packages_skip_regex:
        type: string
    steps:
      - *test_overlay_workspace
  report_coverage:
    description: "Report Coverage"
    steps:
      - *collect_overlay_coverage
      - *upload_overlay_coverage

_environments:
  common_environment: &common_environment
    ROS_WS: "/opt/ros_ws"
    UNDERLAY_WS: "/opt/underlay_ws"
    OVERLAY_WS: "/opt/overlay_ws"
    UNDERLAY_MIXINS: "release ccache lld"
    CCACHE_LOGFILE: "/tmp/ccache.log"
    CCACHE_MAXSIZE: "200M"
    MAKEFLAGS: "-j 2 -l 2 "
    COLCON_DEFAULTS_FILE: "/tmp/defaults.yaml"
    RCUTILS_LOGGING_BUFFERED_STREAM: "0"
    RCUTILS_LOGGING_USE_STDOUT: "0"
    DEBIAN_FRONTEND: "noninteractive"
    PYTHONUNBUFFERED: "1"

executors:
  release_exec:
    docker:
      - image: ghcr.io/ros-navigation/navigation2:main
    resource_class: large
    working_directory: /opt/overlay_ws
    environment:
      <<: *common_environment
      CACHE_NONCE: "Release"
      OVERLAY_MIXINS: "release ccache coverage-gcc lld"

_jobs:
  job_build: &job_build
    parameters: &job_build_parameters
      packages_skip_regex:
        type: string
        default: ""
  job_test: &job_test
    parameters:
      <<: *job_build_parameters
      cache_test:
        type: boolean
        default: false
      rmw:
        default: "rmw_cyclonedds_cpp"
        type: string
    parallelism: 1
    environment:
      RMW_IMPLEMENTATION: << parameters.rmw >>

jobs:
  release_build: &release_build
    <<: *job_build
    executor: release_exec
    steps:
      - checkout_source
      - setup_dependencies
      - build_source:
          packages_skip_regex: << parameters.packages_skip_regex >>
  system_build: &system_build
    <<: *release_build
  release_test: &release_test
    <<: *job_test
    executor: release_exec
    steps:
      - restore_build
      - test_build:
          cache_test: << parameters.cache_test >>
          packages_skip_regex: << parameters.packages_skip_regex >>
      - report_coverage

_parameters:
  release_parameters: &release_parameters
    packages_skip_regex: "nav2_system_tests"

workflows:
  version: 2
  build_and_test:
    jobs:
      - release_build:
          <<: *release_parameters
      - system_build:
          requires:
            - release_build
      - release_test:
          requires:
            - system_build
          cache_test: true
  nightly:
    jobs:
      - release_build:
          <<: *release_parameters
      - system_build:
          requires:
            - release_build
      - release_test:
          requires:
            - system_build
          <<: *release_parameters
          matrix:
            alias: release_test
            parameters:
              rmw:
                - rmw_cyclonedds_cpp
                - rmw_fastrtps_cpp
    triggers:
      - schedule:
          cron: "0 13 * * *"
          filters:
            branches:
              only:
                - main
